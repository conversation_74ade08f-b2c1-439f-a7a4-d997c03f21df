--!strict

--[[
	- file: TBRDS_Client.luau
  
	- version: 2.1.0
	- author: BleckWolf25
	- contributors:
  
	- copyright: Dynamic Innovative Studio
  
	- description:
		- Client-side initialization of TBRDS
		- No client-side control, everything is handled server-side
]]

-- =============================================================================
-- SERVICES
-- =============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- =============================================================================
-- PATHS
-- =============================================================================
local tbrdsFolder = ReplicatedStorage.TBRDS
local sharedFolder = tbrdsFolder.Shared

-- =============================================================================
-- MODULES
-- =============================================================================
local Types = require(tbrdsFolder.TBRDS_Types)
local Utils = require(sharedFolder.TBRDS_Utils)

-- =============================================================================
-- CONSTANTS
-- =============================================================================
local TAG: string = "Client"

-- =============================================================================
-- VARIABLES
-- =============================================================================

-- Player
local LocalPlayer = Players.LocalPlayer :: Player

-- Path
local Remotes = ReplicatedStorage.TBRDS:WaitForChild("Remotes")

-- Events
local TagRemote = Remotes:WaitForChild(Types.TBRDSConstants.REMOTE_NAMES.TagUpdate)
local TagRequestRemote = Remotes:WaitForChild("TagRequestRemote")
local tagRemoteEvent = if TagRemote:IsA("RemoteEvent") then TagRemote else nil

local tagRequestRemoteEvent = if TagRequestRemote:IsA("RemoteEvent") then TagRequestRemote else nil

-- =============================================================================
-- EVENTS
-- =============================================================================

-- Handle tag updates from server
if tagRemoteEvent then
  tagRemoteEvent.OnClientEvent:Connect(
    function(player: Player, tag: Types.RoleName, _style: Types.RoleStyle)
      if player == LocalPlayer then
        return
      end
      Utils.log(TAG, "Received tag update for " .. player.Name .. ": " .. tag)
    end
  )
end

-- Request our tag when character loads
if LocalPlayer then
  LocalPlayer.CharacterAdded:Connect(function()
    if tagRequestRemoteEvent then
      tagRequestRemoteEvent:FireServer()
      Utils.log(TAG, "Requested tag from server")
    end
  end)
end

-- Request tag when we join
if LocalPlayer and LocalPlayer.Character then
  if tagRequestRemoteEvent then
    tagRequestRemoteEvent:FireServer()
    Utils.log(TAG, "Requested tag from server (initial)")
  end
end
