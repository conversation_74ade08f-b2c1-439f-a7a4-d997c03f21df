--!strict

--[[
    - file: CGS_IS_Client_Initialization.luau

    - version: 1.0.0
    - author: Silver
    - contributors: BleckWolf25

    - copyright: Dynamic Innovative Studio

    - description:
      - Core Game System (CGS) Interaction Sub-System initializer
      - Initializes all interaction features of the sub-system
      - Features:
        - Proximity Highlight
        - Custom Proximity Prompt UI & UX
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ProximityPromptService = game:GetService("ProximityPromptService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- PATHS
-- ============================================================================
local CGS_Folder = ReplicatedStorage:WaitForChild("CGS")
local SharedFolder = CGS_Folder:WaitForChild("Shared")

-- ============================================================================
-- MODULES
-- ============================================================================
local ProximityModule = require(SharedFolder:WaitForChild("CGS_Proximity"))

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Show Prompt
ProximityPromptService.PromptShown:Connect(function(prompt: ProximityPrompt)
  ProximityModule.HighLightCreate(prompt)
end)

-- Hide Prompt
ProximityPromptService.PromptHidden:Connect(function(prompt: ProximityPrompt)
  ProximityModule.HighLightDelete(prompt)
end)

-- Promp Hold Began
ProximityPromptService.PromptButtonHoldBegan:Connect(function(prompt: ProximityPrompt)
  ProximityModule.HighLightChangeColor(prompt, Color3.fromRGB(255, 71, 38))
end)

-- Prompt Hold Ended
ProximityPromptService.PromptButtonHoldEnded:Connect(function(prompt: ProximityPrompt)
  ProximityModule.HighLightChangeColor(prompt, Color3.fromRGB(255, 255, 255))
end)
