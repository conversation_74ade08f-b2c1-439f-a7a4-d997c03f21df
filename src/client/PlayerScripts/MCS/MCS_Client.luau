--!strict

--[[
    - file: MCS_Client.luau

    - version: 2.0.2
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Initializes all client-side components for the Modular Command System (MCS),
	  including UI, command parsing, autocomplete, and remote event handling.
	  - Delegates validation and execution to the server-side logic.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService: UserInputService = game:GetService("UserInputService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

local AutocompleteService = require(script.Parent.Core:WaitForChild("MCS_AutocompleteService"))
local CommandParser = require(script.Parent.Core:WaitForChild("MCS_CommandParser"))
local Console = require(script.Parent.UI:WaitForChild("MCS_Console_UI"))
local FeedbackDisplay = require(script.Parent.UI:WaitForChild("MCS_Feedback_UI"))

-- ============================================================================
-- REMOTES
-- ============================================================================
local remotes = ReplicatedStorage:WaitForChild("MCS"):WaitForChild("Remotes")
local commandRemote = remotes:WaitForChild("CommandRemote") :: RemoteEvent
local autocompleteRemote = remotes:WaitForChild("AutocompleteRemote") :: RemoteFunction
local checkConsolePermission = remotes:WaitForChild("CheckConsolePermission") :: RemoteFunction

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local TAG: string = "Client"

-- ============================================================================
-- INITIALIZATION
-- ============================================================================
Console.init()
FeedbackDisplay.init()
AutocompleteService.init(autocompleteRemote)

-- Preload autocomplete suggestions asynchronously
task.spawn(function()
  AutocompleteService.getSuggestions(Configuration.Commands.PREFIX)
end)

-- ============================================================================
-- COMMAND HANDLING
-- ============================================================================

--[[
	Processes raw console input.
	- Sanitizes text.
	- Validates command format.
	- Sends valid command to the server.
	- Displays feedback if invalid.
]]
local function handleCommandInput(text: string): ()
  Utils.print(TAG, "Handling command input: " .. text)
  Utils.startTimer(TAG, "handleCommandInput")

  local sanitizedText = Utils.sanitize(text)
  if sanitizedText ~= text then
    Utils.print(TAG, "Sanitized input due to invalid characters")
  end

  if not CommandParser.isCommand(sanitizedText) then
    Utils.print(TAG, "Not a valid command")
    Utils.endTimer(TAG, "handleCommandInput")
    return
  end

  local commandRequest = CommandParser.parse(sanitizedText)
  if not commandRequest then
    Utils.print(TAG, "Command parse failed")
    FeedbackDisplay.showFeedback(false, "Invalid command format")
    Utils.endTimer(TAG, "handleCommandInput")
    return
  end

  local success = pcall(function()
    commandRemote:FireServer(commandRequest)
  end)

  if success then
    Utils.print(TAG, "Command sent: " .. commandRequest.commandName)
  else
    Utils.print(TAG, "Failed to send command")
    FeedbackDisplay.showFeedback(false, "Failed to send command to server")
  end

  Utils.endTimer(TAG, "handleCommandInput")
end

-- Connect console UI to command input handler
Console.onCommandSubmitted.Event:Connect(handleCommandInput)

-- ============================================================================
-- INPUT BINDING (CONSOLE TOGGLE)
-- ============================================================================

--[[
	Shows the console when F2 is pressed, if the player has permission.
]]
local function onInputBegan(input: InputObject, gameProcessed: boolean): ()
  if gameProcessed or input.KeyCode ~= Enum.KeyCode.F2 then
    return
  end

  local ok, hasPermission = pcall(checkConsolePermission.InvokeServer, checkConsolePermission)
  if ok and hasPermission then
    Console.show()
    Utils.print(TAG, "Console opened")
  else
    Utils.print(TAG, "Permission denied")
    FeedbackDisplay.showFeedback(false, "You don't have permission to open the console")
  end
end

UserInputService.InputBegan:Connect(onInputBegan)

-- ============================================================================
-- SERVER RESPONSE HANDLER
-- ============================================================================

--[[
	Handles responses from the server after a command is executed.
	Validates format and displays success/failure feedback.
]]
commandRemote.OnClientEvent:Connect(function(response: Types.CommandResponse)
  if type(response) ~= "table" then
    Utils.print(TAG, "Invalid response (not a table): " .. tostring(response))
    FeedbackDisplay.showFeedback(false, "Invalid server response")
    return
  end

  if type(response.success) ~= "boolean" then
    Utils.print(TAG, "Missing success field: " .. tostring(response.success))
    FeedbackDisplay.showFeedback(false, "Invalid server response format")
    return
  end

  local message = typeof(response.message) == "string" and response.message
    or (response.success and "Command executed successfully" or "Command failed")

  Utils.print(TAG, "Command result: " .. tostring(response.success))
  FeedbackDisplay.showFeedback(response.success, message)
end)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local MCSClient = {
  handleCommandInput = handleCommandInput,
}
Utils.print(TAG, "MCS Client initialized")

-- ============================================================================
-- EXPORTS
-- ============================================================================
return MCSClient
