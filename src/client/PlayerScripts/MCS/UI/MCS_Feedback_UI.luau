--!strict

--[[
    - file: MCS_Feedback_UI.luau

    - version: 2.1.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Provides feedback notification functionality for the Modular Command System (MCS)
      - Displays success or error messages with modern terminal-inspired UI
      - Supports dynamic sizing, smooth animations, and consistent styling
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TextService: TextService = game:GetService("TextService")
local TweenService: TweenService = game:GetService("TweenService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================

-- UI fallback values
local UI_DEFAULTS = {
  BACKGROUND = Color3.fromRGB(30, 30, 35),
  BORDER = Color3.fromRGB(240, 240, 230),
  SUCCESS_TEXT = Color3.fromRGB(80, 250, 123),
  ERROR_TEXT = Color3.fromRGB(255, 85, 85),
  TEXT = Color3.fromRGB(248, 248, 242),
  CORNER_RADIUS = UDim.new(0, 6),
  ANIMATION_TIME = 0.3,
  FADE_IN_TIME = 0.4,
  FADE_OUT_TIME = 0.6,
  SCALE_TIME = 0.35,
  DISPLAY_TIME = 4,
  MIN_WIDTH = 250,
  MAX_WIDTH = 600,
  HEIGHT = 40,
  PADDING_X = 20,
  PADDING_Y = 10,
} :: Types.UI_ConstantsType

local UI_THEME: Types.UI_ConstantsType = Configuration.UI or UI_DEFAULTS

-- Animation constants
local ANIMATION = {
  FADE_IN_TIME = UI_THEME.FADE_IN_TIME,
  DISPLAY_TIME = UI_THEME.DISPLAY_TIME,
  FADE_OUT_TIME = UI_THEME.FADE_OUT_TIME,
  SCALE_TIME = UI_THEME.SCALE_TIME,
  MIN_WIDTH = UI_THEME.MIN_WIDTH,
  MAX_WIDTH = UI_THEME.MAX_WIDTH,
  HEIGHT = UI_THEME.HEIGHT,
  PADDING_X = UI_THEME.PADDING_X,
  PADDING_Y = UI_THEME.PADDING_Y,
}

local MODULE_NAME = "FeedbackDisplay" :: string

-- ============================================================================
-- VARIABLES
-- ============================================================================
-- Player references
local player: Player = Players.LocalPlayer or Players:WaitForChild("LocalPlayer") :: Player
local playerGui: PlayerGui = player:WaitForChild("PlayerGui") :: PlayerGui

-- UI references
local backgroundFrame: Frame?
local feedbackLabel: TextLabel?
local statusIndicator: Frame?
local currentAnimation: Tween? -- Track current animation tween

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local FeedbackDisplay = {}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Initialize FeedbackDisplay
function FeedbackDisplay.init()
  Utils.print(MODULE_NAME, "Initializing...")

  -- Reuse or create ScreenGui
  local gui: ScreenGui = (
    playerGui:FindFirstChild("MCS_Console") or Instance.new("ScreenGui")
  ) :: ScreenGui

  if not gui.Parent then
    gui.Name = "MCS_Console"
    gui.ResetOnSpawn = false
    gui.Parent = playerGui
  end

  -- Create container frame
  backgroundFrame = Instance.new("Frame")
  if not backgroundFrame then
    Utils.warn("Failed to create background frame for FeedbackDisplay")
    return
  end
  backgroundFrame.Name = "FeedbackContainer"
  backgroundFrame.Size = UDim2.new(0, ANIMATION.MIN_WIDTH, 0, ANIMATION.HEIGHT)
  backgroundFrame.Position = UDim2.new(0.5, -ANIMATION.MIN_WIDTH / 2, 0.05, 0)
  backgroundFrame.BackgroundColor3 = UI_THEME.BACKGROUND
  backgroundFrame.BorderColor3 = UI_THEME.BORDER
  backgroundFrame.BorderSizePixel = 1
  backgroundFrame.AnchorPoint = Vector2.new(0.5, 0)
  backgroundFrame.BackgroundTransparency = 1
  backgroundFrame.Visible = false

  -- Add rounded corners
  local uiCorner = Instance.new("UICorner")
  if not uiCorner then
    Utils.warn("Failed to create uiCorner UICorner for FeedbackDisplay")
    return
  end
  uiCorner.CornerRadius = UI_THEME.CORNER_RADIUS
  uiCorner.Parent = backgroundFrame

  -- Add drop shadow
  local uiStroke = Instance.new("UIStroke")
  if not uiStroke then
    Utils.warn("Failed to create uiStroke UIStroke for FeedbackDisplay")
    return
  end
  uiStroke.Color = UI_THEME.BORDER
  uiStroke.Transparency = 0.7
  uiStroke.Thickness = 1
  uiStroke.Parent = backgroundFrame

  -- Create TextLabel for feedback
  feedbackLabel = Instance.new("TextLabel")
  if not feedbackLabel then
    Utils.warn("Failed to create feedbackLabel TextLabel for FeedbackDisplay")
    return
  end
  feedbackLabel.Name = "Feedback"
  feedbackLabel.Size = UDim2.new(1, -ANIMATION.PADDING_X, 1, 0)
  feedbackLabel.Position = UDim2.new(0, ANIMATION.PADDING_X / 2, 0, 0)
  feedbackLabel.BackgroundTransparency = 1
  feedbackLabel.TextColor3 = UI_THEME.TEXT
  feedbackLabel.TextSize = 16
  feedbackLabel.TextXAlignment = Enum.TextXAlignment.Left
  feedbackLabel.TextYAlignment = Enum.TextYAlignment.Center
  feedbackLabel.TextWrapped = true
  feedbackLabel.Text = ""
  feedbackLabel.Parent = backgroundFrame

  -- Add status indicator
  statusIndicator = Instance.new("Frame")
  if not statusIndicator then
    Utils.warn("Failed to create statusIndicator Frame for FeedbackDisplay")
    return
  end
  statusIndicator.Name = "StatusIndicator"
  statusIndicator.Size = UDim2.new(0, 4, 1, -10)
  statusIndicator.Position = UDim2.new(0, 3, 0, 5)
  statusIndicator.BackgroundColor3 = UI_THEME.SUCCESS_TEXT
  statusIndicator.BorderSizePixel = 0
  statusIndicator.Visible = false

  local indicatorCorner = Instance.new("UICorner")
  if not indicatorCorner then
    Utils.warn("Failed to create indicatorCorner UICorner for FeedbackDisplay")
    return
  end
  indicatorCorner.CornerRadius = UDim.new(1, 0)
  indicatorCorner.Parent = statusIndicator

  statusIndicator.Parent = backgroundFrame

  backgroundFrame.Parent = gui
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Calculate optimal size based on text content
function FeedbackDisplay.calculateOptimalSize(message: string): UDim2
  local textParams = Instance.new("GetTextBoundsParams")
  textParams.Text = Utils.sanitize(message)
  textParams.Size = 16
  textParams.Width = ANIMATION.MAX_WIDTH - ANIMATION.PADDING_X

  local textBounds = TextService:GetTextBoundsAsync(textParams)

  local width =
    math.clamp(textBounds.X + ANIMATION.PADDING_X, ANIMATION.MIN_WIDTH, ANIMATION.MAX_WIDTH)
  local height = ANIMATION.HEIGHT
  if textBounds.X > (ANIMATION.MAX_WIDTH - ANIMATION.PADDING_X) then
    height = math.max(ANIMATION.HEIGHT, textBounds.Y + ANIMATION.PADDING_Y)
  end

  return UDim2.new(0, width, 0, height)
end

-- Cancel existing animations
function FeedbackDisplay.cancelAnimations()
  if currentAnimation then
    currentAnimation:Cancel()
    currentAnimation = nil
  end
end

-- Show Feedback with animations
function FeedbackDisplay.showFeedback(success: boolean, message: string)
  -- Ensure required UI elements exist
  assert(backgroundFrame, "FeedbackDisplay: backgroundFrame is not initialized")
  assert(feedbackLabel, "FeedbackDisplay: feedbackLabel is not initialized")
  assert(statusIndicator, "FeedbackDisplay: statusIndicator is not initialized")

  FeedbackDisplay.cancelAnimations()

  -- Configure status indicator
  statusIndicator.BackgroundColor3 = success and UI_THEME.SUCCESS_TEXT or UI_THEME.ERROR_TEXT
  statusIndicator.Visible = true

  -- Update text and color
  feedbackLabel.Text = Utils.sanitize(message)
  feedbackLabel.TextColor3 = success and UI_THEME.SUCCESS_TEXT or UI_THEME.ERROR_TEXT

  -- Calculate optimal size
  local targetSize = FeedbackDisplay.calculateOptimalSize(message)
  local newXOffset = -targetSize.X.Offset / 2

  -- Set up initial state
  backgroundFrame.Size = UDim2.new(0, 0, 0, targetSize.Y.Offset)
  backgroundFrame.Position = UDim2.new(0.5, 0, 0.05, 0)
  backgroundFrame.BackgroundTransparency = 0.5
  backgroundFrame.Visible = true

  -- Create and play entrance animations
  local expandTween = TweenService:Create(
    backgroundFrame,
    TweenInfo.new(ANIMATION.SCALE_TIME, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
    {
      Size = targetSize,
      Position = UDim2.new(0.5, newXOffset, 0.05, 0),
    }
  )

  local fadeTween = TweenService:Create(
    backgroundFrame,
    TweenInfo.new(ANIMATION.FADE_IN_TIME, Enum.EasingStyle.Sine, Enum.EasingDirection.Out),
    { BackgroundTransparency = 0 }
  )

  currentAnimation = expandTween
  expandTween:Play()

  expandTween.Completed:Connect(function()
    currentAnimation = fadeTween
    fadeTween:Play()
  end)

  -- Schedule auto-hide
  task.delay(ANIMATION.DISPLAY_TIME, function()
    FeedbackDisplay.hideFeedback()
  end)
end

-- Hide Feedback with animations
function FeedbackDisplay.hideFeedback()
  if not backgroundFrame or not backgroundFrame.Visible then
    return
  end

  FeedbackDisplay.cancelAnimations()

  local currentSize = backgroundFrame.Size

  local fadeTween = TweenService:Create(
    backgroundFrame,
    TweenInfo.new(ANIMATION.FADE_OUT_TIME, Enum.EasingStyle.Sine, Enum.EasingDirection.In),
    { BackgroundTransparency = 1 }
  )

  local shrinkTween = TweenService:Create(
    backgroundFrame,
    TweenInfo.new(ANIMATION.SCALE_TIME, Enum.EasingStyle.Back, Enum.EasingDirection.In),
    {
      Size = UDim2.new(0, 0, 0, currentSize.Y.Offset),
      Position = UDim2.new(0.5, 0, 0.05, 0),
    }
  )

  currentAnimation = fadeTween
  fadeTween:Play()

  fadeTween.Completed:Connect(function()
    currentAnimation = shrinkTween
    shrinkTween:Play()
    shrinkTween.Completed:Connect(function()
      if backgroundFrame then
        backgroundFrame.Visible = false
      end
    end)
  end)
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return FeedbackDisplay
