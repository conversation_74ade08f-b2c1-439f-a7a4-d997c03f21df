--!strict

--[[
    - file: MCS_CommandParser.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Client-side command parsing for the Modular Command System (MCS)
      - Tokenizes and formats commands before sending to the server
      - Performs minimal validation to optimize performance
      - Relies on server-side for validation
      - Ensures security through input sanitization
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local MODULE_NAME = "MCS_CommandParser"

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local CommandParser = {}

-- ============================================================================
-- PUBLIC FUNCTIONS
-- ============================================================================

-- Check if text is a command (starts with prefix)
function CommandParser.isCommand(text: string): boolean
  if typeof(text) ~= "string" then
    return false
  end

  -- Explicitly capture first return value
  local hasPrefix: boolean = Utils.hasPrefix(text, Configuration.Commands.PREFIX)
  return hasPrefix
end

-- Parse command text into name and arguments
function CommandParser.parse(text: string): Types.CommandRequest?
  Utils.print(MODULE_NAME, "Parsing command: " .. text)
  Utils.startTimer("parse")

  -- Type guard for non-string input
  if typeof(text) ~= "string" then
    Utils.endTimer("CommandParser", "parse")
    return nil
  end

  if not CommandParser.isCommand(text) then
    Utils.print(MODULE_NAME, "Text is not a command")
    Utils.endTimer("CommandParser", "parse")
    return nil
  end

  -- Sanitize input
  local sanitizedText = Utils.sanitize(text)

  -- Strip prefix
  local commandText = Utils.stripPrefix(sanitizedText, Configuration.Commands.PREFIX)
  if not commandText then
    Utils.print(MODULE_NAME, "Failed to strip prefix")
    Utils.endTimer("CommandParser", "parse")
    return nil
  end

  -- Check command length
  if #commandText > Configuration.Commands.MAX_COMMAND_LENGTH then
    Utils.print(MODULE_NAME, "Command exceeds maximum length")
    Utils.endTimer("CommandParser", "parse")
    return nil
  end

  -- Split into tokens
  local tokens = Utils.splitCommandText(commandText)
  if #tokens == 0 then
    Utils.print(MODULE_NAME, "No tokens found")
    Utils.endTimer("CommandParser", "parse")
    return nil
  end

  -- Extract command name and arguments
  local commandName = tokens[1]:lower()
  local args = #tokens > 1 and { unpack(tokens, 2) } or nil

  -- Limit arguments
  if args and #args > Configuration.Commands.MAX_ARGS then
    args = { unpack(args, 1, Configuration.Commands.MAX_ARGS) }
    Utils.print(MODULE_NAME, "Truncated arguments to max limit")
  end

  Utils.endTimer("CommandParser", "parse")

  -- Explicitly create CommandRequest object
  local request = {
    commandName = commandName,
    args = args,
  } :: Types.CommandRequest

  return request
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================
Utils.print(MODULE_NAME, "Module initialized")
return CommandParser
