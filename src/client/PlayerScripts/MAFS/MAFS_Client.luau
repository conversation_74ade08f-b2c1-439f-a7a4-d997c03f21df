--!strict

--[[
    - file: MAFS_Client.luau

    - version: 1.1.1
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Client-side implementation for the MAFS (Material Audio Footstep System).
      - Handles footstep sound playback, footprint creation, and sound synchronization.
      - Integrates with performance monitoring and centralized configuration.

    - dependencies:
      - MAFS_Types
      - MAFS_Utils
      - MAFS_Remotes
      - MAFS_Configuration

    - notes:
      - Manages local footstep sounds and replicated 3D sounds for other players.
      - Uses centralized utilities and configuration for consistency.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Debris: Debris = game:GetService("Debris")
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService: RunService = game:GetService("RunService")

-- ============================================================================
-- PATHS
-- ============================================================================
local mainPath = ReplicatedStorage:WaitForChild("MAFS")

-- ============================================================================
-- MODULES
-- ============================================================================
local Config = require(ReplicatedStorage.Configurations.Systems:WaitForChild("MAFS_Configuration"))
local Remotes = require(mainPath:WaitForChild("MAFS_Remotes"))
local Types = require(mainPath:WaitForChild("MAFS_Types"))
local Utils = require(mainPath.Shared:WaitForChild("MAFS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================

-- Name of the sounds resource folder
local SOUNDS_FOLDER_NAME: string = "Sounds"

-- Name of the FootPrints resource folder
local FOOTPRINTS_FOLDER_NAME: string = "FootPrints"

-- Logging tag for client context
local TAG: string = "Client"

-- ============================================================================
-- TYPES
-- ============================================================================
-- Configuration type alias for client module.
type MAFSConfiguration = Types.MAFSConfiguration

-- ============================================================================
-- VARIABLES
-- ============================================================================

-- Currently playing footstep sound
local currentFootstepSound: Sound? = nil

-- Part used for sound playback positioning
local soundPart: Part? = nil

-- Performance tracking interface
local performanceMonitor: Types.PerformanceMonitor

-- Resource folders
local soundsFolder: Folder? = nil
local footprintsFolder: Folder? = nil

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

--[[
  Initializes the resource folders from ReplicatedStorage.
  These folders hold sound templates and footprint models.

  @returns nil
]]
local function initializeResourceFolders(): ()
  local mafsFolder = Utils.safeWaitForChild(ReplicatedStorage, "MAFS")
  if not mafsFolder then
    Utils.warn(TAG, "Failed to find MAFS folder in ReplicatedStorage")
    return
  end

  local soundsInstance = Utils.safeWaitForChild(mafsFolder, SOUNDS_FOLDER_NAME)
  if soundsInstance and soundsInstance:IsA("Folder") then
    soundsFolder = soundsInstance :: Folder
  else
    Utils.warn(TAG, "Sounds folder not found or is not a Folder - footstep sounds will not work")
  end

  local footprintsInstance = Utils.safeWaitForChild(mafsFolder, FOOTPRINTS_FOLDER_NAME)
  if footprintsInstance and footprintsInstance:IsA("Folder") then
    footprintsFolder = footprintsInstance :: Folder
  else
    Utils.warn(TAG, "Footprints folder not found or is not a Folder - footprints will not work")
  end
end

--[[
  Returns the workspace folder where MAFS artifacts should be placed.

  @returns Folder? -- MAFS folder inside Workspace.Game or nil if not found
]]
local function getWorkspaceMAFSFolder(): Folder?
  local mafsFolder = game.Workspace.Game:WaitForChild("MAFS")
  if not mafsFolder or not mafsFolder:IsA("Folder") then
    Utils.warn(TAG, `MAFS folder not found at path {game.Workspace.Game:WaitForChild("MAFS")}`)
    return nil
  end
  return mafsFolder :: Folder
end

--[[
  Cleans up the current footstep sound and its associated part.

  @returns nil
]]
local function cleanupSound(): ()
  if currentFootstepSound then
    currentFootstepSound:Stop()
    Utils.safeDestroy(currentFootstepSound)
    currentFootstepSound = nil

    if performanceMonitor then
      performanceMonitor:recordSoundDestruction()
    end
  end

  if soundPart then
    Utils.safeDestroy(soundPart)
    soundPart = nil
  end
end

--[[
  Places a footprint model at the given world position.

  @params position		-- World-space position for footprint
  @params footprintName	-- Name of the footprint model to use

  @returns nil
]]
local function placeFootprint(position: Vector3, footprintName: string): ()
  if not Config.Config.enableFootprints then
    return
  end

  if not footprintsFolder then
    Utils.warn(TAG, "Footprints folder not available")
    return
  end

  local template = footprintsFolder:FindFirstChild(footprintName)
  if not template or not template:IsA("Model") then
    Utils.warn(`Invalid footprint template: {footprintName}`)
    return
  end

  if not template.PrimaryPart then
    Utils.warn(`No PrimaryPart in footprint model: {footprintName}`)
    return
  end

  local workspaceMAFS = getWorkspaceMAFSFolder()
  if not workspaceMAFS then
    return
  end

  local footprint = template:Clone()
  local offsetPosition = position + Config.Config.footprintOffset
  footprint:PivotTo(CFrame.new(offsetPosition))
  footprint.Parent = workspaceMAFS

  Debris:AddItem(footprint, Config.Config.footprintLifetime)

  if performanceMonitor then
    performanceMonitor:recordFootprintCreation()
  end

  Utils.log(`Placed footprint: {footprintName} at {offsetPosition}`)
end

--[[
  Creates a footstep sound instance from a template by name.

  @params soundName		-- Name of the sound template
  @params isLooped		-- Whether the sound should loop

  @returns Sound?		-- Cloned sound object or nil if unavailable
]]
local function createFootstepSound(soundName: string, isLooped: boolean): Sound?
  if not soundsFolder then
    Utils.warn(TAG, "Sounds folder not available")
    return nil
  end

  local template = soundsFolder:FindFirstChild(soundName)
  if not template or not template:IsA("Sound") then
    Utils.warn(`Missing sound template: {soundName}`)
    return nil
  end

  local sound = template:Clone()
  sound.Looped = isLooped

  if performanceMonitor then
    performanceMonitor:recordSoundCreation()
  end

  return sound
end

-- ============================================================================
-- REMOTE EVENT HANDLERS
-- ============================================================================

--[[
  Starts a looped footstep sound at the player's position.

  @params soundName	-- Name of the footstep sound template to play

  @returns nil
]]
Remotes.StartFootstepSound.OnClientEvent:Connect(function(soundName: string)
  if not Config.Config.enableFootsteps then
    return
  end

  cleanupSound()

  currentFootstepSound = createFootstepSound(soundName, true)
  if not currentFootstepSound then
    return
  end

  local workspaceMAFS = getWorkspaceMAFSFolder()
  if not workspaceMAFS then
    return
  end

  soundPart = Utils.createSoundPart(Vector3.new(0, 0, 0), "FootstepSoundEmitter")
  if soundPart then
    soundPart.Parent = workspaceMAFS
    currentFootstepSound.Parent = soundPart
    currentFootstepSound:Play()
  else
    Utils.warn(TAG, "Failed to create soundPart for footstep sound")
  end

  Utils.log(`Started footstep sound: {soundName}`)
end)

--[[
  Updates the current looped footstep sound with a new one.

  @params newSoundName	-- New sound template to switch to

  @returns nil
]]
Remotes.UpdateFootstepSound.OnClientEvent:Connect(function(newSoundName: string)
  if not Config.Config.enableFootsteps then
    return
  end

  if not currentFootstepSound or not soundPart then
    return
  end

  local newSound = createFootstepSound(newSoundName, true)
  if not newSound then
    return
  end

  currentFootstepSound:Stop()
  Utils.safeDestroy(currentFootstepSound)

  currentFootstepSound = newSound
  currentFootstepSound.Parent = soundPart
  currentFootstepSound:Play()

  Utils.log(`Updated footstep sound to: {newSoundName}`)
end)

--[[
  Stops and cleans up the current looped footstep sound.

  @returns nil
]]
Remotes.StopFootstepSound.OnClientEvent:Connect(function()
  cleanupSound()
  Utils.log(TAG, "Stopped footstep sound")
end)

--[[
  Plays a one-shot 3D footstep sound at a given position for spatial realism.

  @params position  -- World position of the sound
  @params soundName	-- Sound template to play

  @returns nil
]]
Remotes.PlayFootstep.OnClientEvent:Connect(function(position: Vector3, soundName: string)
  if not Config.Config.enableFootsteps then
    return
  end

  if not Utils.isValidPosition(position) then
    Utils.warn(TAG, "Invalid position for 3D footstep sound")
    return
  end

  local sound = createFootstepSound(soundName, false)
  if not sound then
    return
  end

  local workspaceMAFS = getWorkspaceMAFSFolder()
  if not workspaceMAFS then
    return
  end

  local part = Utils.createSoundPart(position, "3DFootstepSound")
  part.Parent = workspaceMAFS

  sound.Parent = part
  sound:Play()

  Debris:AddItem(part, sound.TimeLength + 1)

  Utils.log(`Played 3D footstep sound: {soundName} at {position}`)
end)

--[[
  Handles footprint replication event from the server.

  @params position          -- Position to place footprint
  @params footprintName     -- Footprint model to replicate

  @returns nil
]]
Remotes.ReplicateFootprint.OnClientEvent:Connect(function(position: Vector3, footprintName: string)
  placeFootprint(position, footprintName)
end)

-- ============================================================================
-- SOUND SYNCHRONIZATION
-- ============================================================================

--[[
  Synchronizes the sound emitter's position with the player's root part
  to keep the sound spatially accurate as the character moves.

  @returns nil
]]
RunService.RenderStepped:Connect(function()
  if not soundPart then
    return
  end

  local player = Players.LocalPlayer :: Player
  local character = player.Character
  local root = character and character:FindFirstChild("HumanoidRootPart") :: BasePart?

  if root and Utils.isValidPosition(root.Position) then
    soundPart.CFrame = root.CFrame
  end
end)

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

--[[
  Initializes the performance monitoring system on the client,
  if enabled in the configuration.

  @returns nil
]]
local function initializePerformanceMonitor(): ()
  if Config.isPerformanceMonitorEnabled() then
    local PerformanceMonitor =
      require(mainPath:WaitForChild("Shared"):WaitForChild("MAFS_PerformanceMonitor"))
    performanceMonitor = PerformanceMonitor.new(Config.Config)
    Utils.log(TAG, "Performance monitor initialized on client")
  else
    Utils.log(TAG, "Performance monitor disabled on client")
  end
end

--[[
  Initializes the MAFS client by setting up resource folders, performance
  monitoring, and verifying remote event readiness.

  @returns nil
]]
local function initializeClient(): ()
  Utils.log(TAG, "Initializing MAFS client...")

  -- Initialize resource folders
  initializeResourceFolders()

  -- Initialize performance monitoring
  initializePerformanceMonitor()

  -- Wait for remotes to be ready
  if not Remotes.waitForAllRemotes() then
    Utils.warn(TAG, "Failed to initialize remotes")
    return
  end

  Utils.log(TAG, "MAFS client initialized successfully")
end

--[[
  Starts asynchronous client initialization at runtime.

  @returns nil
]]
task.spawn(initializeClient)
