--!strict

--[[
    - file: CGS_OS_Client.luau

    - version: 1.1.0
    - author: BleckWolf25
    - contributors:

    - description:
      - Core Game System (CGS) Observation Sub-System
      - Features camera effects like:
        - Sway Effect (with idle sway)
        - Bobbing Effect (with movement threshold)
        - Dynamic FOV
        - Underwater Effect
        - Full Body View (Feature)
        - Directional Head (Feature)
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService: RunService = game:GetService("RunService")
local TweenService: TweenService = game:GetService("TweenService")
local UserInputService: UserInputService = game:GetService("UserInputService")

-- ============================================================================
-- PATHS
-- ============================================================================
local RpShared = ReplicatedStorage.CGS.Shared
local remotesFolder = ReplicatedStorage.CGS:WaitForChild("Remotes")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration =
  require(ReplicatedStorage.Configurations.Systems:WaitForChild("CGS_Configuration"))
local Types = require(RpShared.Parent:WaitForChild("CGS_Types"))
local Utils = require(RpShared:WaitForChild("CGS_Utils"))

-- ============================================================================
-- REMOTES
-- ============================================================================
local ToggleEffectsRemote = remotesFolder:WaitForChild("ToggleCameraEffects") :: RemoteEvent
local DirectionalHeadRemote = remotesFolder:WaitForChild("DirectionalHeadLook") :: RemoteEvent

-- ============================================================================
-- CONFIGURATION
-- ============================================================================
local TAG: string = "OS_Client"

-- Aliases for each effect configuration
local BodyViewConfig = Configuration.Config.Observation.BodyView
local DirectionalHeadConfig = Configuration.Config.Observation.DirectionalHead
local BobbingConfig = Configuration.Config.Observation.Bobbing
local SwayConfig = Configuration.Config.Observation.Sway
local DynamicFovConfig = Configuration.Config.Observation.DynamicFOV
local MotionConfig = Configuration.Config.Observation.MotionBlur
local UnderwaterConfig = Configuration.Config.Observation.UnderwaterEffect

-- ============================================================================
-- CONSTANTS & STATE
-- ============================================================================
-- Local player and camera references
local localPlayer: Player = Utils.getLocalPlayer()
local camera: Camera? = Utils.getCurrentCamera()

-- Whether camera effects are currently active
local effectsEnabled: boolean = true

-- Dictionary of active effects by name
local activeEffects: { [string]: Types.CameraEffect } = {}

-- Ordered list of effects for deterministic update processing
local sortedEffects: { Types.CameraEffect } = {}

-- ============================================================================
-- FEATURES
-- ============================================================================

-- Body view
local bodyviewFeature: Types.CameraEffect = {
  Enabled = BodyViewConfig.Enabled :: boolean,
  Priority = BodyViewConfig.Priority or 1 :: number,

  _connection = nil :: RBXScriptConnection?,
  _head = nil :: BasePart?,
  _humanoid = nil :: Humanoid?,

  --[[
		Initializes the body view effect by attaching a render loop that dynamically adjusts
		the camera offset based on raycasting from the head forward.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Init = function(self: Types.CameraEffect)
    local character = localPlayer.Character :: Model?
    if not character then
      return
    end

    local humanoid = character:FindFirstChildOfClass("Humanoid") :: Humanoid?
    local head = character:FindFirstChild("Head") :: BasePart?
    if not humanoid or not head then
      return
    end

    self._humanoid = humanoid
    self._head = head

    head.LocalTransparencyModifier = 1
    head.Transparency = 0
    head.CastShadow = true

    humanoid.CameraOffset = Vector3.new(0, 0, -1.35)

    self._connection = RunService.RenderStepped:Connect(function()
      if not character.Parent or not self._head or not self._humanoid then
        return
      end

      local origin = head.Position
      local direction = head.CFrame.LookVector * 2

      local rayParams = RaycastParams.new()
      rayParams.FilterType = Enum.RaycastFilterType.Exclude
      rayParams.FilterDescendantsInstances = { character }

      local result = workspace:Raycast(origin, direction, rayParams)

      local offsetZ: number
      if result then
        offsetZ = (origin - result.Position).Magnitude
      else
        offsetZ = 1.35
      end

      humanoid.CameraOffset = Vector3.new(0, 0, -offsetZ)
    end)

    -- Maintain body transparency consistency during rendering
    for _, part in ipairs(character:GetChildren()) do
      if part:IsA("BasePart") and part.Name ~= "Head" then
        part.LocalTransparencyModifier = part.Transparency
        part:GetPropertyChangedSignal("LocalTransparencyModifier"):Connect(function()
          part.LocalTransparencyModifier = part.Transparency
        end)
      end
    end
  end,

  --[[
		Update method for body view (unused but required for interface).

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Update = function()
    -- No-op
  end,

  --[[
		Resets the body view effect by disconnecting the render step and restoring camera offset.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    if self._connection then
      self._connection:Disconnect()
      self._connection = nil
    end

    if self._humanoid then
      self._humanoid.CameraOffset = Vector3.zero
      self._humanoid = nil
    end

    self._head = nil
  end,
}

-- Directional Head Movement
local directionalHeadFeature: Types.CameraEffect = {
  Enabled = DirectionalHeadConfig.Enabled :: boolean,
  Priority = DirectionalHeadConfig.Priority or 6 :: number,

  _character = nil :: Model?,
  _humanoid = nil :: Humanoid?,
  _root = nil :: BasePart?,
  _neck = nil :: Motor6D?,
  _yOffset = 0 :: number,
  _renderConnection = nil :: RBXScriptConnection?,
  _replicationThread = nil :: thread?,

  --[[
		Initializes the directional head effect. Connects to RenderStepped to update
		neck rotation based on the camera direction, and spawns a loop to replicate
		neck rotation to the server.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Init = function(self: Types.CameraEffect)
    -- Acquire and validate player character
    local character = localPlayer.Character or localPlayer.CharacterAdded:Wait()
    if not character:IsA("Model") then
      return
    end
    self._character = character

    -- Humanoid lookup
    local humanoid = character:FindFirstChildOfClass("Humanoid")
    if not (humanoid and humanoid:IsA("Humanoid")) then
      return
    end
    self._humanoid = humanoid

    -- Root part lookup
    local rootPart = character:FindFirstChild("HumanoidRootPart")
    if not (rootPart and rootPart:IsA("BasePart")) then
      return
    end
    self._root = rootPart

    -- Neck (Motor6D) lookup, fallback to Torso
    self._neck = character:FindFirstChild("Neck", true) :: Motor6D?
    if not self._neck and character:FindFirstChild("Torso") then
      self._neck = character:WaitForChild("Torso"):FindFirstChild("Neck") :: Motor6D?
    end
    if not (self._humanoid and self._root and self._neck) then
      return
    end

    -- Store original Y offset
    self._yOffset = self._neck.C0.Y

    -- Continuously update neck C0 every frame
    self._renderConnection = RunService.RenderStepped:Connect(function(dt)
      self:Update(dt, workspace.CurrentCamera :: Camera)
    end)

    -- Periodically replicate the neck's C0 to the server
    self._replicationThread = task.spawn(function()
      while true do
        if self._neck then
          local neck = self._neck :: Motor6D
          DirectionalHeadRemote:FireServer(neck.C0)
        end
        task.wait(1)
      end
    end)
  end,

  --[[
		Updates the neck C0 transformation based on camera's relative direction.

		@param self Types.CameraEffect -- The effect instance.
		@param _ any -- Ignored delta time (passed by RenderStepped).
		@param cam Camera -- The player's current camera.

		@return void
	]]
  Update = function(self: Types.CameraEffect, _: any, cam: Camera)
    if not self.Enabled then
      return
    end
    if not self._neck then
      return
    end
    if not self._root then
      return
    end
    if not cam then
      return
    end
    if not self._humanoid then
      return
    end

    local neck = self._neck :: Motor6D
    local yOffset: number = self._yOffset or 0

    -- Compute local look vector
    local relative = self._root.CFrame:ToObjectSpace(cam.CFrame)
    local lookVec = relative.LookVector

    -- Clamp yaw/pitch
    local yaw = math.clamp(math.asin(lookVec.X), -math.rad(60), math.rad(60))
    local pitch = math.clamp(math.asin(lookVec.Y), -math.rad(30), math.rad(30))

    -- Apply per-rig rotations
    if self._humanoid.RigType == Enum.HumanoidRigType.R15 then
      neck.C0 = CFrame.new(0, yOffset, 0) * CFrame.Angles(0, -yaw, 0) * CFrame.Angles(pitch, 0, 0)
    else
      neck.C0 = CFrame.new(0, yOffset, 0)
        * CFrame.Angles(3 * math.pi / 2, 0, math.pi)
        * CFrame.Angles(0, 0, -yaw)
        * CFrame.Angles(-pitch, 0, 0)
    end
  end,

  --[[
		Resets the directional head effect: disconnects events, cancels replication,
		and resets the neck's C0 transform.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    if self._renderConnection then
      self._renderConnection:Disconnect()
      self._renderConnection = nil
    end

    local replicationThread = self._replicationThread
    if replicationThread and typeof(replicationThread) == "thread" then
      task.cancel(replicationThread :: thread)
      self._replicationThread = nil
    end

    local yOffset: number = self._yOffset or 0
    if self._neck then
      self._neck.C0 = CFrame.new(0, yOffset, 0)
    end

    self._character = nil
    self._humanoid = nil
    self._root = nil
    self._neck = nil
    self._yOffset = 0
  end,
}

-- ============================================================================
-- CAMERA EFFECTS
-- ============================================================================

-- Bobbing Effect
local bobbingEffect: Types.CameraEffect = {
  Enabled = BobbingConfig.Enabled :: boolean,
  Priority = BobbingConfig.Priority or 2 :: number,
  MovementThreshold = BobbingConfig.MovementThreshold :: number,

  -- Internal state
  _rollTilt = 0 :: number,
  _verticalBop = 0 :: number,
  _horizontalBop = 0 :: number,
  _walkBop = 0 :: number,
  _movementSpeedFactor = 5 :: number,

  --[[
		Update the bobbing effect based on movement and velocity.

		@param self Types.CameraEffect -- The effect instance.
		@param deltaTime number -- Time delta since last frame.
		@param cam Camera -- The player's current camera.

		@return void
	]]
  Update = function(self: Types.CameraEffect, deltaTime: number, cam: Camera)
    if not self.Enabled then
      return
    end

    -- Normalize deltaTime into [0,1] range
    local dt = math.clamp(deltaTime * 30, 0, 1)

    -- Safely get the humanoid
    local character = localPlayer.Character
    local humanoid = character and character:FindFirstChildOfClass("Humanoid")
    if not humanoid or humanoid.Health <= 0 then
      return
    end

    -- Safely get the root part
    local rootPart = humanoid.RootPart
    if not rootPart or not rootPart:IsA("BasePart") then
      return
    end

    -- Compute movement speed
    local velocity = rootPart.AssemblyLinearVelocity
    local horizontalVel = Vector3.new(velocity.X, 0, velocity.Z)
    local speed = horizontalVel.Magnitude
    local clampedSpeed = math.min(speed, 25)

    -- Roll tilt from mouse movement
    local mdx = math.clamp(UserInputService:GetMouseDelta().X, -2.5, 2.5)
    self._rollTilt = Utils.lerp(self._rollTilt, mdx, 0.25 * dt)

    -- Vertical bobbing (idle)
    self._verticalBop =
      Utils.lerp(self._verticalBop, math.sin(tick() * self._movementSpeedFactor) / 5, 0.25 * dt)

    -- Horizontal head tilt based on movement direction
    local lookX = cam.CFrame:VectorToObjectSpace(velocity / math.max(humanoid.WalkSpeed, 0.01)).X
    self._horizontalBop = Utils.lerp(self._horizontalBop, -lookX * 0.04, 0.1 * dt)
    self._horizontalBop = math.clamp(self._horizontalBop, -0.12, 0.1)

    -- Walk bobbing when moving above threshold
    if speed > self.MovementThreshold then
      local freq = 0.5 * math.floor(self._movementSpeedFactor)
      self._walkBop = Utils.lerp(
        self._walkBop,
        math.cos(tick() * freq) * (self._movementSpeedFactor / 200),
        0.25 * dt
      )
    else
      self._walkBop = Utils.lerp(self._walkBop, 0, 0.05 * dt)
    end

    -- Adjust responsiveness based on speed
    if speed > 6 then
      self._movementSpeedFactor = 10
    elseif speed > 0.1 then
      self._movementSpeedFactor = 6
    end

    -- Apply all bops to camera CFrame
    cam.CFrame = cam.CFrame
      * CFrame.fromEulerAnglesXYZ(0, 0, math.rad(self._rollTilt))
      * CFrame.fromEulerAnglesXYZ(
        math.rad(self._verticalBop * dt),
        math.rad(self._walkBop * dt),
        self._horizontalBop
      )
      * CFrame.Angles(0, 0, math.rad(self._verticalBop * dt * (clampedSpeed / 5)))
  end,

  --[[
		Reset the bobbing effect's internal state.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    self._rollTilt = 0
    self._verticalBop = 0
    self._horizontalBop = 0
    self._walkBop = 0
    self._movementSpeedFactor = 5
  end,

  --[[
		Initialize the bobbing effect (logging only).

		@return void
	]]
  Init = function()
    Utils.log(TAG, "Bobbing effect initialized")
  end,
}

-- Sway Effect
local swayEffect: Types.CameraEffect = {
  Enabled = SwayConfig.Enabled :: boolean,
  Priority = SwayConfig.Priority or 3 :: number,
  Turn = 0 :: number,
  Sensitivity = SwayConfig.TurnSpeed :: number,
  ClampRange = SwayConfig.TurnClamp :: number,
  IdleAmount = SwayConfig.IdleAmount :: Vector3,
  IdleSpeed = SwayConfig.IdleSpeed :: number,

  --[[
		Update the sway effect based on mouse delta and idle animation.

		@param self Types.CameraEffect -- The effect instance.
		@param deltaTime number -- Time delta since last frame.
		@param cam Camera -- The player's current camera.

		@return void
	]]
  Update = function(self: Types.CameraEffect, deltaTime: number, cam: Camera)
    if not self.Enabled then
      return
    end

    -- Mouse sway
    local mouseDelta = UserInputService:GetMouseDelta()
    local clampedDelta = math.clamp(mouseDelta.X, -self.ClampRange, self.ClampRange)
    self.Turn = Utils.lerp(self.Turn, clampedDelta, self.Sensitivity * deltaTime)
    cam.CFrame *= CFrame.Angles(0, 0, math.rad(self.Turn))

    -- Idle sway when nearly still
    local character = localPlayer.Character
    if character then
      local rootPart = character:FindFirstChild("HumanoidRootPart")
      if rootPart and rootPart:IsA("BasePart") then
        if rootPart.AssemblyLinearVelocity.Magnitude < 0.1 then
          local time = tick()
          local idleAmount = self.IdleAmount
          local pitch = math.sin(time * self.IdleSpeed) * math.rad(idleAmount.X)
          local roll = math.sin(time * self.IdleSpeed * 1.3) * math.rad(idleAmount.Z)
          cam.CFrame *= CFrame.Angles(pitch, 0, roll)
        end
      end
    end
  end,

  --[[
		Reset the sway effect to its default state.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    self.Turn = 0
  end,

  --[[
		Initialize the sway effect (logging only).

		@return void
	]]
  Init = function()
    Utils.log(TAG, "Sway effect with idle sway initialized")
  end,
}

-- Dynamic FOV Effect
local dynamicFovEffect: Types.CameraEffect = {
  Enabled = DynamicFovConfig.Enabled :: boolean,
  Priority = DynamicFovConfig.Priority or 4 :: number,

  -- Configuration
  Config = {
    DefaultFOV = DynamicFovConfig.DefaultFOV :: number,
    SprintFOV = DynamicFovConfig.SprintFOV :: number,
    JumpFOV = DynamicFovConfig.JumpFOV :: number,
    FreeFallFOV = DynamicFovConfig.FreeFallFOV :: number,
    SwimFOV = DynamicFovConfig.SwimFOV :: number,
    CrouchFOV = DynamicFovConfig.CrouchFOV :: number,
    ProneFOV = DynamicFovConfig.ProneFOV :: number,
    TransitionSpeed = DynamicFovConfig.TransitionSpeed :: number,
  },

  -- State
  connections = {} :: { RBXScriptConnection },
  targetFOV = 70 :: number,
  lastFOV = 70 :: number,
  overrideFOV = nil :: number?,
  currentTween = nil :: Tween?,
  lastUpdateTime = 0 :: number,
  UPDATE_COOLDOWN = 0.1 :: number,

  --[[
		Smoothly tweens the camera's FOV to the specified value.

		@param self Types.CameraEffect -- The effect instance.
		@param fov number -- Target FieldOfView value.
	]]
  tweenFOVTo = function(self: Types.CameraEffect, fov: number)
    -- Cancel any ongoing tween
    if self.currentTween then
      self.currentTween:Cancel()
    end

    local cam: Camera? = workspace.CurrentCamera
    if not cam then
      Utils.warn("DynamicFOVEffect.TweenFOVTo: No camera found for tween.")
      return
    end

    -- Create and play tween
    self.currentTween = TweenService:Create(
      cam,
      TweenInfo.new(self.Config.TransitionSpeed, Enum.EasingStyle.Sine, Enum.EasingDirection.Out),
      { FieldOfView = fov }
    )
    self.currentTween:Play()
  end,

  --[[
		Sets or clears an override FOV.

		@param self Types.CameraEffect -- The effect instance.
		@param fov number? -- Override value or nil to reset.
	]]
  SetFOVOverride = function(self: Types.CameraEffect, fov: number?)
    self.overrideFOV = fov
    self:tweenFOVTo(fov or self.targetFOV)
  end,

  --[[
		Gets the current target FOV considering overrides.

		@param self Types.CameraEffect -- The effect instance.
		@return number -- Current FOV.
	]]
  GetTargetFOV = function(self: Types.CameraEffect): number
    return self.overrideFOV or self.targetFOV
  end,

  --[[
		Attempts to retrieve the current Humanoid from the camera subject or character.

		@return Humanoid? -- Found humanoid or nil.
	]]
  getCurrentHumanoid = function(): Humanoid?
    if not camera then
      return nil
    end

    local subject = camera.CameraSubject
    if subject and subject:IsA("Humanoid") then
      return subject :: Humanoid
    end

    local char = localPlayer.Character
    return char and char:FindFirstChildOfClass("Humanoid")
  end,

  --[[
		Updates target FOV based on humanoid state
	]]
  updateTargetFOV = function(self: Types.CameraEffect)
    local now = tick()
    if now - self.lastUpdateTime < self.UPDATE_COOLDOWN then
      return
    end
    self.lastUpdateTime = now

    local humanoid = self.getCurrentHumanoid()
    if not humanoid then
      return
    end

    local newFOV = self.Config.DefaultFOV
    local state = humanoid:GetState()

    -- State-based FOV calculation
    if state == Enum.HumanoidStateType.Swimming then
      newFOV = self.Config.SwimFOV
    elseif state == Enum.HumanoidStateType.Freefall then
      newFOV = self.Config.FreeFallFOV
    elseif state == Enum.HumanoidStateType.Jumping then
      newFOV = self.Config.JumpFOV
    elseif humanoid.MoveDirection.Magnitude > 0 then
      -- Sprint detection via attribute or high walk speed
      if humanoid:GetAttribute("IsSprinting") or humanoid.WalkSpeed > 14 then
        newFOV = self.Config.SprintFOV
      end
    end

    -- Attribute-based overrides
    if humanoid:GetAttribute("IsProned") then
      newFOV = self.Config.ProneFOV
    elseif humanoid:GetAttribute("IsCrouching") then
      newFOV = self.Config.CrouchFOV
    end

    -- Apply only on change and if not externally overridden
    if newFOV ~= self.lastFOV then
      self.targetFOV = newFOV
      self.lastFOV = newFOV
      if not self.overrideFOV then
        self:tweenFOVTo(newFOV)
      end
    end
  end,

  --[[
		Initializes the dynamic FOV system by connecting character/humanoid signals.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Init = function(self: Types.CameraEffect)
    local function onCharacterAdded(character: Model)
      -- Ensure self.connections exists and is properly typed
      local connections: { RBXScriptConnection } = self.connections or {}

      -- Disconnect existing connections
      for _, conn: RBXScriptConnection in ipairs(connections) do
        conn:Disconnect()
      end
      connections = {}
      self.connections = connections

      local humanoid = character:FindFirstChildOfClass("Humanoid")
      if not humanoid or not humanoid:IsA("Humanoid") then
        return
      end

      -- Type is implicitly Humanoid here
      local stateChangedConn = humanoid.StateChanged:Connect(function()
        self:updateTargetFOV()
      end)
      table.insert(connections, stateChangedConn)

      -- Attribute change signals
      local attributes: { string } = { "IsCrouching", "IsProned", "IsSprinting" }
      for _, attr: string in ipairs(attributes) do
        local attrSignal: RBXScriptSignal = humanoid:GetAttributeChangedSignal(attr)
        local attrConn = attrSignal:Connect(function()
          self:updateTargetFOV()
        end)
        table.insert(connections, attrConn)
      end

      -- Humanoid died connection
      local diedConn = humanoid.Died:Connect(function()
        self:SetFOVOverride(nil)
        self.targetFOV = self.Config.DefaultFOV
        self:tweenFOVTo(self.Config.DefaultFOV)
      end)
      table.insert(connections, diedConn)

      -- Initial update
      self:updateTargetFOV()
    end

    localPlayer.CharacterAdded:Connect(onCharacterAdded)
    local currentCharacter: Model? = localPlayer.Character
    if currentCharacter then
      task.defer(onCharacterAdded, currentCharacter)
    end

    local currentCamera = workspace.CurrentCamera
    if currentCamera then
      currentCamera.FieldOfView = self.Config.DefaultFOV
    end
  end,

  --[[
		Resets the dynamic FOV system by disconnecting all signal connections.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    local conns = self.connections :: { RBXScriptConnection }
    for _, conn in ipairs(conns) do
      conn:Disconnect()
    end
    self.connections = {}
  end,

  --[[
		Dynamic FOV is event-driven; no per-frame update required.
	]]
  Update = function()
    -- No-op
  end,
}

-- Motion Blur Effect
local motionBlurEffect: Types.CameraEffect = {
  Enabled = MotionConfig.Enabled :: boolean,
  Priority = MotionConfig.Priority or 5 :: number,

  _blur = nil :: BlurEffect?,
  _lastLookVector = Vector3.zero :: Vector3,

  --[[
		Initializes the motion blur effect by inserting a BlurEffect into the current camera
		and caching the initial camera look vector.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Init = function(self: Types.CameraEffect)
    -- Retrieves the current camera. Abort if unavailable
    local cam: Camera? = workspace.CurrentCamera
    if not cam then
      Utils.warn(TAG, "MotionBlurEffect.Init: No CurrentCamera available.")
      return
    end

    -- Create and configure BlurEffect
    self._blur = Instance.new("BlurEffect")
    self._blur.Name = "__MotionBlur"
    self._blur.Size = 0
    self._blur.Parent = cam

    -- Cache initial look vector for delta calculation
    self._lastLookVector = cam.CFrame.LookVector
  end,

  --[[
		Updates the motion blur effect based on camera rotation and character velocity.

		@param self Types.CameraEffect -- The effect instance.
		@param dt number -- Delta time since last frame.
		@param cam Camera -- The current camera.

		@return void
	]]
  Update = function(self: Types.CameraEffect, dt: number, cam: Camera)
    -- Skip update if disabled or not initialized
    if not self.Enabled or self._blur == nil then
      return
    end

    local blur = self._blur :: BlurEffect

    -- Attempt to fetch local player's character and humanoid root part
    local player = Players.LocalPlayer :: Player
    local character = player and player.Character
    local hrp = character and character:FindFirstChild("HumanoidRootPart") :: BasePart?
    local humanoid = character and character:FindFirstChildOfClass("Humanoid") :: Humanoid?

    -- When player or necessary parts are missing, decay blur to zero
    if not (hrp and humanoid) then
      blur.Size = math.clamp(blur.Size - MotionConfig.FadeSpeed * dt, 0, MotionConfig.maxBlur)
      return
    end

    -- Fetch config values locally for performance
    local maxBlur = MotionConfig.maxBlur
    local rotSensitivity = MotionConfig.rotationSensitivity
    local speedThreshold = MotionConfig.speedThreshold
    local fadeSpeed = MotionConfig.FadeSpeed

    -- Compute rotational component of blur
    local currentLook = cam.CFrame.LookVector
    local dotProduct = math.clamp(self._lastLookVector:Dot(currentLook), -1, 1)
    local angleRadians = math.acos(dotProduct)
    self._lastLookVector = currentLook
    local rotBlurRaw = math.deg(angleRadians) * rotSensitivity
    local rotBlur = Utils.smootherStep(rotBlurRaw / maxBlur) * maxBlur

    -- Compute translational (speed) component of blur
    local speed = hrp.AssemblyLinearVelocity.Magnitude
    local speedExcess = math.max(speed - speedThreshold, 0)
    local speedBlur = Utils.smootherStep(speedExcess / (maxBlur * 2)) * maxBlur

    -- Combine both effects and interpolate towards target
    local targetBlur = math.clamp(rotBlur + speedBlur, 0, maxBlur)
    blur.Size = math.clamp(blur.Size + ((targetBlur - blur.Size) * dt * fadeSpeed), 0, maxBlur)
  end,

  --[[
		Resets the motion blur effect by destroying the BlurEffect instance.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    if self._blur then
      self._blur:Destroy()
      self._blur = nil
    end
  end,
}

-- Underwater Visual Effect
local underwaterEffect: Types.CameraEffect = {
  Enabled = UnderwaterConfig.Enabled :: boolean,
  Priority = UnderwaterConfig.Priority or 7 :: number,

  blur = nil :: BlurEffect?,
  -- TODO: Implement underwater effect
  --[[
  How it's supposed to work?

  Create parts on the workspace called `WaterHitBox`
  On touch, apply the underwater effect WHILE on the part
  On exit, small flash from the sun, small bubbles (replicated to all clients) and remove the effect
  Realistic effects underwater:
    - Blur
    - Dirty water (if lake/sea/river)
    - Clean water (if pool)
    - Bubbles
    - Flash from the sun (when exiting)
    - Darker vision
  ]]

  Init = function(self: Types.CameraEffect)
    -- TODO: Implement Initialization here

    if self.blur == nil then
      self.blur = Instance.new("BlurEffect")
      self.blur.Name = "UnderWaterEffectBlur"
      self.blur.Size = 50
      self.blur.Enabled = false
    end
  end,

  Update = function()
    -- TODO: Implement Update here if needed
  end,

  Reset = function()
    -- TODO: Implement Reset here if needed
  end,
}

-- ============================================================================
-- EFFECT REGISTRATION
-- ============================================================================
Utils.AddEffect("Bodyview", bodyviewFeature, activeEffects, sortedEffects, TAG)
Utils.AddEffect("Bobbing", bobbingEffect, activeEffects, sortedEffects, TAG)
Utils.AddEffect("Sway", swayEffect, activeEffects, sortedEffects, TAG)
Utils.AddEffect("DynamicFOV", dynamicFovEffect, activeEffects, sortedEffects, TAG)
Utils.AddEffect("MotionBlur", motionBlurEffect, activeEffects, sortedEffects, TAG)
Utils.AddEffect("DirectionalHead", directionalHeadFeature, activeEffects, sortedEffects, TAG)
Utils.AddEffect("Underwater", underwaterEffect, activeEffects, sortedEffects, TAG)

-- ============================================================================
-- CHARACTER EVENTS & MANAGEMENT
-- ============================================================================

--[[
	Called when the local player's character is added to the game.
	Initializes the camera and resets/initializes all active effects.

	@param char Model -- The new character model.
]]
local function onCharacterAdded(char: Model)
  camera = workspace.CurrentCamera
  if camera then
    camera.CameraType = Enum.CameraType.Custom
  end

  -- Log the character addition
  Utils.log(TAG, tostring(char) .. ": character added, resetting camera effects")

  for _, effect in activeEffects do
    if effect.Reset then
      effect:Reset()
    end
    if effect.Init then
      effect:Init()
    end
  end
end

--[[
	Called when the local player's character is removed.
	Resets all active effects.
]]
local function onCharacterRemoving()
  for _, effect in activeEffects do
    if effect.Reset then
      effect:Reset()
    end
  end
end

-- Connect to the CharacterAdded event to initialize camera and effects
localPlayer.CharacterAdded:Connect(onCharacterAdded)

-- Connect to the CharacterRemoving event to reset all active effects
localPlayer.CharacterRemoving:Connect(onCharacterRemoving)

-- If the character already exists, defer initialization
if localPlayer.Character then
  task.defer(onCharacterAdded, localPlayer.Character)
end

-- ============================================================================
-- REMOTE EVENTS
-- ============================================================================

--[[
	Toggles all camera effects based on remote input.

	@param enabled boolean -- Whether effects should be enabled or not.
]]
ToggleEffectsRemote.OnClientEvent:Connect(function(enabled: boolean)
  effectsEnabled = enabled
end)

--[[
	Updates the neck orientation (C0) of a character's head to face a target CFrame.

	@param otherPlayer Player -- The player whose character's neck should be rotated.
	@param targetC0 CFrame -- The target orientation for the neck joint.
]]
DirectionalHeadRemote.OnClientEvent:Connect(function(otherPlayer: Player, targetC0: CFrame)
  if otherPlayer == localPlayer then
    return
  end

  local char = otherPlayer.Character
  local neck = char and char:FindFirstChild("Neck", true) :: Motor6D?
  if neck then
    TweenService
      :Create(neck, TweenInfo.new(0.35, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
        C0 = targetC0,
      })
      :Play()
  end
end)

-- ============================================================================
-- RENDER LOOP
-- ============================================================================

--[[
	Updates all enabled camera effects each frame.

	@param dt number -- Delta time since last frame.
]]
RunService:BindToRenderStep(
  "CGS_CameraEffects",
  Enum.RenderPriority.Camera.Value + 1,
  function(dt: number)
    if not effectsEnabled then
      return
    end
    camera = workspace.CurrentCamera
    if not camera then
      return
    end

    for _, effect in sortedEffects do
      if effect.Enabled then
        effect:Update(dt, camera)
      end
    end
  end
)
