--!strict

--[[
  - file: Ragdoll_Client.luau

  - version: 1.0.0
  - author: BleckWolf25
  - contributors: Silver

  - copyright: Dynamic Innovative Studio

  - description:
    - Client-side logic for the Ragdoll mechanic. Listens for RagdollEvent to enable ragdolls and manages limits/lifetimes.
    - Handles replication and physics simulation on the client to minimize server lag.
]]

-- Ragdoll_Client.luau
-- Client-side logic for the Ragdoll mechanic. Listens for RagdollEvent to enable ragdolls and manages limits/lifetimes.
-- Handles replication and physics simulation on the client to minimize server lag.

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService: RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Config =
  require(ReplicatedStorage.Configurations.Mechanics:WaitForChild("Ragdoll_Configuration"))
local Remotes = require(ReplicatedStorage.Mechanics.Ragdoll:WaitForChild("Ragdoll_Remotes"))
local Types = require(ReplicatedStorage.Mechanics.Ragdoll:WaitForChild("Ragdoll_Types"))
local Utils = require(ReplicatedStorage.Mechanics.Ragdoll.Shared:WaitForChild("Ragdoll_Utils"))

-- ============================================================================
-- CONSTANTS & VARIABLES
-- ============================================================================
local TAG: string = "Client"

local activeStates: { Types.RagdollState } = {}

local Camera = workspace.CurrentCamera :: Camera?

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

-- Helper to enforce max ragdolls
local function enforceLimit(config: Types.RagdollConfig)
  if #activeStates <= config.MaxRagdolls then
    return
  end

  -- Sort oldest → newest
  table.sort(activeStates, function(a, b)
    return a.RagdollStartTime < b.RagdollStartTime
  end)

  -- Remove oldest
  local oldest = table.remove(activeStates, 1)
  if oldest then
    Utils.CleanupRagdoll(oldest)
  end
end

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================

-- Heartbeat cleanup
RunService.Heartbeat:Connect(function()
  local now = tick()

  for i = #activeStates, 1, -1 do
    local st = activeStates[i]
    if st and st.Character and st.Character.Parent then
      if now - st.RagdollStartTime >= Config.MaxRagdollLifetime then
        Utils.CleanupRagdoll(st)
        table.remove(activeStates, i)
      end
    else
      table.remove(activeStates, i)
    end
  end
end)

-- ============================================================================
-- EVENTS
-- ============================================================================

-- Respond to server
Remotes.RagdollEvent.OnClientEvent:Connect(function(character: Model, config: Types.RagdollConfig)
  if not config.CollisionEnabled then
    return
  end

  -- STREAMING-AWARE: Ignore if model isn't streamed in
  if not character:IsDescendantOf(workspace) then
    Utils.log(TAG, "Ragdoll skipped (not streamed in):", character.Name)
    return
  end

  -- DISTANCE CULLING: Skip if too far from LocalPlayer's camera
  local root = character:FindFirstChild("HumanoidRootPart") :: BasePart?
  if root and Camera then
    local camPos = Camera.CFrame.Position
    local dist = (camPos - root.Position).Magnitude

    if dist > config.MaxBroadcastDistance then
      Utils.log(TAG, `Ragdoll skipped (distance = {math.floor(dist)}):`, character.Name)
      return
    end
  end

  local state = Utils.EnableRagdoll(character, config)
  if state.IsRagdolled then
    table.insert(activeStates, state)
    enforceLimit(config)
  end
end)
