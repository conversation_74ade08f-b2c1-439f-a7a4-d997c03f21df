--!strict

--[[
  - file: Ragdoll_Server.luau

  - version: 1.0.0
  - author: BleckWolf25
  - contributors: Silver

  - copyright: Dynamic Innovative Studio

  - description:
    - Server-side logic for the Ragdoll mechanic. Detects character death and triggers ragdoll state via RemoteEvent.
    - Clients handle replication and physics simulation.
]]

-- Ragdoll_Server.luau
-- Server-side logic for the Ragdoll mechanic. Detects character death and triggers ragdoll state via RemoteEvent.
-- Clients handle replication and physics simulation.

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Config =
  require(ReplicatedStorage.Configurations.Mechanics:WaitForChild("Ragdoll_Configuration"))
local Remotes = require(ReplicatedStorage.Mechanics.Ragdoll:WaitForChild("Ragdoll_Remotes"))
local Utils = require(ReplicatedStorage.Mechanics.Ragdoll.Shared:WaitForChild("Ragdoll_Utils"))

-- ============================================================================
-- CNSTANTS, STATES, VARIABLES
-- ============================================================================
local TAG: string = "Server"

-- Access the RagdollEvent directly from the Remotes module
local RagdollEvent = Remotes.RagdollEvent :: RemoteEvent

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

local function setClientNetworkOwner(character: Model, player: Player)
  for _, part in ipairs(character:GetDescendants()) do
    if part:IsA("BasePart") then
      -- Assign to player so client owns simulation
      pcall(function()
        part:SetNetworkOwner(player)
      end)
    end
  end
end

-- When a character dies, broadcast to nearby clients
local function onCharacterAdded(player: Player, character: Model)
  local humanoid = character:WaitForChild("Humanoid") :: Humanoid

  humanoid.Died:Connect(function()
    Utils.log(TAG, "Player died:", player.Name)

    -- Assign Network Ownership to client
    setClientNetworkOwner(character, player)

    local root = character:FindFirstChild("HumanoidRootPart") :: BasePart?
    if not root then
      return
    end

    local radius = Config.MaxBroadcastDistance
    local boxSize = Vector3.new(radius * 2, radius * 2, radius * 2)
    local partsInBox = workspace:GetPartBoundsInBox(root.CFrame, boxSize)

    -- Track players to whom we already sent the event
    local sentPlayers: { [Player]: true } = {}

    for _, part in ipairs(partsInBox) do
      -- Skip if not in a character model
      local model = part:FindFirstAncestorOfClass("Model")
      if not model then
        continue
      end

      -- Try to resolve player
      local p = Players:GetPlayerFromCharacter(model)
      if p and not sentPlayers[p] then
        sentPlayers[p] = true
        RagdollEvent:FireClient(p, character, Config)
      end
    end

    -- Always ensure the owner receives it
    if not sentPlayers[player] then
      RagdollEvent:FireClient(player, character, Config)
    end
  end)
end

local function onPlayerAdded(player: Player)
  player.CharacterAdded:Connect(function(char)
    onCharacterAdded(player, char)
  end)
  if player.Character then
    onCharacterAdded(player, player.Character)
  end
end

-- ============================================================================
-- EVENTS
-- ============================================================================

Players.PlayerAdded:Connect(onPlayerAdded)
for _, pl in ipairs(Players:GetPlayers()) do
  onPlayerAdded(pl)
end
