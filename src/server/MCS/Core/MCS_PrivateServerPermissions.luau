--!strict

--[[
    - file: MCS_PrivateServerPermissions.luau

    - version: 1.0.1
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Private Server Permissions Module for the Modular Command System (MCS)
      - Features:
        - Set player permissions for private servers and allow normal users to use all MCS commands without affecting public servers
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local DataStoreService: DataStoreService = game:GetService("DataStoreService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(game.ReplicatedStorage.MCS.MCS_Types)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local PrivateServerPermissions = {}

-- ============================================================================
-- VARIABLES
-- ============================================================================
local permissionsDataStore = DataStoreService:GetDataStore("MCS_PrivateServerPermissions")

-- ============================================================================
-- FUNCTIONS
-- ============================================================================

--[[
  Sets a user's permission level in a specific private server.

  @param privateServerId: string - Unique identifier of the private server
  @param userId: number - Player's UserId
  @param permissionLevel: number - New permission level to assign

  @return none
]]
function PrivateServerPermissions.SetPlayerPermission(
  privateServerId: string,
  userId: number,
  permissionLevel: number
)
  local key: string = privateServerId
  local permissions: Types.PermissionsData = permissionsDataStore:GetAsync(key) or {}
  permissions[userId] = permissionLevel

  local success = pcall(function()
    permissionsDataStore:SetAsync(key, permissions)
  end)

  if not success then
    warn("Failed to set permission for user " .. userId)
  end
end

--[[
  Retrieves a player's permission level in a specific private server.

  @param privateServerId: string - Unique identifier of the private server
  @param userId: number - Player's UserId

  @return number? - The permission level if set, otherwise nil
]]
function PrivateServerPermissions.GetPlayerPermission(
  privateServerId: string,
  userId: number
): number?
  local key: string = privateServerId
  local permissions: Types.PermissionsData? = permissionsDataStore:GetAsync(key)

  if permissions then
    return permissions[userId]
  end

  return nil
end

--[[
  Removes a custom permission entry for a player in a private server.

  @param privateServerId: string - Unique identifier of the private server
  @param userId: number - Player's UserId

  @return none
]]
function PrivateServerPermissions.RemovePlayerPermission(privateServerId: string, userId: number)
  local key: string = privateServerId
  local permissions: Types.PermissionsData? = permissionsDataStore:GetAsync(key)

  if permissions and permissions[userId] then
    permissions[userId] = nil

    local success = pcall(function()
      permissionsDataStore:SetAsync(key, permissions)
    end)

    if not success then
      warn("Failed to remove permission for user " .. userId)
    end
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return PrivateServerPermissions
