--!strict

--[[
    - file: MCS_PermissionService.luau

    - version: 2.0.1
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Permission Service for the Modular Command System (MCS)
      - Manages authorization for command execution:
        - Defines permission roles and levels
        - Checks if players can execute specific commands
        - Provides caching for performance optimization
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local PrivateServerPermissions = require(script.Parent:WaitForChild("MCS_PrivateServerPermissions"))
local Types = require(ReplicatedStorage.MCS.MCS_Types)
local Utils = require(ReplicatedStorage.MCS.Shared.MCS_Utils)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local PermissionService = {}

-- ============================================================================
-- VARIABLES & CONFIGURATION
-- ============================================================================

-- Command permission requirements (default = PLAYER)
local commandPermissions: Types.CommandPermissions = {}

-- Permission cache to avoid frequent group rank checks
local permissionCache: Types.PermissionCache = {}

-- Permission levels
PermissionService.Levels = {
  PLAYER = 1,
  DISDevelopers = 249,
  JuniorModerators = 250,
  GameModerators = 252,
  SeniorModerators = 253,
  Anonmancer = 254,
  BleckWolf25 = 255,
} :: Types.PermissionLevels

-- Configuration
local CONFIG = {
  GROUP_ID = Configuration.Group.GROUP_ID,
  RANK_TO_LEVEL = {
    [255] = PermissionService.Levels.BleckWolf25,
    [254] = PermissionService.Levels.Anonmancer,
    [253] = PermissionService.Levels.SeniorModerators,
    [252] = PermissionService.Levels.GameModerators,
    [250] = PermissionService.Levels.JuniorModerators,
    [249] = PermissionService.Levels.DISDevelopers,
    [1] = PermissionService.Levels.PLAYER,
  },
  SPECIAL_USERS = {},
  CACHE_DURATION = Configuration.Group.CACHE_DURATION,
}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================
function PermissionService.init()
  Players.PlayerRemoving:Connect(function(player)
    permissionCache[player.UserId] = nil
  end)
  Utils.print("PermissionService", "Initialized")
end

-- ============================================================================
-- FUNCTIONS
-- ============================================================================

-- Register permission requirement for a command
function PermissionService.registerCommandPermission(commandName: string, permissionLevel: number)
  commandPermissions[commandName:lower()] = permissionLevel
end

-- Get permission level for a command
function PermissionService.getCommandPermissionLevel(commandName: string): number
  return commandPermissions[commandName:lower()] or PermissionService.Levels.PLAYER
end

-- Get permission level for a player
function PermissionService.getPermissionLevel(player: Player): number
  local userId = player.UserId
  if permissionCache[userId] then
    local cacheData = permissionCache[userId]
    if os.time() - cacheData.lastUpdated < CONFIG.CACHE_DURATION then
      return cacheData.level
    end
  end

  local level: number = PermissionService.Levels.PLAYER
  local specialLevel = CONFIG.SPECIAL_USERS[userId]
  if specialLevel then
    level = specialLevel
  elseif CONFIG.GROUP_ID > 0 then
    local rank = player:GetRankInGroup(CONFIG.GROUP_ID)
    local highestLevel = PermissionService.Levels.PLAYER
    for rankValue, permLevel in next, CONFIG.RANK_TO_LEVEL :: { [number]: number } do
      if rank >= rankValue and permLevel > highestLevel then
        highestLevel = permLevel
      end
    end
    level = highestLevel
  end

  permissionCache[userId] = {
    level = level,
    lastUpdated = os.time(),
  } :: Types.PermissionCacheEntry

  return level
end

-- Get effective permission level, considering private server permissions
function PermissionService.getEffectivePermissionLevel(player: Player): number
  local globalLevel = PermissionService.getPermissionLevel(player)
  if game.PrivateServerId ~= "" then
    -- Private server owner gets at least SeniorModerators level
    if player.UserId == game.PrivateServerOwnerId then
      return math.max(globalLevel, PermissionService.Levels.SeniorModerators)
    else
      local privateLevel =
        PrivateServerPermissions.GetPlayerPermission(game.PrivateServerId, player.UserId)
      if privateLevel then
        return math.max(globalLevel, privateLevel)
      end
    end
  end
  return globalLevel
end

-- Check if player can use a specific command
function PermissionService.canUseCommand(player: Player, commandName: string): boolean
  local effectiveLevel = PermissionService.getEffectivePermissionLevel(player)
  local requiredLevel = PermissionService.getCommandPermissionLevel(commandName)

  -- In public servers, only staff members can use commands
  if game.PrivateServerId == "" then
    local globalLevel = PermissionService.getPermissionLevel(player)
    if globalLevel <= PermissionService.Levels.PLAYER then
      return false -- Regular players cannot use commands in public servers
    end
  end

  return effectiveLevel >= requiredLevel
end

-- Set permission level for a specific command
function PermissionService.setCommandPermission(commandName: string, level: number)
  commandPermissions[commandName:lower()] = level
end

-- Get all commands accessible by player's permission level
function PermissionService.getAccessibleCommands(player: Player): Types.AccessibleCommands
  local playerLevel = PermissionService.getEffectivePermissionLevel(player)
  local accessibleCommands: Types.AccessibleCommands = {}
  for command, requiredLevel in pairs(commandPermissions) do
    if playerLevel >= requiredLevel then
      table.insert(accessibleCommands, command)
    end
  end
  return accessibleCommands
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return PermissionService
