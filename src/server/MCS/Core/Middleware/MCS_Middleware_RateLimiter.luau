--!strict

--[[
    - file: MCS_Middleware_RateLimiter.luau

    - version: 2.0.2
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Rate Limiting Middleware for Modular Command System (MCS)
      - Prevents command spam through intelligent rate limiting
      - Provides tiered rate limits based on permission levels
      - Implements sliding window algorithm for accurate rate limiting
      - Features automatic cleanup and performance monitoring
      - Supports per-command and global rate limiting strategies
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")

-- ============================================================================
-- MODULES
-- ============================================================================
local PermissionService = require(
  ServerScriptService:WaitForChild("MCS"):WaitForChild("Core"):WaitForChild("MCS_PermissionService")
)
local Types = require(ReplicatedStorage:WaitForChild("MCS"):WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage:WaitForChild("MCS").Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local TAG: string = "RateLimiter"
local CLEANUP_INTERVAL = 60 -- seconds
local CLEANUP_PROBABILITY = 0.02 -- 2% chance per command to trigger cleanup
local HISTORY_RETENTION_TIME = 300 -- 5 minutes of command history retention

-- ============================================================================
-- VARIABLES
-- ============================================================================
local isInitialized: boolean = false
local lastGlobalCleanup: number = 0
local totalRateLimitViolations: number = 0
local totalCommandsChecked: number = 0

-- User command tracking
-- Format: {userId = UserRateLimitData}
local userRateLimitData: { [number]: Types.UserRateLimitData } = {}

-- Rate limit configurations by permission level
local rateLimitConfigs: { [number]: Types.RateLimitConfig } = {}

-- Per-command rate limit overrides
local commandSpecificLimits: { [string]: Types.RateLimitConfig } = {}

-- ============================================================================
-- PRIVATE FUNCTIONS
-- ============================================================================

-- Initialize rate limit configurations
local function initializeRateLimits()
  -- Default configurations based on permission levels
  rateLimitConfigs = {
    -- Standard players: 3 commands per 5 seconds
    [PermissionService.Levels.PLAYER] = {
      maxCommands = 3,
      windowSeconds = 5,
      burstAllowance = 1,
    },

    -- D.I.S Developers: 5 commands per 5 seconds
    [PermissionService.Levels.DISDevelopers] = {
      maxCommands = 5,
      windowSeconds = 5,
      burstAllowance = 2,
    },

    -- Junior Moderators: 8 commands per 5 seconds
    [PermissionService.Levels.JuniorModerators] = {
      maxCommands = 8,
      windowSeconds = 5,
      burstAllowance = 2,
    },

    -- Game Moderators: 12 commands per 5 seconds
    [PermissionService.Levels.GameModerators] = {
      maxCommands = 12,
      windowSeconds = 5,
      burstAllowance = 3,
    },

    -- Senior Moderators: 15 commands per 5 seconds
    [PermissionService.Levels.SeniorModerators] = {
      maxCommands = 15,
      windowSeconds = 5,
      burstAllowance = 5,
    },

    -- High-level administrators: 25 commands per 5 seconds
    [PermissionService.Levels.Anonmancer] = {
      maxCommands = 25,
      windowSeconds = 5,
      burstAllowance = 8,
    },

    -- Owner: 30 commands per 5 seconds
    [PermissionService.Levels.BleckWolf25] = {
      maxCommands = 30,
      windowSeconds = 5,
      burstAllowance = 10,
    },
  }

  -- Command-specific rate limits for sensitive operations
  commandSpecificLimits = {
    -- Moderation commands might need stricter limits
    ["ban"] = {
      maxCommands = 2,
      windowSeconds = 10,
    },
    ["kick"] = {
      maxCommands = 3,
      windowSeconds = 10,
    },
    ["warn"] = {
      maxCommands = 5,
      windowSeconds = 10,
    },
  }
end

-- Get rate limit configuration for a user and command
local function getRateLimitConfig(
  permissionLevel: number,
  commandName: string
): Types.RateLimitConfig
  -- Check for command-specific limits first
  local commandLimit = commandSpecificLimits[commandName:lower()]
  if commandLimit then
    return commandLimit
  end

  -- Fall back to permission-level limits
  return rateLimitConfigs[permissionLevel] or rateLimitConfigs[PermissionService.Levels.PLAYER]
end

-- Clean up expired command history entries for a specific user
local function cleanupUserHistory(userId: number, currentTime: number): number
  local userData = userRateLimitData[userId]
  if not userData then
    return 0
  end

  local cleanedCount = 0
  local newHistory: { Types.CommandHistoryEntry } = {}

  for _, entry in ipairs(userData.commandHistory) do
    if currentTime - entry.timestamp <= HISTORY_RETENTION_TIME then
      table.insert(newHistory, entry)
    else
      cleanedCount += 1
    end
  end

  userData.commandHistory = newHistory
  userData.lastCleanup = currentTime

  return cleanedCount
end

-- Perform global cleanup of rate limit data
local function performGlobalCleanup()
  local currentTime = os.time()

  -- Only run global cleanup every minute
  if currentTime - lastGlobalCleanup < CLEANUP_INTERVAL then
    return
  end

  Utils.startTimer("GlobalRateLimitCleanup")

  local usersRemoved = 0
  local entriesCleaned = 0
  local usersToRemove: { number } = {}

  for userId, userData in pairs(userRateLimitData) do
    local cleaned = cleanupUserHistory(userId, currentTime)
    entriesCleaned += cleaned

    -- Remove users with no recent activity
    if #userData.commandHistory == 0 and currentTime - userData.lastCleanup > CLEANUP_INTERVAL then
      table.insert(usersToRemove, userId)
    end
  end

  for _, userId in ipairs(usersToRemove) do
    userRateLimitData[userId] = nil
    usersRemoved += 1
  end

  lastGlobalCleanup = currentTime
  Utils.endTimer(TAG, "GlobalRateLimitCleanup")

  if usersRemoved > 0 or entriesCleaned > 0 then
    Utils.print(
      TAG,
      string.format(
        "Cleanup completed: %d users removed, %d entries cleaned",
        usersRemoved,
        entriesCleaned
      )
    )
  end
end

-- Initialize user rate limit data if not exists
local function initializeUserData(userId: number)
  if not userRateLimitData[userId] then
    userRateLimitData[userId] = {
      commandHistory = {},
      lastCleanup = os.time(),
      totalCommands = 0,
      violationCount = 0,
    } :: Types.UserRateLimitData
  end
  return userRateLimitData[userId]
end

-- Check if user is within rate limits using sliding window algorithm
local function checkRateLimit(
  userId: number,
  commandName: string,
  config: Types.RateLimitConfig,
  currentTime: number
): (boolean, string?, number?)
  local userData = initializeUserData(userId)

  -- Clean up old entries for this user
  cleanupUserHistory(userId, currentTime)

  -- Count commands within the current window
  local commandsInWindow = 0
  local windowStart = currentTime - config.windowSeconds

  for _, entry in ipairs(userData.commandHistory) do
    if entry.timestamp > windowStart then
      commandsInWindow += 1
    end
  end

  -- Calculate effective limit (base + burst allowance)
  local effectiveLimit = config.maxCommands
  if config.burstAllowance and userData.violationCount == 0 then
    effectiveLimit += config.burstAllowance
  end

  -- Check if user would exceed rate limit
  if commandsInWindow >= effectiveLimit then
    -- Calculate wait time until oldest command in window expires
    local oldestInWindow = math.huge
    for _, entry in ipairs(userData.commandHistory) do
      if entry.timestamp > windowStart then
        oldestInWindow = math.min(oldestInWindow, entry.timestamp)
      end
    end

    local waitTime = math.ceil(config.windowSeconds - (currentTime - oldestInWindow))
    userData.violationCount += 1
    userData.lastViolation = currentTime
    totalRateLimitViolations += 1

    local message = string.format(
      "Rate limit exceeded. Please wait %d second%s before using another command.",
      waitTime,
      waitTime == 1 and "" or "s"
    )

    return false, message, waitTime
  end

  -- Record the command execution
  table.insert(userData.commandHistory, {
    timestamp = currentTime,
    commandName = commandName,
  })

  userData.totalCommands += 1

  return true, nil, nil
end

-- ============================================================================
-- MIDDLEWARE IMPLEMENTATION
-- ============================================================================
local RateLimiter = {
  name = TAG,
  priority = 3,
  enabled = true,
  version = "2.0.1",
} :: Types.MiddlewareModule

-- Initialize the rate limiter middleware
function RateLimiter.init(): boolean
  Utils.startTimer("RateLimiterInit")

  -- Initialize rate limit configurations
  initializeRateLimits()

  -- Set up cleanup when players leave
  Players.PlayerRemoving:Connect(function(player)
    userRateLimitData[player.UserId] = nil
    Utils.print(
      TAG,
      string.format("Cleaned up rate limit data for %s (%d)", player.Name, player.UserId)
    )
  end)

  -- Verify PermissionService is available
  if not PermissionService or not PermissionService.getPermissionLevel then
    Utils.print(TAG, "ERROR: PermissionService not available or invalid")
    return false
  end

  isInitialized = true
  Utils.endTimer(TAG, "RateLimiterInit")
  Utils.print(TAG, "Rate limiter middleware initialized successfully")

  return true
end

-- Main rate limiting process
function RateLimiter.process(player: Player, commandName: string): (boolean, string?, any?)
  if not isInitialized then
    return false, "Rate limiter middleware not initialized", nil
  end

  Utils.startTimer("RateLimitCheck")
  totalCommandsChecked += 1

  -- Occasional cleanup to prevent memory bloat
  if math.random() < CLEANUP_PROBABILITY then
    performGlobalCleanup()
  end

  local currentTime = os.time()
  local userId = player.UserId

  -- Get permission level and rate limit configuration
  local permissionLevel = PermissionService.getPermissionLevel(player)
  local config = getRateLimitConfig(permissionLevel, commandName)

  -- Sanitize command name
  local sanitizedCommand = Utils.sanitize(commandName)

  -- Check rate limit
  local allowed, errorMessage, waitTime =
    checkRateLimit(userId, sanitizedCommand, config, currentTime)

  Utils.endTimer(TAG, "RateLimitCheck")

  if not allowed then
    Utils.print(
      TAG,
      string.format(
        "Rate limit violation: %s (%d) blocked from using '%s' - %s",
        player.Name,
        userId,
        sanitizedCommand,
        errorMessage or "unknown reason"
      )
    )
    return false,
      errorMessage,
      {
        waitTime = waitTime,
        rateLimitConfig = config,
        violationType = "rate_limit_exceeded",
      }
  end

  -- Success - provide useful data for downstream middleware
  return true,
    nil,
    {
      permissionLevel = permissionLevel,
      rateLimitConfig = config,
      commandsInWindow = Utils.tableCount(
        userRateLimitData[userId] and userRateLimitData[userId].commandHistory or {}
      ),
      timestamp = currentTime,
    }
end

-- Cleanup function for middleware shutdown
function RateLimiter.cleanup()
  Utils.print(TAG, "Cleaning up rate limiter middleware")

  -- Clear all rate limit data
  userRateLimitData = {}

  -- Reset counters
  totalRateLimitViolations = 0
  totalCommandsChecked = 0
  lastGlobalCleanup = 0

  isInitialized = false

  Utils.print(
    TAG,
    string.format(
      "Cleanup completed. Processed %d commands, %d violations total",
      totalCommandsChecked,
      totalRateLimitViolations
    )
  )
end

-- ============================================================================
-- PUBLIC API
-- ============================================================================

-- Get rate limit statistics for monitoring
function RateLimiter.getStatistics(): { [string]: any }
  return {
    totalCommandsChecked = totalCommandsChecked,
    totalViolations = totalRateLimitViolations,
    activeUsers = Utils.tableCount(userRateLimitData),
    lastGlobalCleanup = lastGlobalCleanup,
  }
end

-- Manually trigger cleanup (useful for testing)
function RateLimiter.forceCleanup()
  if isInitialized then
    performGlobalCleanup()
  end
end

-- Set custom rate limit for a specific command
function RateLimiter.setCommandRateLimit(commandName: string, config: Types.RateLimitConfig)
  commandSpecificLimits[commandName:lower()] = config
  Utils.print(TAG, string.format("Set custom rate limit for command '%s'", commandName))
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return RateLimiter
