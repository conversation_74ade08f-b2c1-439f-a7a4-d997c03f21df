--!strict

--[[
    - file: MCS_Middleware_Analytics.luau

    - version: 2.0.2
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Analytics Middleware for Modular Command System (MCS)
      - Tracks command usage and player behavior
      - Stores data in DataStores for analysis
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local DataStoreService: DataStoreService = game:GetService("DataStoreService")
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local TAG: string = "Analytics"

-- ============================================================================
-- VARIABLES
-- ============================================================================
local isInitialized: boolean = false
local commandStats: { [string]: Types.CommandStatistics } = {}
local playerAnalytics: { [number]: Types.PlayerAnalytics } = {}
local systemMetrics: Types.SystemMetrics = {
  totalCommandsProcessed = 0,
  totalPlayers = 0,
  averageCommandsPerPlayer = 0,
  mostPopularCommand = nil,
  peakUsageTime = 0,
  systemUptime = os.time(),
  totalErrors = 0,
  successRate = 0,
}
local systemAnalyticsDataStore: DataStore?
local playerAnalyticsDataStore: DataStore?

-- ============================================================================
-- PRIVATE FUNCTIONS
-- ============================================================================

-- Initialize DataStores for analytics
local function initializeDataStores(): boolean
  local middleware = Configuration.Middleware
  if not middleware or not middleware.Analytics then
    Utils.print(TAG, "Analytics configuration missing")
    return false
  end
  local config: Types.AnalyticsConfig = middleware.Analytics

  local success: boolean = pcall(function()
    systemAnalyticsDataStore = DataStoreService:GetDataStore(config.ANALYTICS_DATASTORE_NAME)
    playerAnalyticsDataStore = DataStoreService:GetDataStore(config.PLAYER_ANALYTICS_DATASTORE_NAME)
  end)

  if not success then
    Utils.print(TAG, "Failed to initialize DataStores.")
    return false
  end
  return true
end

-- Save analytics data to DataStores
local function saveAnalytics(): boolean
  if not systemAnalyticsDataStore or not playerAnalyticsDataStore then
    Utils.print(TAG, "DataStores not initialized, skipping save")
    return false
  end

  local systemDataStore: DataStore = assert(systemAnalyticsDataStore, "System DataStore is nil")
  local playerDataStore: DataStore = assert(playerAnalyticsDataStore, "Player DataStore is nil")

  local success: boolean = pcall(function()
    systemDataStore:SetAsync("SystemMetrics", systemMetrics)
    systemDataStore:SetAsync("Analytics", commandStats)
    for userId, analytics in pairs(playerAnalytics) do
      playerDataStore:SetAsync(tostring(userId), analytics)
    end
  end)

  if not success then
    Utils.print(TAG, "Failed to save analytics.")
    return false
  end
  Utils.print(TAG, "Analytics saved successfully")
  return true
end

-- Update command statistics
local function updateCommandStats(commandName: string, success: boolean)
  if not commandStats[commandName] then
    commandStats[commandName] = {
      totalUses = 0,
      uniqueUsers = 0,
      lastUsed = 0,
      firstUsed = os.time(),
      averageArgsCount = 0,
      errorCount = 0,
      successCount = 0,
      averageExecutionTime = nil,
    } :: Types.CommandStatistics
  end

  local stats = commandStats[commandName]
  stats.totalUses += 1
  stats.lastUsed = os.time()
  if success then
    stats.successCount += 1
  else
    stats.errorCount += 1
  end
end

-- Update player analytics
local function updatePlayerAnalytics(player: Player, commandName: string)
  local userId = player.UserId
  if not playerAnalytics[userId] then
    playerAnalytics[userId] = {
      totalCommands = 0,
      uniqueCommands = {},
      firstCommandTime = os.time(),
      lastCommandTime = 0,
      mostUsedCommand = nil,
      averageSessionLength = nil,
      commandsPerSession = nil,
      errorRate = 0,
    } :: Types.PlayerAnalytics
  end

  local analytics = playerAnalytics[userId]
  analytics.totalCommands += 1
  analytics.lastCommandTime = os.time()
  analytics.uniqueCommands[commandName] = (analytics.uniqueCommands[commandName] or 0) + 1
end

-- ============================================================================
-- MIDDLEWARE IMPLEMENTATION
-- ============================================================================
local Analytics = {
  name = TAG,
  priority = 5,
  enabled = true,
  version = "2.0.1",
} :: Types.MiddlewareModule

function Analytics.init(): boolean
  Utils.startTimer(TAG, "AnalyticsInit")

  local middleware = Configuration.Middleware
  if not middleware or not middleware.Analytics then
    Utils.print(TAG, "Analytics configuration missing - disabling module")
    Utils.endTimer(TAG, "AnalyticsInit")
    return false
  end
  local config: Types.AnalyticsConfig = middleware.Analytics

  if not initializeDataStores() then
    Utils.print(TAG, "Analytics DataStores failed to initialize - disabling module")
    Utils.endTimer(TAG, "AnalyticsInit")
    return false
  end

  isInitialized = true

  task.spawn(function()
    while isInitialized do
      task.wait(config.SAVE_INTERVAL)
      saveAnalytics()
    end
  end)

  Utils.endTimer(TAG, "AnalyticsInit")
  Utils.print(TAG, string.format("%s initialized successfully", TAG))
  return true
end

function Analytics.process(
  player: Player,
  commandName: string,
  args: { string }?
): (boolean, string?, any?)
  if not isInitialized then
    return false, "Analytics not initialized", nil
  end

  Utils.startTimer(TAG, "AnalyticsProcess")

  -- Sanitize inputs
  local sanitizedCommand = Utils.sanitize(commandName)
  local sanitizedArgs = {}
  if args then
    for i, arg in ipairs(args) do
      sanitizedArgs[i] = Utils.sanitize(arg)
    end
  end

  -- Update analytics
  updateCommandStats(sanitizedCommand, true)
  updatePlayerAnalytics(player, sanitizedCommand)

  -- Calculate args count
  local argsCount = args and #args or 0
  if commandStats[sanitizedCommand] then
    local stats = commandStats[sanitizedCommand]
    stats.averageArgsCount = (stats.averageArgsCount or 0) * (stats.totalUses - 1)
      + argsCount / stats.totalUses
  end

  -- Update system metrics
  systemMetrics.totalCommandsProcessed += 1
  systemMetrics.totalPlayers = #Players:GetPlayers()
  systemMetrics.averageCommandsPerPlayer = systemMetrics.totalCommandsProcessed
    / math.max(1, systemMetrics.totalPlayers)

  Utils.endTimer(TAG, "AnalyticsProcess")
  return true,
    nil,
    {
      commandStats = commandStats[sanitizedCommand],
      playerAnalytics = playerAnalytics[player.UserId],
    }
end

function Analytics.cleanup()
  Utils.print(TAG, "Cleaning up analytics middleware")
  saveAnalytics()
  commandStats = {}
  playerAnalytics = {}
  isInitialized = false
  systemAnalyticsDataStore = nil
  playerAnalyticsDataStore = nil
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Analytics
