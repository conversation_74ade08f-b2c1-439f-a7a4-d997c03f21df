--!strict

--[[
    - file: MCS_Middleware_Auth.luau

    - version: 2.0.2
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Authentication Middleware for Modular Command System (MCS)
      - Validates player identity and enforces permission-based access control
      - Integrates with PermissionService for centralized authorization
      - Implements proper error handling and performance monitoring
      - Follows MCS middleware contract with standardized return types
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")

-- ============================================================================
-- MODULES
-- ============================================================================
local PermissionService =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PermissionService"))
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local TAG: string = "Authentication"

-- ============================================================================
-- VARIABLES
-- ============================================================================
local isInitialized: boolean = false
local authFailureCount: number = 0
local lastCleanupTime: number = 0

-- Cache for recently validated players to reduce redundant checks
local validationCache: { [number]: { valid: boolean, timestamp: number } } = {}
local CACHE_EXPIRY_TIME = 30 -- seconds

-- ============================================================================
-- PRIVATE FUNCTIONS
-- ============================================================================

-- Clean expired entries from validation cache
local function cleanupCache()
  local currentTime = os.time()

  -- Only cleanup every 60 seconds to avoid excessive processing
  if currentTime - lastCleanupTime < 60 then
    return
  end

  Utils.startTimer("CacheCleanup")

  local expiredKeys: { number } = {}
  for userId, entry in pairs(validationCache) do
    if currentTime - entry.timestamp > CACHE_EXPIRY_TIME then
      table.insert(expiredKeys, userId)
    end
  end

  for _, userId in ipairs(expiredKeys) do
    validationCache[userId] = nil
  end

  lastCleanupTime = currentTime
  Utils.endTimer(TAG, "CacheCleanup")

  if #expiredKeys > 0 then
    Utils.print(TAG, string.format("Cleaned %d expired cache entries", #expiredKeys))
  end
end

-- Validate player object and connection status
local function validatePlayerState(player: Player): (boolean, string?)
  -- Basic null check
  if not player then
    return false, "Player object is nil"
  end

  -- Check if player is still in the game
  if not player.Parent then
    return false, "Player is no longer in the game"
  end

  -- Verify player is actually in Players service
  local foundPlayer = Players:FindFirstChild(player.Name)
  if not foundPlayer or foundPlayer ~= player then
    return false, "Player object is invalid or disconnected"
  end

  -- Check for basic required properties
  if not player.UserId or player.UserId <= 0 then
    return false, "Player has invalid UserId"
  end

  return true, nil
end

-- Check validation cache for recent results
local function checkValidationCache(userId: number): boolean?
  local cached = validationCache[userId]
  if cached and (os.time() - cached.timestamp) <= CACHE_EXPIRY_TIME then
    Utils.print(TAG, string.format("Using cached validation for UserId: %d", userId))
    return cached.valid
  end
  return nil
end

-- Update validation cache
local function updateValidationCache(userId: number, isValid: boolean)
  validationCache[userId] = {
    valid = isValid,
    timestamp = os.time(),
  }
end

-- ============================================================================
-- MIDDLEWARE IMPLEMENTATION
-- ============================================================================
local Authentication = {
  name = TAG,
  priority = 1,
  enabled = true,
  version = "2.0.1",
} :: Types.MiddlewareModule

-- Initialize the authentication middleware
function Authentication.init(): boolean
  Utils.startTimer(TAG, "Initialization")

  -- Verify required services are available
  if not PermissionService then
    Utils.print(TAG, "ERROR: PermissionService not available")
    return false
  end

  -- Check for required PermissionService functions
  if not PermissionService.getPermissionLevel or not PermissionService.canUseCommand then
    Utils.print(TAG, "ERROR: Required PermissionService functions not available")
    return false
  end

  isInitialized = true
  Utils.endTimer(TAG, "AuthInit")
  Utils.print(TAG, "Authentication middleware initialized successfully")

  return true
end

-- Main authentication process
function Authentication.process(player: Player, commandName: string): (boolean, string?, any?)
  if not isInitialized then
    return false, "Authentication middleware not initialized", nil
  end

  Utils.startTimer("AuthProcess")

  -- Periodic cache cleanup
  cleanupCache()

  -- Step 1: Validate player state
  local playerValid, playerError = validatePlayerState(player)
  if not playerValid then
    authFailureCount += 1
    Utils.endTimer(TAG, "AuthProcess")
    Utils.print(TAG, string.format("Player validation failed: %s", playerError or "unknown"))
    return false, "Player validation failed", nil
  end

  -- Step 2: Check validation cache
  local cachedResult = checkValidationCache(player.UserId)
  if cachedResult ~= nil then
    if not cachedResult then
      authFailureCount += 1
    end
    Utils.endTimer(TAG, "AuthProcess")
    return cachedResult, if cachedResult then nil else "Permission denied (cached)", nil
  end

  -- Step 3: Sanitize command name
  local sanitizedCommand = Utils.sanitize(commandName) :: string
  if sanitizedCommand ~= commandName then
    Utils.print(
      TAG,
      string.format("Command name sanitized: '%s' -> '%s'", commandName, sanitizedCommand)
    )
  end

  -- Step 4: Check command permission with timeout protection
  local permissionSuccess, permissionResult = pcall(function()
    return PermissionService.canUseCommand(player, sanitizedCommand)
  end)

  if not permissionSuccess then
    authFailureCount += 1
    Utils.endTimer(TAG, "AuthProcess")
    Utils.print(TAG, string.format("Permission check error: %s", tostring(permissionResult)))
    return false, "Permission system error", nil
  end

  -- Step 5: Process permission result
  local hasPermission = permissionResult

  -- Update cache with result
  updateValidationCache(player.UserId, hasPermission)

  if not hasPermission then
    authFailureCount += 1
    Utils.endTimer(TAG, "AuthProcess")
    Utils.print(
      TAG,
      string.format(
        "Permission denied for %s (%d) using command '%s'",
        player.Name,
        player.UserId,
        sanitizedCommand
      )
    )
    return false, "You don't have permission to use this command", nil
  end

  Utils.endTimer(TAG, "AuthProcess")
  Utils.print(
    TAG,
    string.format(
      "Authentication successful for %s (%d) using command '%s'",
      player.Name,
      player.UserId,
      sanitizedCommand
    )
  )

  return true,
    nil,
    {
      userId = player.UserId,
      commandName = sanitizedCommand,
      permissionLevel = PermissionService.getPermissionLevel(player),
      timestamp = os.time(),
    }
end

-- Cleanup function for middleware shutdown
function Authentication.cleanup()
  Utils.print(TAG, "Cleaning up authentication middleware")

  -- Clear validation cache
  validationCache = {}

  -- Reset counters
  authFailureCount = 0
  lastCleanupTime = 0

  isInitialized = false

  Utils.print(TAG, string.format("Cleanup completed. Total auth failures: %d", authFailureCount))
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Authentication
