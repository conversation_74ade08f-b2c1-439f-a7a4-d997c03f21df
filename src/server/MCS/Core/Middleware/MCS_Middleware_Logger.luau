--!strict

--[[
    - file: MCS_Middleware_Logger.luau

    - version: 2.0.2
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Logger Middleware for Modular Command System (MCS)
      - Records command usage for auditing and debugging purposes
      - Supports multiple logging targets: console, DataStore, and webhooks
      - Implements privacy protection for sensitive commands
      - Provides comprehensive logging with configurable options
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local DataStoreService: DataStoreService = game:GetService("DataStoreService")
local HttpService: HttpService = game:GetService("HttpService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- VARIABLES
-- ============================================================================
local isInitialized: boolean = false
local LoggerConfig: Types.LoggerConfig?
local commandLogStore: DataStore?
local logStats = {
  totalLogs = 0,
  consoleLogs = 0,
  datastoreLogs = 0,
  webhookLogs = 0,
  errors = 0,
  lastLogTime = 0,
}
local MODULE_NAME = "Logger"
local COMMAND_PREFIX = Configuration.Commands.PREFIX

-- ============================================================================
-- PRIVATE FUNCTIONS
-- ============================================================================

-- Validate LoggerConfig
local function validateLoggerConfig(config: any): boolean
  if type(config) ~= "table" then
    return false
  end
  local cfg = config :: { [string]: any }
  return type(cfg.CONSOLE_LOGGING) == "boolean"
    and type(cfg.DATASTORE_LOGGING) == "boolean"
    and type(cfg.WEBHOOK_LOGGING) == "boolean"
    and (cfg.WEBHOOK_URL == nil or type(cfg.WEBHOOK_URL) == "string")
    and type(cfg.DATASTORE_NAME) == "string"
    and type(cfg.MAX_LOG_ENTRIES) == "number"
    and type(cfg.SENSITIVE_COMMANDS) == "table"
    and type(cfg.LOG_RETENTION_DAYS) == "number"
end

-- Parse command name from full command string
local function parseCommandName(fullCommand: string): string
  -- Remove prefix and take first word
  local cleanCommand = fullCommand:gsub("^" .. COMMAND_PREFIX, ""):match("^%w+")
  return cleanCommand or fullCommand
end

-- Check if a command is sensitive
local function isCommandSensitive(commandName: string): boolean
  if not LoggerConfig or not LoggerConfig.SENSITIVE_COMMANDS then
    return false
  end
  local cleanCommand = parseCommandName(commandName):lower()
  return LoggerConfig.SENSITIVE_COMMANDS[cleanCommand] or false
end

-- Create a log entry
local function createLogEntry(
  player: Player,
  commandName: string,
  args: { string }?,
  executionTime: number?
): Types.LogEntry
  local cleanCommand = parseCommandName(commandName)
  local isSensitive = isCommandSensitive(commandName)
  local now = os.time()

  return {
    timestamp = now,
    dateString = tostring(os.date("%Y-%m-%d %H:%M:%S", now)) :: string,
    player = {
      userId = player.UserId,
      name = player.Name,
      displayName = if player.DisplayName ~= player.Name then player.DisplayName :: string else nil,
    },
    command = cleanCommand,
    arguments = if not isSensitive then args else nil,
    sensitive = isSensitive,
    executionTime = executionTime,
  }
end

-- Log to console
local function logToConsole(logEntry: Types.LogEntry)
  if not LoggerConfig or not LoggerConfig.CONSOLE_LOGGING then
    return
  end

  local argsStr = if logEntry.arguments
    then " | Args: " .. table.concat(logEntry.arguments, " ")
    else if logEntry.sensitive then " | Args: [REDACTED]" else ""

  local executionStr = if logEntry.executionTime
    then string.format(" | Time: %.2fms", logEntry.executionTime * 1000)
    else ""

  Utils.print(
    MODULE_NAME,
    string.format(
      "Player: %s (%d) | Command: %s%s%s",
      logEntry.player.name,
      logEntry.player.userId,
      logEntry.command,
      argsStr,
      executionStr
    )
  )

  logStats.consoleLogs += 1
end

--[[
  Appends a log entry to the DataStore for a private server.

  @desc If the DataStore is missing or malformed, safely exits.
        Limits log entries based on configuration.

  @return none
]]
local function logToDataStore(logEntry: Types.LogEntry)
  if not LoggerConfig or not LoggerConfig.DATASTORE_LOGGING or not commandLogStore then
    return
  end

  local logKey = "logs_" .. tostring(os.date("%Y%m%d", logEntry.timestamp))

  task.spawn(function()
    local success = pcall(function()
      if not commandLogStore then
        return -- Type-safe exit if commandLogStore is nil
      end

      local logs: { Types.LogEntry } = {}
      local stored = commandLogStore:GetAsync(logKey)

      if type(stored) == "table" then
        logs = stored :: { Types.LogEntry }
      end

      table.insert(logs, logEntry)

      if LoggerConfig and #logs > LoggerConfig.MAX_LOG_ENTRIES then
        table.sort(logs, function(a: Types.LogEntry, b: Types.LogEntry)
          return a.timestamp > b.timestamp
        end)
        logs = Utils.tableLimit(logs, LoggerConfig.MAX_LOG_ENTRIES) :: { Types.LogEntry }
      end

      commandLogStore:SetAsync(logKey, logs)
    end)

    if success then
      logStats.datastoreLogs += 1
    else
      logStats.errors += 1
      Utils.print(MODULE_NAME, "Failed to save to DataStore")
    end
  end)
end

-- Create webhook embed
local function createWebhookEmbed(logEntry: Types.LogEntry): Types.WebhookPayload
  local fields: { { name: string, value: string, inline: boolean } } = {
    {
      name = "👤 Player",
      value = string.format("%s (%d)", logEntry.player.name, logEntry.player.userId),
      inline = true,
    },
    {
      name = "⚡ Command",
      value = logEntry.command,
      inline = true,
    },
  }

  if logEntry.arguments then
    table.insert(fields, {
      name = "📝 Arguments",
      value = table.concat(logEntry.arguments, " "),
      inline = false,
    })
  elseif logEntry.sensitive then
    table.insert(fields, {
      name = "🔒 Arguments",
      value = "[REDACTED - Sensitive Command]",
      inline = false,
    })
  end

  if logEntry.executionTime then
    table.insert(fields, {
      name = "⏱️ Execution Time",
      value = string.format("%.2f ms", logEntry.executionTime * 1000),
      inline = true,
    })
  end

  return {
    content = "",
    embeds = {
      {
        title = "🔧 Command Executed",
        description = string.format("Command `%s` was executed", logEntry.command),
        color = 0x00FF00,
        timestamp = tostring(os.date("!%Y-%m-%dT%H:%M:%S.000Z", logEntry.timestamp)) :: string,
        fields = fields,
      },
    },
  }
end

--[[
  Sends a webhook notification using the log entry as payload.

  @desc Uses LoggerConfig.WEBHOOK_URL to transmit a formatted message.
        Handles missing configuration gracefully.

  @return none
]]
local function logToWebhook(logEntry: Types.LogEntry)
  if not LoggerConfig or not LoggerConfig.WEBHOOK_LOGGING or not LoggerConfig.WEBHOOK_URL then
    return
  end

  task.spawn(function()
    local success = pcall(function()
      if LoggerConfig ~= nil then
        HttpService:PostAsync(
          LoggerConfig.WEBHOOK_URL :: string,
          HttpService:JSONEncode(createWebhookEmbed(logEntry)),
          Enum.HttpContentType.ApplicationJson
        )
      else
        warn("LoggerConfig is nil. Cannot access WEBHOOK_URL.")
      end
    end)

    if success then
      logStats.webhookLogs += 1
    else
      logStats.errors += 1
      Utils.print(MODULE_NAME, "Failed to send webhook")
    end
  end)
end

-- Perform log cleanup
local function performLogCleanup()
  if not commandLogStore then
    return
  end

  task.spawn(function()
    local cutoff = os.time() - (LoggerConfig :: Types.LoggerConfig).LOG_RETENTION_DAYS * 86400
    Utils.print(
      MODULE_NAME,
      "Cleanup scheduled for logs before " .. tostring(os.date("%Y-%m-%d", cutoff))
    )
    -- Cleanup logic is placed here (if needed, but right now none)
  end)
end

-- ============================================================================
-- MIDDLEWARE IMPLEMENTATION
-- ============================================================================
local Logger = {
  name = MODULE_NAME,
  priority = 4,
  enabled = true,
  version = "2.0.1",
} :: Types.MiddlewareModule

--[[
  Initializes the logger configuration and optionally the DataStore.

  @desc Validates logger configuration and sets global `LoggerConfig`.
        If logging is enabled, initializes the DataStore and starts cleanup routine.

  @return none
]]
function Logger.init(): boolean
  if isInitialized then
    return true
  end

  Utils.startTimer(MODULE_NAME, "LoggerInit")

  local success = pcall(function()
    if not Configuration.Middleware or not Configuration.Middleware.Logger then
      error("Missing Logger configuration")
    end
    local config = Configuration.Middleware.Logger
    if not validateLoggerConfig(config) then
      error("Invalid Logger configuration")
    end
    LoggerConfig = config :: Types.LoggerConfig

    if LoggerConfig.DATASTORE_LOGGING then
      commandLogStore = DataStoreService:GetDataStore(LoggerConfig.DATASTORE_NAME)
      Utils.print(MODULE_NAME, "DataStore initialized")
    end

    task.spawn(function()
      while isInitialized do
        task.wait(86400) -- Daily cleanup
        performLogCleanup()
      end
    end)
  end)

  if not success then
    Utils.print(MODULE_NAME, "Failed to initialize: Unknown error")
    Utils.endTimer(MODULE_NAME, "LoggerInit")
    return false
  end

  isInitialized = true
  Utils.print(MODULE_NAME, "Logger initialized successfully")
  Utils.endTimer(MODULE_NAME, "LoggerInit")
  return true
end

function Logger.process(
  player: Player,
  commandName: string,
  args: { string }?
): (boolean, string?, { logEntry: Types.LogEntry }?)
  if not isInitialized then
    return false, "Logger not initialized", nil
  end

  Utils.startTimer(MODULE_NAME, "LoggerProcess")

  local logEntry = createLogEntry(player, commandName, args)
  logStats.totalLogs += 1
  logStats.lastLogTime = logEntry.timestamp

  logToConsole(logEntry)
  logToDataStore(logEntry)
  logToWebhook(logEntry)

  Utils.endTimer(MODULE_NAME, "LoggerProcess")
  return true, nil, { logEntry = logEntry }
end

function Logger.getStatistics(): { [string]: any }
  return {
    totalLogs = logStats.totalLogs,
    consoleLogs = logStats.consoleLogs,
    datastoreLogs = logStats.datastoreLogs,
    webhookLogs = logStats.webhookLogs,
    errors = logStats.errors,
    lastLogTime = logStats.lastLogTime,
    configuration = LoggerConfig :: Types.LoggerConfig?,
    isInitialized = isInitialized,
  }
end

function Logger.cleanup()
  Utils.print(MODULE_NAME, "Cleaning up logger middleware")
  isInitialized = false
  commandLogStore = nil
  LoggerConfig = nil
end

function Logger.forceCleanup()
  Utils.print(MODULE_NAME, "Force cleanup requested")
  performLogCleanup()
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Logger
