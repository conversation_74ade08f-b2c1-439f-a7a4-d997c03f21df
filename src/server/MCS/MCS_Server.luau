--!strict

--[[
    - file: MCS_Server.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Server initialization script for the Modular Command System (MCS)
      - Loads and initializes all necessary MCS modules in the correct order
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULE LOADING
-- ============================================================================
local CommandDispatcher = require(script.Parent.Core.MCS_CommandDispatcher)
local Middleware = require(script.Parent.Core.Middleware.MCS_Middleware_Initialization)
local PermissionService = require(script.Parent.Core.MCS_PermissionService)
local Remotes = require(ReplicatedStorage:WaitForChild("MCS").MCS_Remotes)
local Utils = require(ReplicatedStorage:WaitForChild("MCS").Shared.MCS_Utils)

-- ============================================================================
-- INITIALIZATION MAIN FUNCTION
-- ============================================================================
local function init()
  Utils.print("MCS_Server_Initialization", "Starting initialization...")

  local success = pcall(PermissionService.init)
  if not success then
    warn("Failed to initialize PermissionService")
  end

  success = pcall(Middleware.init)
  if not success then
    warn("Failed to initialize Middleware")
  end

  success = pcall(CommandDispatcher.init)
  if not success then
    warn("Failed to initialize CommandDispatcher")
  end

  success = pcall(Remotes.init)
  if not success then
    warn("Failed to initialize Remotes")
  end

  Utils.print("MCS_Server_Initialization", "Initialization completed.")
end

-- ============================================================================
-- INITIALIZE THE SYSTEM
-- ============================================================================
init()
