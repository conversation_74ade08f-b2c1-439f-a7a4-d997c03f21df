--!strict

--[[
    - file: CGS_Flags.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Core Game System (CGS) Flags for the NoExploits sub-system.
      - This module manages per-player exploit-related flags, such as speed and jump permissions, to help prevent and track exploit attempts in the game.

    - dependencies:
      - CGS_Types

    - usage:
      local Flags = require(ReplicatedStorage.CGS.NoExploits.Modules.CGS_Flags)

      -- Get or initialize a player's flags
      local exploitFlags = Flags.Get(player)
      print(exploitFlags.CanSpeed) -- false by default

      -- Set a flag
      Flags.Set(player, "CanSpeed", true)

      -- Clear all flags for a player (e.g., on leave)
      Flags.Clear(player)

    - notes:
      - Flags are stored in memory and are not persisted between server sessions.
      - Intended for use by server-side anti-exploit systems.
      - Extend Types.ExploitFlags and Types.ExploitFlagKey in CGS_Types to add more flags as needed.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ReplicatedStorage.CGS.CGS_Types)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Flags = {}

-- ============================================================================
-- MAPPING
-- ============================================================================

--[[
    PlayerFlags: Stores exploit flags for each player.
    Key: Player instance
    Value: ExploitFlags (record of exploit-related boolean flags)
]]
local PlayerFlags: Types.PlayerFlagsMap = {}

-- ============================================================================
-- METHODS
-- ============================================================================

--[[
    Get(player: Player): Types.ExploitFlags
    Retrieves the exploit flags for the given player.
    If the player does not have flags yet, initializes them with default values.

    @param player Player - The player whose flags are being retrieved.
    @return Types.ExploitFlags - The player's exploit flags.
]]
function Flags.Get(player: Player): Types.ExploitFlags
  if not PlayerFlags[player] then
    PlayerFlags[player] = {
      CanSpeed = false,
      CanSuperJump = false,
    }
  end
  return PlayerFlags[player]
end

--[[
    Set(player: Player, key: Types.ExploitFlagKey, value: boolean): ()
    Sets a specific exploit flag for the given player.

    @param player Player - The player whose flag is being set.
    @param key Types.ExploitFlagKey - The flag key to set.
    @param value boolean - The value to assign to the flag.
]]
function Flags.Set(player: Player, key: Types.ExploitFlagKey, value: boolean): ()
  local state = PlayerFlags[player]
  if state then
    -- Cast to dictionary to allow dynamic access
    (state :: { [string]: boolean })[key] = value
  end
end

--[[
    Clear(player: Player): ()
    Removes all exploit flags for the given player.

    @param player Player - The player whose flags are being cleared.
]]
function Flags.Clear(player: Player): ()
  PlayerFlags[player] = nil
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Flags
