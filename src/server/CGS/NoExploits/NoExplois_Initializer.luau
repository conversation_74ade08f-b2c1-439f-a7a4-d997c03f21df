--!strict

--[[
    - file: NoExploits_Initializer.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Core Game System (CGS) NoExploits Entry point
      ]]

-- ============================================================================
-- MODULES
-- ============================================================================
local AntiExploitService = require(script.Parent.Security:WaitForChild("CGS_AntiExploitService"))

-- ============================================================================
-- INITIALIZATION
-- ============================================================================
AntiExploitService.Start()
