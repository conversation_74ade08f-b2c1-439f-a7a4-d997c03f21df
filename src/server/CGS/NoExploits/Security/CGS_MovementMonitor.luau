--!strict

--[[
    - file: CGS_MovementMonitor.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Core Game System (CGS) NoExploits Movement Monitor with performance optimization.
      - Monitors player movement and enforces anti-exploit validation using Validator and Flags modules.
      - Resets player position on detected violations unless the player is flagged as allowed.

    - dependencies:
      - CGS_Configuration
      - CGS_Flags
      - CGS_Validator

    - usage:
      local Monitor = require(ReplicatedStorage.CGS.NoExploits.Security.CGS_MovementMonitor)
      Monitor.Start()

    - notes:
      - Designed for use by server-side anti-exploit systems.
      - Uses performance optimizations to limit checks per frame.
      - Extend or adjust logic to add new movement validation types.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration =
  require(ReplicatedStorage.Configurations.Systems:WaitForChild("CGS_Configuration"))
local Flags = require(script.Parent.Parent.Modules.CGS_Flags)
local Validator = require(script.Parent.CGS_Validator)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Monitor = {}

-- ============================================================================
-- VARIABLES
-- ============================================================================

-- Last known valid positions per player
local LastValidPositions: { [Player]: CFrame } = {}

-- Performance optimization variables
--[[
    Index of the next player to check in the validation loop.
    Controls which player is processed in the current frame to stagger checks.
    
    @type number
    @default 1 (from configuration)
]]
local playerCheckIndex: number = Configuration.Config.AntiExploit.Detection.playerCheckIndex or 1

--[[
    Maximum number of players to check per frame.
    Limits performance impact by spreading checks across frames.
    
    @type number
    @default 10 (from configuration)
]]
local maxChecksPerFrame: number = Configuration.Config.AntiExploit.Detection.maxChecksPerFrame or 10

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

--[[
    Monitor.Start(): ()
    Starts the movement monitoring loop. Checks a limited number of players per frame for violations.
    Resets player position if violations are detected and the player is not flagged as allowed.

    @return ()
]]
-- Start monitoring
function Monitor.Start()
  RunService.Heartbeat:Connect(function()
    local players = Players:GetPlayers()
    if #players == 0 then
      return
    end

    local checksThisFrame = 0
    local playersChecked = 0

    while checksThisFrame < maxChecksPerFrame and playersChecked < #players do
      if playerCheckIndex > #players then
        playerCheckIndex = 1
      end

      local player = players[playerCheckIndex]
      playerCheckIndex += 1
      playersChecked += 1

      if player and player.Character then
        local char = player.Character :: Model
        local hrpInstance = char:WaitForChild("HumanoidRootPart") :: BasePart
        local hum = char:WaitForChild("Humanoid") :: Humanoid

        if hrpInstance and hrpInstance:IsA("BasePart") and hum then
          local hrp = hrpInstance :: BasePart
          local state = Flags.Get(player)

          if not LastValidPositions[player] then
            LastValidPositions[player] = hrp.CFrame
          end

          local violations = Validator.ValidatePlayer(player)
          if #violations > 0 and (not state.CanSpeed or not state.CanSuperJump) then
            hrp.CFrame = LastValidPositions[player]
          else
            LastValidPositions[player] = hrp.CFrame
          end

          checksThisFrame += 1
        end
      end
    end
  end)
end

-- Clean up player data when they leave
Players.PlayerRemoving:Connect(function(player)
  LastValidPositions[player] = nil
end)

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Monitor
