--!strict

--[[
    - file: CGS_Validator.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Core Game System (CGS) NoExploits sub-system Validator.
      - This module provides anti-exploit validation functions for the CGS system, including speed, jump, and Y-kill checks.
      - Tracks per-player movement and state to detect and flag suspicious behavior.

    - dependencies:
      - CGS_Configuration

    - usage:
      local Validator = require(ReplicatedStorage.CGS.NoExploits.Security.CGS_Validator)

      -- Validate a player (returns a list of violation strings)
      local violations = Validator.ValidatePlayer(player)
      if #violations > 0 then
        print("Player violations:", violations)
      end

      -- Get a player's current violation count
      local count = Validator.GetPlayerViolations(player)
      print("Violations:", count)

    - notes:
      - Per-player data is stored in memory and reset on player leave.
      - Designed for use by server-side anti-exploit systems.
      - Extend or adjust detection logic in this module to add new validation types.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local CGS_Configuration = require(ReplicatedStorage.Configurations.Systems.CGS_Configuration)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Validator = {}

-- ============================================================================
-- DATA & TRACKING
-- ============================================================================

-- Per-player tracking with circular buffers for performance
local playerData: {
  [Player]: {
    -- Position tracking
    positionHistory: { Vector3 },
    timeHistory: { number },
    historyIndex: number,

    -- State tracking
    jumpCount: number,
    lastJumpTime: number,

    -- Violation system
    violations: number,
    lastViolationTime: number,
  },
} =
  {}

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Initialize player data with optimized structures
local function initializePlayerData(player: Player): ()
  playerData[player] = {
    positionHistory = {},
    timeHistory = {},
    historyIndex = 1,
    lastGrounded = tick(),
    airTime = 0,
    jumpCount = 0,
    lastJumpTime = 0,
    violations = 0,
    lastViolationTime = 0,
  }
end

-- Calculate average velocity with lag compensation
local function calculateAverageVelocity(player: Player): number
  local data = playerData[player]
  if not data or #data.positionHistory < 2 then
    return 0
  end

  local totalDistance = 0
  local totalTime = 0
  local samples =
    math.min(#data.positionHistory, CGS_Configuration.Config.AntiExploit.Detection.Speed.SampleSize)

  for i = 1, samples - 1 do
    local currentIdx = ((data.historyIndex - i - 1) % samples) + 1
    local nextIdx = ((data.historyIndex - i) % samples) + 1

    if data.positionHistory[currentIdx] and data.positionHistory[nextIdx] then
      totalDistance += (data.positionHistory[nextIdx] - data.positionHistory[currentIdx]).Magnitude
      totalTime += data.timeHistory[nextIdx] - data.timeHistory[currentIdx]
    end
  end

  return if totalTime > 0 then totalDistance / totalTime else 0
end

-- Update violation count with decay
local function updateViolations(player: Player, increment: number): ()
  local data = playerData[player]
  if not data then
    return
  end

  local currentTime = tick()
  local timeDelta = currentTime - data.lastViolationTime

  -- Apply decay
  data.violations = math.max(
    0,
    data.violations - (timeDelta * CGS_Configuration.Config.AntiExploit.Detection.Violation.Decay)
  )
  data.violations += increment
  data.lastViolationTime = currentTime
end

-- ==========================================================================
-- VALIDATION FUNCTIONS
-- ==========================================================================

--[[
    IsSpeeding(player: Player, hrp: BasePart): boolean
    Checks if the player is moving faster than allowed, using average velocity and lag compensation.
    Updates the player's position history for speed checks.

    @param player Player - The player to check.
    @param hrp BasePart - The player's HumanoidRootPart.
    @return boolean - True if the player is speeding, false otherwise.
]]
function Validator.IsSpeeding(player: Player, hrp: BasePart): boolean
  if not CGS_Configuration.Config.AntiExploit.Toggles.EnableSpeedCheck then
    return false
  end
  if not hrp then
    return false
  end

  local data = playerData[player]
  if not data then
    initializePlayerData(player)
    data = playerData[player]
  end

  -- Update position history (circular buffer)
  local currentTime = tick()
  data.historyIndex = (
    data.historyIndex % CGS_Configuration.Config.AntiExploit.Detection.Speed.SampleSize
  ) + 1
  data.positionHistory[data.historyIndex] = hrp.Position
  data.timeHistory[data.historyIndex] = currentTime

  -- Calculate average velocity
  local avgVelocity = calculateAverageVelocity(player)
  local threshold = CGS_Configuration.Config.AntiExploit.Movement.SpeedThreshold
    * CGS_Configuration.Config.AntiExploit.Detection.Speed.ToleranceMultiplier

  return avgVelocity > threshold
end

--[[
    IsSuperJumping(player: Player, hrp: BasePart, humanoid: Humanoid): boolean
    Checks if the player is performing a super jump (exceeding jump height or too many jumps).
    Tracks jump count and cooldowns.

    @param player Player - The player to check.
    @param hrp BasePart - The player's HumanoidRootPart.
    @param humanoid Humanoid - The player's Humanoid instance.
    @return boolean - True if the player is super jumping, false otherwise.
]]
function Validator.IsSuperJumping(player: Player, hrp: BasePart, humanoid: Humanoid): boolean
  if not CGS_Configuration.Config.AntiExploit.Toggles.EnableJumpCheck then
    return false
  end
  if not hrp or not humanoid then
    return false
  end

  local data = playerData[player]
  if not data then
    initializePlayerData(player)
    data = playerData[player]
  end

  local currentTime = tick()
  local isJumping = humanoid:GetState() == Enum.HumanoidStateType.Jumping

  -- Track consecutive jumps
  if
    isJumping
    and (currentTime - data.lastJumpTime)
      > CGS_Configuration.Config.AntiExploit.Detection.Jump.Cooldown
  then
    data.jumpCount += 1
    data.lastJumpTime = currentTime
  elseif humanoid.FloorMaterial ~= Enum.Material.Air then
    data.jumpCount = 0
  end

  -- Check height and consecutive jumps
  local exceedsHeight = hrp.Position.Y > CGS_Configuration.Config.AntiExploit.Movement.JumpHeightMax
  local tooManyJumps = data.jumpCount
    > CGS_Configuration.Config.AntiExploit.Detection.Jump.MaxConsecutiveJumps

  return exceedsHeight or tooManyJumps
end

-- ============================================================================
-- VALIDATE PLAYER
-- ============================================================================

--[[
    ValidatePlayer(player: Player): { string }
    Runs all validation checks for the player and returns a list of violation strings.
    Handles Y-kill, speed, and super jump checks.

    @param player Player - The player to validate.
    @return { string } - List of violation types detected for the player.
]]
function Validator.ValidatePlayer(player: Player): { string }
  if
    not player
    or not player.Character
    or not player.Character:FindFirstChild("HumanoidRootPart")
  then
    return {}
  end

  local character = player.Character
  local hrp = character:WaitForChild("HumanoidRootPart") :: BasePart
  local humanoid = character:FindFirstChildOfClass("Humanoid")

  if not humanoid then
    return {}
  end

  local violations = {}

  if
    CGS_Configuration.Config.AntiExploit.Toggles.EnableYKillCheck
    and hrp.Position.Y < CGS_Configuration.Config.AntiExploit.Detection.KillYThreshold
  then
    humanoid.Health = 0
    table.insert(violations, "FellBelowMap")
    return violations
  end

  if Validator.IsSpeeding(player, hrp) then
    table.insert(violations, "Speed")
    updateViolations(player, 1)
  end

  if Validator.IsSuperJumping(player, hrp, humanoid) then
    table.insert(violations, "SuperJump")
    updateViolations(player, 1.5)
  end

  return violations
end

--[[
    GetPlayerViolations(player: Player): number
    Returns the current violation count for the player (with decay applied).

    @param player Player - The player to query.
    @return number - The player's violation count.
]]
function Validator.GetPlayerViolations(player: Player): number
  local data = playerData[player]
  return data and data.violations or 0
end

-- Clean up player data when they leave
Players.PlayerRemoving:Connect(function(player)
  playerData[player] = nil
end)

-- Initialize existing players
for _, player in Players:GetPlayers() do
  initializePlayerData(player)
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Validator
