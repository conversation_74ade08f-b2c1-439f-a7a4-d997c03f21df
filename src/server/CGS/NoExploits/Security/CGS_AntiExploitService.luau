--!strict

--[[
    - file: CGS_AntiExploitService.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Core Game System (CGS) Anti-exploit Service.
      - Initializes and manages the NoExploits sub-system, including player flag management and movement monitoring.
      - Should be required by the NoExploits_Initializer to activate anti-exploit protections.

    - dependencies:
      - CGS_Flags
      - CGS_MovementMonitor

    - usage:
      local AntiExploitService = require(ReplicatedStorage.CGS.NoExploits.Security.CGS_AntiExploitService)
      AntiExploitService.Start()

    - notes:
      - Designed for use by server-side anti-exploit systems.
      - Automatically manages player flags and starts movement monitoring on initialization.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")

-- ============================================================================
-- PATHS
-- ============================================================================
local mainPath = script.Parent

-- ============================================================================
-- MODULES
-- ============================================================================
local Flags = require(mainPath.Parent.Modules:WaitForChild("CGS_Flags"))
local Monitor = require(mainPath:WaitForChild("CGS_MovementMonitor"))

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local AntiExploitService = {}

--[[
    AntiExploitService.Start(): ()
    Initializes the anti-exploit service, sets up player flag management, and starts the movement monitor.
    Should be called once on server startup by the NoExploits_Initializer.

    @return ()
]]
-- Initialize the anti exploit service
function AntiExploitService.Start()
  -- When a player joins/spawn, set and get the default flags
  Players.PlayerAdded:Connect(function(player)
    Flags.Get(player)
  end)

  -- When a player leaves the game, clear the default flags
  Players.PlayerRemoving:Connect(function(player)
    Flags.Clear(player)
  end)

  -- Start Monitoring
  Monitor.Start()
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return AntiExploitService
