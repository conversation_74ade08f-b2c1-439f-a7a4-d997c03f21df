--!strict

--[[
    - file: MAFS_Server.luau

    - version: 1.1.1
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Server-side implementation for the MAFS (Material Audio Footstep System).
      - Handles player movement detection, material resolution, and sound broadcasting.
      - Integrates with performance monitoring and centralized configuration.

    - dependencies:
      - MAFS_Types
      - MAFS_Utils
      - MAFS_Remotes
      - MAFS_Configuration

    - notes:
      - Manages footstep detection and broadcasting to all clients.
      - Uses centralized configuration and proper logging throughout.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- PATHS
-- ============================================================================
local mainPath = ReplicatedStorage:WaitForChild("MAFS")

-- ============================================================================
-- MODULES
-- ============================================================================
local Config = require(ReplicatedStorage.Configurations.Systems:WaitForChild("MAFS_Configuration"))
local Remotes = require(mainPath:WaitForChild("MAFS_Remotes"))
local Types = require(mainPath:WaitForChild("MAFS_Types"))
local Utils = require(mainPath.Shared:WaitForChild("MAFS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
-- Base time (seconds) between footstep checks
local STEP_INTERVAL_BASE: number = 0.4

-- Minimum speed factor applied to intervals
local SPEED_FACTOR_MIN: number = 0.5

-- Maximum speed factor applied to intervals
local SPEED_FACTOR_MAX: number = 2.0

-- Divisor to normalize speed factor
local SPEED_DIVISOR: number = 10

-- Identifier tag for logging
local TAG: string = "Server"

-- ============================================================================
-- TYPES
-- ============================================================================
-- Aliases for configuration and footstep state types.
type MAFSConfiguration = Types.MAFSConfiguration
type PlayerFootstepState = Types.PlayerFootstepState

-- ============================================================================
-- VARIABLES
-- ============================================================================
-- Who’s currently playing a footstep sound
local footstepSoundsPlaying: { [Player]: boolean } = {}

-- Last floor material per player
local lastMaterial: { [Player]: Enum.Material } = {}

-- Timestamp of last footprint per player
local lastFootprintTime: { [Player]: number } = {}

-- Performance monitor
local performanceMonitor: Types.PerformanceMonitor

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

--[[
  Checks if the given player is moving based on velocity, movement direction,
  and floor material.

  @params humanoid 	-- The character’s Humanoid component
  @params root   	-- The HumanoidRootPart for velocity checks

  @returns boolean  -- True if player is moving; false otherwise
]]
local function isPlayerMoving(humanoid: Humanoid, root: BasePart): boolean
  local velocity = root.AssemblyLinearVelocity.Magnitude
  local moveDirMag = humanoid.MoveDirection.Magnitude
  local material = humanoid.FloorMaterial

  return velocity > Config.Config.velocityThreshold
    and moveDirMag > 0
    and material ~= Enum.Material.Air
end

--[[
  Retrieves the corresponding footstep sound name for the given material.

  @params material Enum.Material   -- The material underfoot

  @returns string                  -- Sound identifier for the material
]]
local function getSoundForMaterial(material: Enum.Material): string
  return Config.getSoundForMaterial(material)
end

--[[
  Broadcasts looped footstep sound events to nearby players,
  handling start, update, and stop actions.

  @params player	-- The source player triggering the event
  @params action   	-- Footstep action type
  @params soundName -- Optional sound name for Start/Update

  @returns nil
]]
local function broadcastFootstepLoop(
  player: Player,
  action: "Start" | "Update" | "Stop",
  soundName: string?
): ()
  for _, other in Players:GetPlayers() do
    if other ~= player then
      local otherChar = other.Character
      local otherRoot = otherChar and otherChar:FindFirstChild("HumanoidRootPart") :: BasePart?
      local myRoot = player.Character
        and player.Character:FindFirstChild("HumanoidRootPart") :: BasePart?
      if
        otherRoot
        and myRoot
        and Utils.isWithinRange(otherRoot.Position, myRoot.Position, Config.Config.soundRadius)
      then
        if action == "Start" and soundName then
          Remotes.StartFootstepSound:FireClient(other, soundName)
        elseif action == "Update" and soundName then
          Remotes.UpdateFootstepSound:FireClient(other, soundName)
        elseif action == "Stop" then
          Remotes.StopFootstepSound:FireClient(other)
        end
      end
    end
  end
  if performanceMonitor then
    performanceMonitor:recordNetworkRequest()
  end
end

--[[
  Handles footstep sounds for the player and broadcasts appropriately.

  @params player	-- The player whose footstep sounds are managed
  @params isMoving  -- Whether the player is currently moving
  @params material  -- The floor material under the player
  @params soundName  The sound identifier for this material

  @returns nil
]]
local function handleFootstepSounds(
  player: Player,
  isMoving: boolean,
  material: Enum.Material,
  soundName: string
): ()
  if not Config.Config.enableFootsteps then
    return
  end

  local prevMaterial = lastMaterial[player]

  if isMoving then
    lastMaterial[player] = material

    if not footstepSoundsPlaying[player] then
      footstepSoundsPlaying[player] = true

      -- Local client
      Remotes.StartFootstepSound:FireClient(player, soundName)
      Utils.log(TAG, `Started footstep sound for {player.Name}: {soundName}`)

      -- Others
      broadcastFootstepLoop(player, "Start", soundName)
    elseif prevMaterial and prevMaterial ~= material then
      -- Local client
      Remotes.UpdateFootstepSound:FireClient(player, soundName)
      Utils.log(TAG, `Updated footstep sound for {player.Name}: {soundName}`)

      -- Others
      broadcastFootstepLoop(player, "Update", soundName)
    end
  else
    if footstepSoundsPlaying[player] then
      footstepSoundsPlaying[player] = nil

      -- Local client
      Remotes.StopFootstepSound:FireClient(player)
      Utils.log(TAG, `Stopped footstep sound for {player.Name}`)

      -- Others
      broadcastFootstepLoop(player, "Stop", nil)
    end
    lastMaterial[player] = nil
    lastFootprintTime[player] = nil
  end
end

--[[
  Handles footprint replication for moving players based on interval and distance.

  @params player 	-- The player leaving a footprint
  @params root 		-- The player's HumanoidRootPart for position
  @params material 	-- The floor material for footprint type
  @params velocity 	-- The player's current speed magnitude
  @params now 		-- The current timestamp for interval checks

  @returns nil
]]
local function handleFootprints(
  player: Player,
  root: BasePart,
  material: Enum.Material,
  velocity: number,
  now: number
): ()
  if not Config.Config.enableFootprints then
    return
  end

  local lastTime = lastFootprintTime[player] or 0
  local speedFactor =
    Utils.clampNumber(velocity / SPEED_DIVISOR, SPEED_FACTOR_MIN, SPEED_FACTOR_MAX)
  local interval = STEP_INTERVAL_BASE / speedFactor

  if now - lastTime >= interval then
    lastFootprintTime[player] = now
    local footprintName = Config.getFootprintForMaterial(material)

    if footprintName then
      for _, other in Players:GetPlayers() do
        local otherChar = other.Character
        local otherRoot = otherChar and otherChar:FindFirstChild("HumanoidRootPart") :: BasePart?

        if
          otherRoot
          and Utils.isWithinRange(otherRoot.Position, root.Position, Config.Config.soundRadius)
        then
          Remotes.ReplicateFootprint:FireClient(other, player.Name, root.Position, footprintName)
          Remotes.ReplicateFootprint:FireClient(other, player.Name, root.Position, footprintName)
        end
      end

      if performanceMonitor then
        performanceMonitor:recordFootprintCreation()
      end
    end
  end
end

--[[
  Handles 3D footstep sound replication to nearby players.

  @params player 	-- The source player for 3D sound
  @params action 	-- "Start", "Update", or "Stop" action type
  @params soundName -- The sound identifier to replicate

  @returns nil
]]
local function handle3DSounds(player: Player, action, soundName: string): ()
  if not Config.Config.enableFootsteps then
    return
  end

  for _, other in Players:GetPlayers() do
    if other ~= player then
      local otherRoot = other.Character and other.Character:FindFirstChild("HumanoidRootPart")
      local otherRootPart = otherRoot and (otherRoot :: BasePart)
      local playerRootPart = player.Character
        and player.Character:FindFirstChild("HumanoidRootPart")
      if
        otherRootPart
        and playerRootPart
        and Utils.isWithinRange(
          otherRootPart.Position,
          (playerRootPart :: BasePart).Position,
          Config.Config.soundRadius
        )
      then
        if action == "Start" then
          Remotes.StartFootstepSound:FireClient(other, soundName)
        elseif action == "Update" then
          Remotes.UpdateFootstepSound:FireClient(other, soundName)
        elseif action == "Stop" then
          Remotes.StopFootstepSound:FireClient(other)
        end
      end
    end
  end

  if performanceMonitor then
    performanceMonitor:recordNetworkRequest()
  end
end

--[[
  Processes a single player's footstep logic: movement detection,
  sound handling, footprint replication, and 3D sound updates.

  @params player	-- The player to process
  @params now 		-- The current timestamp

  @returns nil
]]
local function processPlayerFootsteps(player: Player, now: number): ()
  local character = player.Character
  if not character then
    return
  end

  local humanoid = character:FindFirstChildOfClass("Humanoid")
  local root = character:FindFirstChild("HumanoidRootPart") :: BasePart?
  if not humanoid or not root then
    return
  end

  local isMoving = isPlayerMoving(humanoid, root)
  local material = humanoid.FloorMaterial
  local soundName = getSoundForMaterial(material)
  local velocity = root.AssemblyLinearVelocity.Magnitude

  -- Handle footstep sounds
  handleFootstepSounds(player, isMoving, material, soundName)

  -- Handle footprints and 3D sounds only if moving
  if isMoving then
    handleFootprints(player, root, material, velocity, now)
    handle3DSounds(player, "Update", soundName)
  end
end

-- Clean up player data when they leave
local function onPlayerRemoving(player: Player): ()
  footstepSoundsPlaying[player] = nil
  lastMaterial[player] = nil
  lastFootprintTime[player] = nil
  Utils.log(TAG, `Cleaned up MAFS data for {player.Name}`)
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

--[[
  Initializes the performance monitoring system if enabled in the configuration.

  @returns nil
]]
local function initializePerformanceMonitor(): ()
  if Config.isPerformanceMonitorEnabled() then
    local PerformanceMonitor =
      require(mainPath:WaitForChild("Shared"):WaitForChild("MAFS_PerformanceMonitor"))
    performanceMonitor = PerformanceMonitor.new(Config.Config)
    Utils.log(TAG, "Performance monitor initialized on server")
  else
    Utils.log(TAG, "Performance monitor disabled on server")
  end
end

--[[
  Sets up the MAFS server: performance reporting, event bindings, and periodic reports.

  @returns nil
]]
local function initializeServer(): ()
  Utils.log(TAG, "Initializing MAFS server...")

  -- Initialize performance monitoring
  initializePerformanceMonitor()

  -- Connect player cleanup
  Players.PlayerRemoving:Connect(onPlayerRemoving)

  -- Generate initial performance report if monitoring is on
  if performanceMonitor and performanceMonitor:isEnabled() then
    local monitor = performanceMonitor :: Types.PerformanceMonitor
    task.wait(10) -- Wait 10 seconds before first report
    local initialReport = monitor:generateReport()
    Utils.log(initialReport, "INFO")

    -- Start periodic reporting every 2 minutes
    task.spawn(function()
      while true do
        task.wait(120) -- 2 minutes
        local report = monitor:generateReport()
        Utils.log(report, "INFO")
      end
    end)
  end

  Utils.log(TAG, "MAFS server initialized successfully")
end

-- ============================================================================
-- MAIN LOOP
-- ============================================================================

--[[
  Main loop: spawns the server initialization and processes player footsteps
  on every update tick defined in configuration.

  @returns nil
]]
task.spawn(function()
  -- Initialize server once at start
  initializeServer()

  while true do
    task.wait(Config.Config.updateInterval)
    local now = os.clock()

    for _, player in Players:GetPlayers() do
      processPlayerFootsteps(player, now)
    end
  end
end)
