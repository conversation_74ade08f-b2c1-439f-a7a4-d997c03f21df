--!strict

--[[
	- file: DCPS_Server.luau

	- version: 1.0.0
	- author: BleckWolf25
	- contributors:

	- copyright: Dynamic Innovative Studio

	- description:
        - Server entry point for the Diurnal Cycle Processor System (DCPS).
        - Orchestrates time‐of‐day segment detection, lighting transitions, and remote events.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Lighting: Lighting = game:GetService("Lighting")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Config = require(ReplicatedStorage.Configurations.Systems:WaitForChild("DCPS_Configuration"))
local CycleLogic = require(ServerStorage.DCPS.Shared:WaitForChild("DCPS_CycleDayNight"))
-- local LightController = require(ServerStorage.DCPS.Shared:WaitForChild("DCPS_LightController"))
local Remotes = require(ServerStorage.DCPS:WaitForChild("DCPS_Remotes"))
local Types = require(ServerStorage.DCPS:WaitForChild("DCPS_Types"))
local Utils = require(ServerStorage.DCPS.Shared:WaitForChild("DCPS_Utils"))

-- ============================================================================
-- VARIABLES & CONSTANTS
-- ============================================================================
-- Tag used for console logs
local TAG: string = "Server"

-- Store the entire active segment
local activeSegment: Types.CycleSegment? = nil

-- Flatten configuration map into an array for iteration
local segments: { Types.CycleSegment } = {}

-- ============================================================================
-- SEGMENT UTILITIES
-- ============================================================================

-- Convert segments to array
for _, segment in pairs(Config.CycleSegments :: { [string]: Types.CycleSegment }) do
  table.insert(segments, segment)
end

-- Sort by start time
table.sort(segments, function(a: Types.CycleSegment, b: Types.CycleSegment): boolean
  return a.Definition.StartTime < b.Definition.StartTime
end)

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================
Utils.log(TAG, "Initializing DCPS Server...")
-- LightController.Start()

--[[
    Determines which configured segment the current time falls into.

    @param currentTime number — current time in hours (0–24)
    @return Types.CycleSegment? — matching segment or nil if none
]]
local function evaluateTimeSegment(currentTime: number): Types.CycleSegment?
  for _, segment: Types.CycleSegment in ipairs(segments) do
    local def = segment.Definition
    if Utils.isTimeInSegment(currentTime, def.StartTime, def.EndTime) then
      return segment
    end
  end
  return nil
end

--[[
    Handles everything needed when the time segment rolls over.

    @param newSegment Types.CycleSegment — segment that just became active
]]
local function onSegmentChanged(newSegment: Types.CycleSegment)
  local success = pcall(function()
    activeSegment = newSegment
    local name = newSegment.Definition.Name

    Utils.log(TAG, "Time Segment Changed →", name)

    -- Fire server-side event
    Remotes.FireTimeSegment(name)

    -- Apply lighting with error handling
    local lightingSuccess = pcall(function()
      Utils.applyLightingTransition(Lighting, newSegment.Lighting, Config.TransitionTime)
    end)

    if not lightingSuccess then
      Utils.warn(TAG, "Lighting transition failed")
    end
  end)

  if not success then
    Utils.warn(TAG, "Segment change handler failed")
  end
end

-- ============================================================================
-- MAIN LOOP WITH ERROR HANDLING
-- ============================================================================
local function safeTickLoop()
  while true do
    local success = pcall(function()
      local rawTimeStr: string = Lighting.TimeOfDay
      local timeNow: number = Utils.timeStringToNumber(rawTimeStr)

      local currentSegment: Types.CycleSegment? = evaluateTimeSegment(timeNow)
      if
        currentSegment
        and (not activeSegment or currentSegment.Definition.Name ~= activeSegment.Definition.Name)
      then
        onSegmentChanged(currentSegment)
      end
    end)

    if not success then
      Utils.warn(TAG, "Main loop error")
    end

    task.wait(Config.UpdateInterval)
  end
end

-- ============================================================================
-- INITIALIZATION WITH ERROR HANDLING
-- ============================================================================
local function initialize()
  -- Set initial time segment
  local rawTimeStr: string = Lighting.TimeOfDay
  local timeNow: number = Utils.timeStringToNumber(rawTimeStr)
  local initialSegment = evaluateTimeSegment(timeNow)

  if initialSegment then
    onSegmentChanged(initialSegment)
    Utils.log(TAG, "Initial time segment:", initialSegment.Definition.Name)
  else
    Utils.warn(TAG, "No initial time segment found for time:", timeNow)
  end

  -- Start systems
  task.spawn(safeTickLoop)

  local cycleSuccess = pcall(function()
    CycleLogic.Start()
  end)

  if not cycleSuccess then
    Utils.warn(TAG, "Cycle logic failed to start")
  end

  Utils.log(TAG, "DCPS Server started successfully")
end

-- Protected initialization
local success = pcall(initialize)
if not success then
  Utils.warn(TAG, "Initialization failed")
end
