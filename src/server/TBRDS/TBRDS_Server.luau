--!strict

--[[
    - file: TBRDS_Server.luau
    
    - version: 3.0.0
    - author: BleckWolf25
    - contributors:
    
    - copyright: Dynamic Innovative Studio
    
    - description:
        - The entry point of TBRDS, sets up services, handles player roles, and integrates with other systems.
        - It includes error handling, performance monitoring, and admin commands for system management.
]]

-- =============================================================================
-- SERVICES
-- =============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")

-- =============================================================================
-- MODULES
-- =============================================================================
local TBRDSRemotes = require(ReplicatedStorage.TBRDS.TBRDS_Remotes)
local Types = require(ReplicatedStorage.TBRDS.TBRDS_Types)
local Utils = require(ReplicatedStorage.TBRDS.Shared.TBRDS_Utils)

-- =============================================================================
-- CONSTANTS
-- =============================================================================
local TAG: string = "Server"

-- =============================================================================
-- VARIABLES
-- =============================================================================

-- Do NOT assign values to those variables
local EventSystem
local PerformanceMonitor

local ServiceManager: Types.ServiceManager
local isSystemRunning: boolean = false
local startupTime: number = 0

-- =============================================================================
-- PRIVATE METHODS
-- =============================================================================

--[[
    Initializes the TBRDS by loading services and remote events.
    
    @returns boolean True if initialization succeeds, false otherwise.
]]
local function initializeSystem(): boolean
  Utils.log(TAG, "Starting TBRDS system initialization...")
  startupTime = tick()

  if not TBRDSRemotes.InitializeRemotes() then
    Utils.warn(TAG, "Failed to initialize remote events")
    return false
  end

  if not ServiceManager.Initialize() then
    Utils.warn(TAG, "Failed to initialize service manager")
    return false
  end

  if not ServiceManager.AreAllServicesHealthy() then
    Utils.warn(TAG, "Not all services are healthy after initialization")
    return false
  end

  local initTime: number = (tick() - startupTime) * 1000
  Utils.log(TAG, "Initialized successfully in " .. initTime .. "ms")
  return true
end

--[[
    Sets up system monitoring, including admin commands via chat.
    
    @returns nil
]]
local function setupSystemMonitoring(): ()
  local function handleAdminCommands(player: Player, message: string): ()
    local configService = ServiceManager.GetService("Configuration") :: Types.ConfigurationService?
    if not configService then
      return
    end

    local groupId: number = configService.GetGroups().Primary.Id
    local success, rank = pcall(function()
      return player:GetRankInGroup(groupId)
    end)
    if not success or rank < 252 then
      return
    end

    local command: string = string.lower(message)
    if command == "/tbrds status" then
      local report: string = ServiceManager.GetSystemHealthReport()
      print("=== TBRDS Status Report ===")
      print(report)
    elseif command == "/tbrds restart" then
      Utils.log(TAG, "System restart requested by " .. player.Name)
      if ServiceManager.EmergencyRecovery() then
        Utils.log(TAG, "System restart completed successfully")
      else
        Utils.warn(TAG, "System restart failed")
      end
    elseif command == "/tbrds metrics" then
      if not PerformanceMonitor then
        Utils.warn(TAG, "PerformanceMonitor is nil")
        return
      end
      local metrics = PerformanceMonitor.GetDetailedMetrics() :: Types.DetailedMetricsResult
      print("=== TBRDS Performance Metrics ===")
      print("Uptime: " .. metrics.uptime .. " seconds")
      print("Tag Assignments: " .. metrics.basic.TagAssignments)
      print("Cache Hit Rate: " .. (metrics.cacheHitRate * 100) .. "%")
      print("Average Validation Time: " .. metrics.averageValidationTime .. "ms")
      print("Error Rate: " .. metrics.errorRate .. " errors/second")
    elseif command == "/tbrds refresh" then
      Utils.log(TAG, "Tag refresh requested by " .. player.Name)
      local tagService = ServiceManager.GetService("Tag") :: Types.TagService?
      if tagService then
        local refreshedCount: number = 0
        for _, targetPlayer: Player in ipairs(Players:GetPlayers()) do
          if targetPlayer.Parent then
            tagService.RefreshPlayerTag(targetPlayer)
            refreshedCount += 1
          end
        end
        Utils.log(TAG, "Refreshed tags for " .. refreshedCount .. " players")
      end
    end
  end

  Players.PlayerAdded:Connect(function(player: Player): ()
    player.Chatted:Connect(function(message: string): ()
      handleAdminCommands(player, message)
    end)
  end)

  for _, player: Player in ipairs(Players:GetPlayers()) do
    if player.Parent then
      player.Chatted:Connect(function(message: string): ()
        handleAdminCommands(player, message)
      end)
    end
  end

  Utils.log(TAG, "System monitoring enabled")
end

--[[
    Sets up error handling and recovery mechanisms, including periodic health checks.
    
    @returns nil
]]
local function setupErrorHandling(): ()
  task.spawn(function(): ()
    while isSystemRunning do
      task.wait(60)
      if not ServiceManager.AreAllServicesHealthy() then
        Utils.warn(TAG, "Service health check failed, attempting recovery...")
        if ServiceManager.EmergencyRecovery() then
          Utils.log(TAG, "Automatic recovery successful")
        else
          Utils.warn(TAG, "Automatic recovery failed - manual intervention required")
        end
      end
    end
  end)

  game:BindToClose(function(): ()
    Utils.log(TAG, "Game shutting down, cleaning up TBRDS...")
    isSystemRunning = false
    ServiceManager.Shutdown()
  end)

  Utils.log(TAG, "Error handling and recovery systems enabled")
end

--[[
    Sets up integration with other systems via event subscriptions.
    
    @returns nil
]]
local function setupSystemIntegration(): ()
  local configService = ServiceManager.GetService("Configuration") :: Types.ConfigurationService?
  if not configService then
    Utils.log(TAG, "System integration skipped: Configuration service unavailable")
    return
  end
  local config = configService.GetConfiguration()
  if not config or not config.Integration or not config.Integration.BroadcastToOtherSystems then
    Utils.log(TAG, "System integration skipped: Integration not enabled or configuration invalid")
    return
  end
  if not EventSystem then
    Utils.log(TAG, "System integration skipped: EventSystem unavailable")
    return
  end

  EventSystem.Subscribe("TagChanged", function(eventData: Types.TagEventData): ()
    Utils.log(
      TAG,
      "Tag changed: "
        .. (eventData.OldRole or "None")
        .. " -> "
        .. eventData.NewRole
        .. " for "
        .. eventData.Player.Name
    )
  end)

  EventSystem.Subscribe("SecurityViolation", function(eventData: Types.TagEventData): ()
    Utils.warn(TAG, "Security Violation: " .. eventData.NewRole .. " for " .. eventData.Player.Name)
  end)

  Utils.log(TAG, "System integration enabled")
end

-- =============================================================================
-- INITIALIZATION
-- =============================================================================

-- Load EventSystem with error handling
do
  local success, module = pcall(function()
    return require(ReplicatedStorage.TBRDS.Shared:WaitForChild("TBRDS_EventSystem"))
  end)
  if not success then
    Utils.warn(TAG, "Failed to load TBRDS_EventSystem: " .. tostring(module))
    return
  end
  EventSystem = module
end

-- Load PerformanceMonitor with error handling
do
  local success, module = pcall(function()
    return require(ReplicatedStorage.TBRDS.Shared:WaitForChild("TBRDS_PerformanceMonitor"))
  end)
  if not success then
    Utils.warn(TAG, "Failed to load TBRDS_PerformanceMonitor: " .. tostring(module))
    return
  end
  PerformanceMonitor = module
end

-- Load ServiceManager with error handling
do
  local servicesFolder = ReplicatedStorage.TBRDS:FindFirstChild("Services")
  if not servicesFolder then
    Utils.warn(TAG, "Services folder not found in ReplicatedStorage.TBRDS")
    return
  end
  local success, module = pcall(function()
    return require(servicesFolder:WaitForChild("TBRDS_ServiceManager"))
  end)
  if not success then
    Utils.warn(TAG, "Failed to load TBRDS_ServiceManager: " .. tostring(module))
    return
  end
  ServiceManager = module :: Types.ServiceManager
end

-- Initialize the system
if not initializeSystem() then
  error("Failed to initialize system - aborting startup")
end

-- Set up monitoring, error handling, and integration
setupSystemMonitoring()
setupErrorHandling()
setupSystemIntegration()

-- Mark system as running
isSystemRunning = true

-- Log final status report
local finalReport: string = ServiceManager.GetSystemHealthReport()
Utils.log(TAG, "=== TBRDS Startup Complete ===")
print(finalReport)

-- Store interface in ServerStorage
local TBRDSFolder: Folder
local existingTBRDS = ServerStorage:FindFirstChild("TBRDS")
if existingTBRDS and existingTBRDS:IsA("Folder") then
  TBRDSFolder = existingTBRDS :: Folder
else
  TBRDSFolder = Instance.new("Folder")
  TBRDSFolder.Name = "TBRDS"
  TBRDSFolder.Parent = ServerStorage
end

Utils.log(TAG, "Server initialization complete!")
