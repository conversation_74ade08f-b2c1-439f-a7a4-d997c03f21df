--!strict

--[[
    - file: TBRDSSystem.luau
    - description: Provides the TBRDS system interface for server-side access.
]]

-- =============================================================================
-- MODULES
-- =============================================================================
local Types = require(game.ReplicatedStorage.TBRDS.TBRDS_Types)

-- =============================================================================
-- Import Types
-- =============================================================================
local TBRDSSystem: Types.TBRDSSystem? = nil

-- =============================================================================
-- FUNCTIONS
-- =============================================================================

-- Function to set the TBRDSSystem (called by TBRDS_Server)
local function setSystem(system: Types.TBRDSSystem): ()
  TBRDSSystem = system
end

-- Function to get the TBRDSSystem (called by other scripts)
local function getSystem(): Types.TBRDSSystem
  if not TBRDSSystem then
    error("TBRDSSystem not initialized. Ensure TBRDS_Server has run.")
  end
  return TBRDSSystem
end

-- =============================================================================
-- EXPORTS
-- =============================================================================
return {
  setSystem = setSystem,
  getSystem = getSystem,
}
