--!strict

--[[
    - file: Stats.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Performance statistics command for the Modular Command System (MCS)
      - Displays performance metrics and system statistics
      - Requires high permission level for system monitoring
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local MODULE_NAME = "StatsCommand"

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local StatsCommand = {}

-- ============================================================================
-- COMMAND PROPERTIES
-- ============================================================================
StatsCommand.Aliases = { "perf", "performance", "metrics" }
StatsCommand.Description = "Display system performance statistics and metrics"
StatsCommand.Usage = "stats [identifier]"
StatsCommand.PermissionLevel = 5 -- High permission level for system monitoring

-- ============================================================================
-- EXECUTE FUNCTION
-- ============================================================================
function StatsCommand.Execute(args: { string }, prefix: string): Types.CommandResponse
  Utils.startTimer("StatsCommand")

  local identifier = args[1]
  local stats = Utils.getPerformanceStats(identifier)

  if not stats then
    Utils.endTimer(MODULE_NAME, "StatsCommand")
    return {
      success = false,
      message = "Performance monitoring is disabled or no statistics available",
    }
  end

  local message = ""

  if identifier then
    -- Show specific identifier stats
    if type(stats) == "table" and stats.count then
      message = string.format(
        "Performance Stats for '%s':\n"
          .. "• Count: %d operations\n"
          .. "• Average Time: %.3f ms\n"
          .. "• Min Time: %.3f ms\n"
          .. "• Max Time: %.3f ms\n"
          .. "• Errors: %d",
        identifier,
        stats.count,
        stats.averageTime * 1000,
        stats.minTime * 1000,
        stats.maxTime * 1000,
        stats.errors
      )
    else
      message = string.format("No statistics found for identifier '%s'", identifier)
    end
  else
    -- Show all stats summary
    if type(stats) == "table" then
      local totalOperations = 0
      local totalErrors = 0
      local identifierCount = 0

      for _, stat in pairs(stats) do
        if type(stat) == "table" and stat.count then
          totalOperations = totalOperations + stat.count
          totalErrors = totalErrors + stat.errors
          identifierCount = identifierCount + 1
        end
      end

      message = string.format(
        "System Performance Summary:\n"
          .. "• Total Identifiers: %d\n"
          .. "• Total Operations: %d\n"
          .. "• Total Errors: %d\n"
          .. "• Success Rate: %.1f%%\n\n"
          .. "Use '%sstats <identifier>' for detailed stats",
        identifierCount,
        totalOperations,
        totalErrors,
        totalOperations > 0 and ((totalOperations - totalErrors) / totalOperations * 100) or 0,
        prefix
      )
    else
      message = "No performance statistics available"
    end
  end

  Utils.endTimer(MODULE_NAME, "StatsCommand")
  return {
    success = true,
    message = message,
  }
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return StatsCommand
