--!strict

--[[
    - file: Ban.luau

    - version: 2.1.1
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Bans a player from the game for a specified duration
      - Supports persistent bans using DataStores
      - Supports private server-specific bans that don't affect public servers
      - Useful for moderation and player management
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local DataStoreService: DataStoreService = game:GetService("DataStoreService")
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")

-- ============================================================================
-- MODULES
-- ============================================================================
local PermissionService =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PermissionService"))
local PrivateServerPermissions =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PrivateServerPermissions"))
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local TAG: string = "Ban"
local BAN_DB: string = "MCS_Bans"
local PRV_SERV_BAN_DB: string = "MCS_PrivateServerBans"

-- ============================================================================
-- VARIABLES
-- ============================================================================
local banStore: DataStore?
local privateBanStore: DataStore?
local isPrivateServer: boolean = game.PrivateServerId ~= ""

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local BanCommand = {}

-- ============================================================================
-- COMMAND METADATA
-- ============================================================================
BanCommand.Description = "Bans a player from the game (use [private] for private server bans)"
BanCommand.Usage = "ban [[private]] <player> [reason] [duration]"
BanCommand.PermissionLevel = PermissionService.Levels.SeniorModerators

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

--[[
  Initializes the ban DataStores used for tracking global and private bans.

  @desc
    - Acquires the main ban store.
    - If running in a private server, also acquires a private ban store.
    - Errors are logged but do not halt execution.

  @return none
]]
local function initialize()
  local success = pcall(function()
    banStore = DataStoreService:GetDataStore(BAN_DB)
    if isPrivateServer then
      privateBanStore = DataStoreService:GetDataStore(PRV_SERV_BAN_DB)
    end
  end)

  if not success then
    Utils.print(TAG, "Failed to initialize ban DataStores")
  end
end

-- Initialize
initialize()

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

local function isPlayerBanned(userId: number): (boolean, string?, number?)
  -- Check private server bans first if applicable
  if isPrivateServer and privateBanStore then
    local store: DataStore = privateBanStore
    local key = string.format("%s_%d", game.PrivateServerId, userId)
    local success, banData: Types.BanData? = pcall(function()
      return store:GetAsync(key)
    end)
    if success and banData then
      local banEnd = banData.endTime
      local reason = banData.reason
      if banEnd and os.time() < banEnd then
        return true, reason, banEnd
      end
    end
  end

  -- Check public server bans
  if banStore then
    local store: DataStore = banStore
    local success, banData: Types.BanData? = pcall(function()
      return store:GetAsync(tostring(userId))
    end)
    if success and banData then
      local banEnd = banData.endTime
      local reason = banData.reason
      if banEnd and os.time() < banEnd then
        return true, reason, banEnd
      end
    end
  end

  return false, nil, nil
end

--[[
  Saves a ban entry into the specified DataStore.

  @param store            DataStore        – The target DataStore
  @param key              string           – Key under which to save the ban
  @param endTime          number           – Expiration timestamp for the ban
  @param reason           string           – Explanation for the ban
  @param userId           number           – ID of the user being banned

  @return boolean, string? – True if the save succeeded;
                           – false and an error message otherwise
]]
local function applyBan(
  userId: number,
  reason: string,
  duration: number?,
  isPrivate: boolean
): (boolean, string?)
  local store: DataStore
  local key: string

  if isPrivate then
    if not isPrivateServer then
      return false, "Cannot apply private server ban in a public server"
    end
    if not privateBanStore then
      return false, "Private server ban DataStore not initialized"
    end
    store = privateBanStore
    key = string.format("%s_%d", game.PrivateServerId, userId)
  else
    if not banStore then
      return false, "Ban DataStore not initialized"
    end
    store = banStore
    key = tostring(userId)
  end

  local endTime = duration and (os.time() + duration) or math.huge
  local success = pcall(function()
    store:SetAsync(
      key,
      {
        endTime = endTime,
        reason = reason,
      } :: Types.BanData
    )
  end)

  if not success then
    Utils.print(TAG, string.format("Failed to save ban for %d", userId))
    return false, "unknown error"
  end

  Utils.print(
    TAG,
    string.format(
      "Ban saved for UserId %d: %s (End: %s, Scope: %s)",
      userId,
      reason,
      endTime == math.huge and "Permanent" or tostring(os.date("%Y-%m-%d %H:%M:%S", endTime)),
      isPrivate and "Private Server" or "Public Server"
    )
  )
  return true, nil
end

-- ============================================================================
-- EXECUTE FUNCTION
-- ============================================================================
--[[
    Executes the ban command on a target player. Handles both public and private server bans,
    with support for optional reason and duration. Kicks the player after banning.

    @param player Player -- The moderator issuing the command.
    @param args { string } -- The command arguments: [private] <player> [reason] [duration].
	@param prefix string -- The command prefix used (for usage message formatting).

	@return Types.CommandResponse -- The response object with success, message, and optional data.
]]
function BanCommand.Execute(player: Player, args: { string }, prefix: string): Types.CommandResponse
  if #args < 1 then
    return {
      success = false,
      message = "Usage: " .. prefix .. "ban [[private]] <player> [reason] [duration]",
    }
  end

  -- Check for [private] flag
  local isPrivateBan = args[1]:lower() == "[private]"
  local targetNameIndex = isPrivateBan and 2 or 1

  -- Check if we have enough arguments
  if #args < targetNameIndex then
    return {
      success = false,
      message = "Usage: " .. prefix .. "ban [[private]] <player> [reason] [duration]",
    }
  end

  local targetName = args[targetNameIndex]

  -- Validate private server permissions if needed
  if isPrivateBan then
    if not isPrivateServer then
      return {
        success = false,
        message = "Private server bans can only be applied in a private server",
      }
    end

    local permissionLevel =
      PrivateServerPermissions.GetPlayerPermission(game.PrivateServerId, player.UserId)
    if not permissionLevel or permissionLevel < PermissionService.Levels.SeniorModerators then
      return {
        success = false,
        message = "Insufficient permissions to ban in this private server",
      }
    end
  end

  -- Find the target player
  local targetInstance
  for _, p in ipairs(Players:GetPlayers()) do
    if p.Name:lower() == targetName:lower() then
      targetInstance = p
      break
    end
  end

  if not targetInstance or not targetInstance:IsA("Player") then
    return {
      success = false,
      message = "Player '" .. targetName .. "' not found or is not a valid player",
    }
  end

  Utils.print("BanCommand", "Executing ban for: " .. targetInstance.Name)

  local target = targetInstance :: Player
  local targetUserId = target.UserId

  -- Check if already banned
  local isBanned, banReason, _ = isPlayerBanned(targetUserId)
  if isBanned then
    return {
      success = false,
      message = string.format("%s is already banned: %s", target.Name, banReason or "No reason"),
    }
  end

  -- Extract reason and duration
  local reasonIndex = targetNameIndex + 1
  local durationIndex = targetNameIndex + 2
  local reason = args[reasonIndex] or "No reason provided"
  local duration = Utils.parseDuration(args[durationIndex])

  -- Apply the ban
  local banSuccess, banError = applyBan(targetUserId, reason, duration, isPrivateBan)
  if not banSuccess then
    return { success = false, message = banError or "Failed to ban player" }
  end

  -- Kick the player
  local durationString = duration
      and string.format(" (Duration: %s)", tostring(args[durationIndex] or ""))
    or ""
  local privateFlag = isPrivateBan and " (Private Server)" or ""
  local kickMessage = string.format(
    "Banned by %s: %s%s%s",
    player.Name,
    tostring(reason),
    tostring(durationString),
    tostring(privateFlag)
  )

  local kickSuccess = pcall(function()
    target:Kick(kickMessage)
  end)

  if not kickSuccess then
    Utils.print(TAG, string.format("Failed to kick %s", target.Name))
  else
    Utils.print(TAG, string.format("Kicked %s successfully", target.Name))
  end

  return {
    success = true,
    message = string.format(
      "Banned %s successfully%s",
      target.Name,
      isPrivateBan and " in this private server" or ""
    ),
  }
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return BanCommand
