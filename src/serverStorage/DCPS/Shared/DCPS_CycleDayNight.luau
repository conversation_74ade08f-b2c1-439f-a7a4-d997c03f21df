--!strict

--[[
	- file: DCPS_CycleDayNight.luau

	- version: 1.0.0
	- author: BleckWolf25
	- contributors:

	- copyright: Dynamic Innovative Studio

	- description:
        - Manages the in-game day/night clock progression.
        - Updates Lighting.ClockTime based on real-time delta.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Lighting: Lighting = game:GetService("Lighting")

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local DCPS_Cycle = {}

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local DAY_DURATION_SECONDS = 900 -- 15 real minutes (900) for a full day
local INGAME_HOURS_PER_SECOND = 24 / DAY_DURATION_SECONDS

-- ============================================================================
-- VARIABLES
-- ============================================================================
local currentClockTime = Lighting.ClockTime
local lastUpdate = os.clock()

-- ============================================================================
-- INITIALIZATION
-- ============================================================================
--[[
    Begins the day/night clock loop. On each tick, computes the real-time delta,
    converts it to in-game hours, wraps around at 24h, and applies it to Lighting.ClockTime.
]]
function DCPS_Cycle.Start(): ()
  task.spawn(function()
    while true do
      local now: number = os.clock()
      local dt: number = now - lastUpdate
      lastUpdate = now

      currentClockTime += dt * INGAME_HOURS_PER_SECOND
      if currentClockTime >= 24 then
        currentClockTime -= 24
      end

      Lighting.ClockTime = currentClockTime
      task.wait()
    end
  end)
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return DCPS_Cycle
