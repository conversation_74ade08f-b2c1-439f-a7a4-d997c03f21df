--!strict

--[[
	- file: DCPS_Utils.luau

	- version: 1.0.0
	- author: BleckWolf25
	- contributors:

	- copyright: Dynamic Innovative Studio

	- description:
		- Utilities for Diurnal Cycle Processor System (DCPS).
		- Reduces code repetition and improve mantainability.
		- Contains the following utilities:
			- Logging
			- Time Conversions
			- Tween Utilities
			- Lighting Utilities
			- Preloading
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")
local TweenService: TweenService = game:GetService("TweenService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(ReplicatedStorage.Configurations.Systems.DCPS_Configuration)
local Types = require(ServerStorage.DCPS.DCPS_Types)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local DCPS_Utils = {}

-- ============================================================================
-- LOGGING UTILITIES
-- ============================================================================

-- Tag
function DCPS_Utils.formatTag(tag: string): string
  return "[DCPS::" .. tag .. "]"
end

-- Log level
function DCPS_Utils.log(tag: string, ...): ()
  if Configuration.DebugMode then
    print(DCPS_Utils.formatTag(tag), ...)
  end
end

-- Warn level
function DCPS_Utils.warn(tag: string, ...): ()
  if Configuration.DebugMode then
    warn(DCPS_Utils.formatTag(tag), ...)
  end
end

-- ============================================================================
-- TIME CONVERSIONS
-- ============================================================================

-- Time (string) to Number
function DCPS_Utils.timeStringToNumber(timeStr: string): number
  local h, m, s = string.match(timeStr, "(%d+):(%d+):(%d+)")
  if not h or not m or not s then
    return 0
  end
  local hNum = tonumber(h)
  local mNum = tonumber(m)
  local sNum = tonumber(s)
  if hNum and mNum and sNum then
    return hNum + mNum / 60 + sNum / 3600
  end
  return 0
end

-- Time (number) to String
function DCPS_Utils.timeNumberToString(timeNum: number): string
  local totalSeconds = math.floor(timeNum * 3600 + 0.5)
  local h = math.floor(totalSeconds / 3600) % 24
  local m = math.floor((totalSeconds % 3600) / 60)
  local s = totalSeconds % 60
  return string.format("%02d:%02d:%02d", h, m, s)
end

--[[
    Time Segment Evaluation (handles wrap-around)
]]
function DCPS_Utils.isTimeInSegment(
  currentTime: number,
  startTime: number,
  endTime: number
): boolean
  -- Handle midnight wrap-around
  if startTime > endTime then
    -- Segment crosses midnight (e.g., 22:00 to 06:00)
    return currentTime >= startTime or currentTime < endTime
  else
    -- Normal segment within same day
    return currentTime >= startTime and currentTime < endTime
  end
end

-- ============================================================================
-- TWEEN UTILITIES
-- ============================================================================

-- Tween Utility for Lighting Transition
function DCPS_Utils.tweenProperty(
  instance: Instance,
  propertyTable: { [string]: any },
  duration: number
)
  local tweenInfo = TweenInfo.new(duration, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
  local tween = TweenService:Create(instance, tweenInfo, propertyTable)
  tween:Play()
  return tween
end

-- ============================================================================
-- LIGHTING UTILITIES
-- ============================================================================

-- Smoothly apply lighting settings (partial or full)
function DCPS_Utils.applyLightingTransition(
  current: Lighting,
  targetProfile: Types.LightingProfile,
  duration: number
)
  local tag = "LightingTransition"

  -- Tween Atmosphere if exists
  local atmosphere = current:FindFirstChildOfClass("Atmosphere")
  if atmosphere and targetProfile.Atmosphere then
    DCPS_Utils.tweenProperty(atmosphere, targetProfile.Atmosphere :: { [string]: any }, duration)
  else
    DCPS_Utils.warn(tag, "Missing Atmosphere or Atmosphere settings")
  end

  -- Tween SunRays if exists
  local sunRays = current:FindFirstChildOfClass("SunRaysEffect")
  if sunRays and targetProfile.SunRays then
    DCPS_Utils.tweenProperty(sunRays, targetProfile.SunRays, duration)
  else
    DCPS_Utils.warn(tag, "Missing SunRays or SunRays settings")
  end

  -- Replace Skybox (swap, not tweenable)
  local existingSky = current:FindFirstChildOfClass("Sky")
  if existingSky then
    existingSky:Destroy()
  end

  local newSky = Instance.new("Sky")
  newSky.Name = "CycleSkybox"
  for key, value in pairs(targetProfile.Skybox :: { [string]: any }) do
    (newSky :: any)[key] = value
  end
  newSky.Parent = current
end

-- ============================================================================
-- PRELOAD UTILITIES
-- ============================================================================

-- Preload skybox assets to avoid popping
function DCPS_Utils.preloadSkyboxAssets(profile: Types.LightingProfile)
  local contentProvider = game:GetService("ContentProvider")
  local assetIds = {}

  for _, v in pairs(profile.Skybox :: Types.SkyboxAssetFields & { [string]: string? }) do
    if typeof(v) == "string" and string.find(v, "rbxassetid://") then
      table.insert(assetIds, v)
    end
  end

  if #assetIds > 0 then
    contentProvider:PreloadAsync(assetIds)
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return DCPS_Utils
