--!strict

--[[
	- file: DCPS_Types.luau

	- version: 1.0.0
	- author: BleckWolf25
	- contributors:

	- copyright: Dynamic Innovative Studio

	- description:
        - Centralized remote events for the Diurnal Cycle Processor System (DCPS).
        - Exposes utilities to fire and listen to time‐segment changes.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ServerStorage = game:GetService("ServerStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ServerStorage.DCPS.DCPS_Types)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Remotes = {}

-- ============================================================================
-- CONSTANTS & VARIABLES
-- ============================================================================

-- Ordered list of all defined time segments
local timeSegments: { Types.TimeSegment } = {
  "Sunrise",
  "Morning",
  "Afternoon",
  "Sunset",
  "Night",
  "Midnight",
}

-- Table storing a BindableEvent per time segment
local BindableEvents: { [Types.TimeSegment]: BindableEvent } = {}

-- Current active segment for server-side access
local currentSegment: Types.TimeSegment? = nil

-- ============================================================================
-- PUBLIC METHODS
-- ============================================================================

--[[
    Fires the BindableEvent associated with the given time segment.
    
    @param segment Types.TimeSegment — the time segment to dispatch
]]
function Remotes.FireTimeSegment(segment: Types.TimeSegment): ()
  currentSegment = segment
  local event = BindableEvents[segment]
  if event then
    event:Fire()
  end
end

--[[
    Gets the current active time segment.

    @return Types.TimeSegment? — the current segment name, or nil if none
]]
function Remotes.GetCurrentSegment(): Types.TimeSegment?
  return currentSegment
end

--[[
    Connects a listener callback to the event for the specified segment.
    
    @param segment Types.TimeSegment — the time segment to listen for
    @param callback () -> () — invoked when the segment fires
    @return RBXScriptConnection — the event connection
]]
function Remotes.OnTimeSegment(segment: Types.TimeSegment, callback: () -> ()): RBXScriptConnection
  local event = BindableEvents[segment]
  if event then
    return event.Event:Connect(function()
      callback()
    end)
  end
  error("Invalid segment: " .. tostring(segment))
end

-- ============================================================================
-- TIME SEGMENTS & EVENTS
-- ============================================================================

for _, segmentName: Types.TimeSegment in ipairs(timeSegments) do
  local event = Instance.new("BindableEvent")
  event.Name = "On" .. segmentName
  BindableEvents[segmentName] = event
end

-- Expose the raw events table for advanced use
Remotes.BindableEvents = BindableEvents

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Remotes

-- ============================================================================
-- EXAMPLE USAGE
-- ============================================================================

-- From another server-side module or script
--[[

local DCPS_Remotes = require(ServerStorage.DCPS.DCPS_Remotes)

DCPS_Remotes.OnTimeSegment("Sunset", function()
	print("It's sunset! Turning on outside lights...")
end)

]]
