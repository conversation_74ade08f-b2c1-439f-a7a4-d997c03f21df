--!strict

--[[
	- file: DCPS_Types.luau

	- version: 1.0.0
	- author: BleckWolf25
	- contributors:

	- copyright: Dynamic Innovative Studio

	- description:
		- Centralized Configuration for Diurnal Cycle Processor System (DCPS).
]]

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Types = {}

-- ============================================================================
-- TYPES
-- ============================================================================
export type TimeSegment = "Sunrise" | "Morning" | "Afternoon" | "Sunset" | "Night" | "Midnight"

export type CycleDefinition = {
  Name: TimeSegment,
  StartTime: number, -- 0.0 to 24.0 (fractional hours, e.g. 6.5 for 06:30)
  EndTime: number, -- 0.0 to 24.0
}

export type DCPS_Cycle = {
  Start: () -> (),
}

export type AtmosphereSettings = {
  Density: number,
  Offset: number,
  Color: Color3,
  Decay: Color3,
  Glare: number,
  Haze: number,
}

export type SkyboxSettings = {
  SkyboxBk: string?,
  SkyboxDn: string?,
  SkyboxFt: string?,
  SkyboxLf: string?,
  SkyboxRt: string?,
  SkyboxUp: string?,
  StarCount: number?,
  SunAngularSize: number?,
  MoonAngularSize: number?,
}

export type SkyboxAssetFields = {
  SkyboxBk: string?,
  SkyboxDn: string?,
  SkyboxFt: string?,
  SkyboxLf: string?,
  SkyboxRt: string?,
  SkyboxUp: string?,
}

export type SunRaysSettings = {
  Intensity: number?,
  Spread: number?,
}

export type LightingProfile = {
  Atmosphere: AtmosphereSettings,
  Skybox: SkyboxSettings,
  SunRays: SunRaysSettings,
  Brightness: number,
  ExposureCompensation: number,
}

export type CycleSegment = {
  Definition: CycleDefinition,
  Lighting: LightingProfile,
}

-- Define CycleSegments as a record type with explicit keys
export type CycleSegments = {
  Sunrise: CycleSegment,
  Morning: CycleSegment,
  Afternoon: CycleSegment,
  Sunset: CycleSegment,
  Night: CycleSegment,
  Midnight: CycleSegment,
}

export type DCPSConfiguration = {
  DebugMode: boolean,
  CycleSegments: CycleSegments,
  TransitionTime: number, -- in seconds, duration of transitions between states
  UpdateInterval: number, -- how often to evaluate (e.g. every 0.5 seconds)
}

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Types
