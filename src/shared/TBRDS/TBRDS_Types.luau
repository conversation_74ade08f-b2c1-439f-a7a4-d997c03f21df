--!strict

--[[
    - file: TBRDS_Types.luau

    - version: 3.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Type definitions for the Tag-Based Role Display System (TBRDS)
      - Defines player roles, styles, event systems, validation, and security types
      - Used by both client and server components
]]

-- =============================================================================
-- PLAYER & ROLE TYPES
-- =============================================================================

export type PlayerId = number
export type RoleName = string
export type GroupId = number
export type GroupRank = number

-- =============================================================================
-- STYLE & VISUAL TYPES
-- =============================================================================

export type GradientStyle = {
  Colors: { Color3 },
  Rotation: number,
}

export type RoleStyle = {
  Color: Color3,
  Font: Enum.Font,
  Image: string?,
  Gradient: {
    Colors: { Color3 },
    Rotation: number,
  }?,
  FontStyle: string?,
  TextStroke: {
    Color: Color3,
    Transparency: number,
  }?,
  GetText: ((Player) -> string)?,
}

-- =============================================================================
-- ROLE HANDLING TYPES
-- =============================================================================

export type RoleHandler = {
  Check: (Player) -> boolean,
  Style: RoleStyle,
}

export type RoleHandlersModule = {
  GetPlayerRole: (Player) -> RoleName,
  [RoleName]: RoleHandler,
  User: RoleHandler,
  Supporter: RoleHandler,
  Trusted: RoleHandler,
  Developer: RoleHandler,
  ["Junior Moderator"]: RoleHandler,
  Investors: RoleHandler,
  ["Game Moderator"]: RoleHandler,
  ["Senior Moderator"]: RoleHandler,
  Anonmancer: RoleHandler,
  BleckWolf25: RoleHandler,
}

-- =============================================================================
-- PLAYER DATA TYPES
-- =============================================================================

export type PlayerTagData = {
  Role: RoleName,
  BillboardGUI: BillboardGui?,
  LastUpdated: number,
  ValidationCount: number,
  SecurityFlags: { string }?,
  GroupRank: number?, -- Cached group rank
  SpecialTag: string?, -- Cached special tag
}

export type PlayerCache = {
  [Player]: PlayerTagData,
}

-- =============================================================================
-- EVENT SYSTEM TYPES
-- =============================================================================

export type TagEventType =
  "TagChanged"
  | "TagAssigned"
  | "TagRemoved"
  | "RoleValidated"
  | "SecurityViolation"
  | "ConfigurationChanged"

export type TagEventData = {
  Player: Player,
  OldRole: RoleName?,
  NewRole: RoleName,
  Timestamp: number,
  Source: string,
  Metadata: { [string]: any }?,
}

export type EventCallback = (TagEventData) -> ()

export type EventSystem = {
  Subscribe: (TagEventType, EventCallback) -> string,
  Unsubscribe: (string) -> boolean,
  Fire: (TagEventType, TagEventData) -> (),
  GetSubscribers: (TagEventType) -> { EventCallback },
}

export type EventMetrics = {
  eventsProcessed: number,
  subscribersNotified: number,
  errorCount: number,
  lastReset: number,
}

export type EventSubscribers = { [TagEventType]: { [string]: EventCallback } }

-- =============================================================================
-- VALIDATION & SECURITY TYPES
-- =============================================================================

export type ValidationResult = {
  Success: boolean,
  Role: RoleName?,
  ErrorCode: string?,
  ErrorMessage: string?,
  SecurityFlags: { string }?,
}

export type SecurityEvent = {
  Type: string,
  Player: Player,
  Severity: "Low" | "Medium" | "High" | "Critical",
  Description: string,
  Timestamp: number,
  Data: { [string]: any }?,
}

export type RateLimitData = {
  Requests: { number },
  LastReset: number,
}

export type RateLimiter = {
  Window: number,
  Limit: number,
  Requests: { [number]: RateLimitData },

  CheckLimit: (self: RateLimiter, userId: number) -> (boolean, number),
  Reset: (self: RateLimiter, userId: number) -> (),
}

-- =============================================================================
-- CONFIGURATION & SETTINGS TYPES
-- =============================================================================

export type ConfigurationSummary = {
  initialized: boolean,
  performanceMetrics: boolean,
  eventSystem: boolean,
  rateLimitWindow: number,
  rateLimitMaxRequests: number,
  primaryGroupId: number,
  roleCount: number,
  listenerCount: number,
}

export type EventNames = {
  TagChanged: "TagChanged",
  TagAssigned: "TagAssigned",
  TagRemoved: "TagRemoved",
  RoleValidated: "RoleValidated",
  SecurityViolation: "SecurityViolation",
  ConfigurationChanged: "ConfigurationChanged",
}

export type BillboardSettings = {
  MaxDistance: number,
  StudsOffset: Vector3,
  Size: UDim2,
  AlwaysOnTop: boolean,
  LightInfluence: number,
}

export type AntiExploitSettings = {
  EnablePositionValidation: boolean,
  EnableRoleValidation: boolean,
  EnableDataStoreValidation: boolean,
  MaxRoleChangesPerMinute: number,
  SuspiciousActivityThreshold: number,
}

export type DebugConfig = {
  LogTagAssignments: boolean,
  LogValidationEvents: boolean,
  LogSecurityEvents: boolean,
  LogPerformanceMetrics: boolean,
  VerboseLogging: boolean,
}

export type MetricsConfig = {
  TrackTagAssignments: boolean,
  TrackValidationTime: boolean,
  TrackSecurityEvents: boolean,
  TrackPerformance: boolean,
  MetricsRetentionTime: number,
}

export type ValidationRules = {
  RequireCharacter: boolean,
  RequireValidUserId: boolean,
  RequireGroupMembership: boolean,
  ValidateRolePermissions: boolean,
  CheckGamePassOwnership: boolean,
}

export type CacheConfig = {
  PlayerDataTTL: number,
  RoleDataTTL: number,
  GroupDataTTL: number,
  MaxCacheSize: number,
}

export type TBRDSConfiguration = {
  Settings: {
    DebugMode: boolean,
    EnablePerformanceMetrics: boolean,
    EnableEventSystem: boolean,
    RateLimit: {
      Window: number,
      MaxRequests: number,
    },
    MaxTagLength: number,
    MaxDisplayNameLength: number,
    TagValidationInterval: number,
    GroupRankCheckInterval: number,
    MaxCachedPlayers: number,
    BillboardSettings: BillboardSettings,
    AntiExploit: AntiExploitSettings,
  },
  Groups: {
    Primary: {
      Id: GroupId,
      Name: string,
    },
    [string]: any,
  },
  RolePriority: { RoleName },
  GamePasses: { [string]: number },
  Events: EventNames,
  Remotes: { [string]: string },
  ErrorCodes: { [string]: string },
  Metrics: MetricsConfig,
  Debug: DebugConfig,
  ValidationRules: ValidationRules,
  Cache: CacheConfig,
  Integration: {
    BroadcastToOtherSystems: boolean,
  },
  GetRolePriority: (role: string) -> number?,
  IsDebugMode: () -> boolean,
  SetDebugMode: (enabled: boolean) -> (),
}

export type TBRDSSettings = {
  DebugMode: boolean,
  EnablePerformanceMetrics: boolean,
  EnableEventSystem: boolean,
  RateLimit: {
    Window: number,
    MaxRequests: number,
  },
  MaxTagLength: number,
  MaxDisplayNameLength: number,
  TagValidationInterval: number,
  GroupRankCheckInterval: number,
  MaxCachedPlayers: number,
  BillboardSettings: BillboardSettings,
  AntiExploit: AntiExploitSettings,
}

-- =============================================================================
-- CONSTANTS & SERVICE TYPES
-- =============================================================================

export type TBRDSConstants = {
  GROUP_ID: number,
  GROUP_NAME: string,
  ROLE_PRIORITY: { RoleName },
  EVENT_NAMES: EventNames,
  GAMEPASS_IDS: { [string]: number },
  REMOTE_NAMES: { [string]: string },
  ERROR_CODES: { [string]: string },
}

export type ServiceManager = {
  Initialize: () -> boolean,
  GetService: (string) -> any,
  AreAllServicesHealthy: () -> boolean,
  GetSystemHealthReport: () -> string,
  EmergencyRecovery: () -> boolean,
  Shutdown: () -> (),
  GetServiceStatusSummary: () -> { services: { [string]: ServiceStatus } },
}

export type ServiceStatus = {
  initialized: boolean,
  healthy: boolean,
  lastCheck: number,
  details: { [string]: any }?,
}

export type ServiceModule = {
  Initialize: () -> boolean,
  Cleanup: (() -> ())?,
  GetServiceStatus: (() -> { initialized: boolean })?,
  RefreshPlayerTag: ((Player) -> ValidationResult)?,
}

export type BillboardServiceStatus = {
  initialized: boolean,
  activeBillboards: number,
  validBillboards: number,
  invalidBillboards: number,
  settings: BillboardSettings,
}

export type RoleService = {
  GetPlayerRole: (Player) -> RoleName,
  ValidatePlayerRole: (Player, RoleName) -> ValidationResult,
  GetPlayersWithRole: (RoleName) -> { Player },
  GetRoleStyle: (RoleName) -> RoleStyle?,
}

export type TagService = {
  Initialize: () -> boolean,
  GetPlayerTagData: (Player) -> PlayerTagData?,
  AssignTag: (Player) -> ValidationResult,
  UpdateTag: (Player, string) -> boolean,
  RefreshPlayerTag: (Player) -> ValidationResult,
  RemoveTag: (Player) -> Player,
  ValidateTag: (Player, string) -> ValidationResult,
  SaveSpecialTag: (number, string) -> boolean,
  LoadSpecialTag: (number) -> string?,
  GetTagStatistics: () -> { totalPlayers: number, roleDistribution: { [string]: number } },
  GetServiceStatus: () -> TagServiceStatus,
  HandlePlayerLeaving: (Player) -> (),
  GetAllPlayerTags: () -> PlayerCache,
}

export type RoleCacheEntry = { role: RoleName, timestamp: number, validationCount: number }

export type TagServiceStatus = {
  initialized: boolean,
  rateLimiterActive: boolean,
  dataStoreActive: boolean,
  remoteEventsActive: boolean,
  details: {
    totalPlayers: number,
    roleDistribution: { [string]: number },
  },
}

export type ConfigurationService = {
  Initialize: () -> boolean,
  GetConfiguration: () -> TBRDSConfiguration,
  GetSettings: () -> TBRDSSettings,
  GetGroups: () -> { [string]: any },
  GetRolePriority: () -> { string },
  GetGamePasses: () -> { [string]: number },
  GetEvents: () -> EventNames,
  GetRemotes: () -> { [string]: string },
  GetErrorCodes: () -> { [string]: string },
  IsDebugMode: () -> boolean,
  IsPerformanceMetricsEnabled: () -> boolean,
  IsEventSystemEnabled: () -> boolean,
  GetRateLimitConfig: () -> { Window: number, MaxRequests: number },
  GetBillboardSettings: () -> BillboardSettings,
  GetAntiExplRateLimitSeconds: () -> number,
  UpdateConfiguration: (newConfig: TBRDSConfiguration) -> boolean,
  SubscribeToChanges: (callback: (TBRDSConfiguration) -> ()) -> string,
  UnsubscribeFromChanges: (listenerId: string) -> boolean,
  ValidateConfiguration: (config: TBRDSConfiguration) -> (boolean, { string }),
  GetConfigurationSummary: () -> ConfigurationSummary,
  ReloadConfiguration: () -> boolean,
  Cleanup: () -> (),
}

export type TagStatistics = {
  totalPlayers: number,
  roleDistribution: { [string]: number },
}

-- =============================================================================
-- PERFORMANCE & MONITORING TYPES
-- =============================================================================

export type RoleType = string
export type ErrorType = string
export type SecurityEventType = string
export type ValidationTime = number

export type PerformanceMetrics = {
  TagAssignments: number,
  ValidationTime: number,
  SecurityEvents: number,
  CacheHits: number,
  CacheMisses: number,
  ErrorCount: number,
  LastReset: number,
}

-- Historical snapshot type
export type HistoricalSnapshot = {
  timestamp: number,
  metrics: PerformanceMetrics,
}

-- Detailed metrics type
export type DetailedMetrics = {
  validationTimes: { ValidationTime },
  tagAssignmentsByRole: { [RoleType]: number },
  errorsByType: { [ErrorType]: number },
  securityEventsByType: { [SecurityEventType]: number },
  performanceHistory: { PerformanceSnapshot },
}

-- Thresholds for performance warnings
export type PerformanceThresholds = {
  MaxValidationTime: number,
  MaxTagAssignmentTime: number,
  MaxCacheSize: number,
  MaxErrorRate: number,
}

-- Result type for detailed metrics
export type DetailedMetricsResult = {
  basic: PerformanceMetrics,
  cacheHitRate: number,
  averageValidationTime: number,
  errorRate: number,
  uptime: number,
  tagAssignmentsByRole: { [RoleType]: number },
  errorsByType: { [ErrorType]: number },
  securityEventsByType: { [SecurityEventType]: number },
  recentValidationTimes: { ValidationTime },
}

-- Performance snapshot type
export type PerformanceSnapshot = {
  timestamp: number,
  metrics: PerformanceMetrics,
  detailed: DetailedMetricsResult,
}

-- Module interface for external usage
export type PerformanceMonitor = {
  RecordTagAssignment: (roleName: string?) -> (),
  RecordValidationTime: (timeMs: number) -> (),
  RecordSecurityEvent: (eventType: string?) -> (),
  RecordCacheHit: () -> (),
  RecordCacheMiss: () -> (),
  RecordError: (errorType: string?) -> (),
  GetMetrics: () -> PerformanceMetrics,
  GetDetailedMetrics: () -> DetailedMetricsResult,
  Reset: () -> (),
  CreateSnapshot: () -> (),
  GetHistory: () -> { PerformanceSnapshot },
  CheckPerformanceHealth: () -> { string },
  GenerateReport: () -> string,
}

-- =============================================================================
-- TBRDS CONSTANTS MODULE
-- =============================================================================

local TBRDSConstants: TBRDSConstants = {
  GROUP_ID = 34320208,
  GROUP_NAME = "Dynamic Innovative Studio",
  ROLE_PRIORITY = {
    "BleckWolf25",
    "Anonmancer",
    "Senior Moderator",
    "Game Moderator",
    "Junior Moderator",
    "Developer",
    "Investors",
    "Trusted",
    "Supporter",
    "User",
  },
  GAMEPASS_IDS = {
    Supporter = 99445069658101,
  },
  EVENT_NAMES = {
    TagChanged = "TagChanged",
    TagAssigned = "TagAssigned",
    TagRemoved = "TagRemoved",
    RoleValidated = "RoleValidated",
    SecurityViolation = "SecurityViolation",
    ConfigurationChanged = "ConfigurationChanged",
  },
  REMOTE_NAMES = {
    TagUpdate = "TagRemote",
    TagRequest = "TagRequestRemote",
    TagValidation = "TagValidationRemote",
    SecurityReport = "SecurityReportRemote",
  },
  ERROR_CODES = {
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",
    INVALID_PLAYER = "INVALID_PLAYER",
    INVALID_ROLE = "INVALID_ROLE",
    SECURITY_VIOLATION = "SECURITY_VIOLATION",
    DATASTORE_ERROR = "DATASTORE_ERROR",
    VALIDATION_FAILED = "VALIDATION_FAILED",
  },
}

-- =============================================================================
-- TBRDS SYSTEM INTERFACE
-- =============================================================================
export type TBRDSSystem = {
  ServiceManager: ServiceManager,
  GetPlayerRole: (Player) -> string,
  RefreshPlayerTag: (Player) -> boolean,
  GetSystemHealth: () -> string,
  IsSystemHealthy: () -> boolean,
}

-- =============================================================================
-- EXPORTS
-- =============================================================================
return { TBRDSConstants = TBRDSConstants }
