-- --!strict

--[[
    - file: TBRDS_Remotes.luau

    - version: 2.1.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Remote event management module for the TBRDS (Tag-Based Role Distribution System).
      - Provides a centralized, type-safe, and consistent API for managing RemoteEvents used by TBRDS.
      - Handles creation, caching, and retrieval of RemoteEvents for both server and client.
      - Ensures all remotes are initialized and available before use.
      - Supports debug logging and cleanup for testing.
]]

-- =============================================================================
-- SERVICES
-- =============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService: RunService = game:GetService("RunService")

-- =============================================================================
-- MODULES
-- =============================================================================
local Configuration = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Utils = require(ReplicatedStorage.TBRDS.Shared.TBRDS_Utils)

-- =============================================================================
-- MAIN IMPLEMENTATION
-- =============================================================================
local TBRDSRemotes = {}

-- =============================================================================
-- TYPE ANNOTATIONS & DEFINITIONS
-- =============================================================================
--[[
    RemoteEventMap: Dictionary of remote event names to RemoteEvent instances.
    TBRDSRemotes: Public API type for this module.
]]
export type RemoteEventMap = { [string]: RemoteEvent }
export type TBRDSRemotes = typeof(TBRDSRemotes) & {
  GetRemotesFolder: () -> Folder?,
  GetTagUpdateRemote: () -> RemoteEvent?,
  GetTagRequestRemote: () -> RemoteEvent?,
  GetTagValidationRemote: () -> RemoteEvent?,
  GetSecurityReportRemote: () -> RemoteEvent?,
  InitializeRemotes: () -> boolean,
  WaitForRemotes: () -> boolean,
  CleanupRemotes: () -> (),
  GetAllRemotes: () -> RemoteEventMap,
  AreRemotesReady: () -> boolean,
}

-- =============================================================================
-- VARIABLES
-- =============================================================================
-- Remote event cache for fast lookup and consistency.
local remoteEvents: RemoteEventMap = {}
local remotesFolder: Folder? = nil

-- =============================================================================
-- CONSTANTS
-- =============================================================================
local TAG: string = "Remotes"
local REMOTES_FOLDER_NAME: string = "Remotes"
local TBRDS_FOLDER_NAME: string = "TBRDS"

-- =============================================================================
-- PRIVATE METHODS
-- =============================================================================
--[[
    Get or create the TBRDS folder in ReplicatedStorage.
    
    @return Folder -- The TBRDS folder.
    
    @throws error if folder is not found on client after timeout.
]]
local function getTBRDSFolder(): Folder
  local tbrdsFolder = ReplicatedStorage:FindFirstChild(TBRDS_FOLDER_NAME)

  if not tbrdsFolder then
    if RunService:IsServer() then
      tbrdsFolder = Instance.new("Folder")
      tbrdsFolder.Name = TBRDS_FOLDER_NAME
      tbrdsFolder.Parent = ReplicatedStorage
      Utils.log(TAG, "Created TBRDS folder")
    else
      -- Client waits for server to create it
      tbrdsFolder = ReplicatedStorage:WaitForChild(TBRDS_FOLDER_NAME, 10)
      if not tbrdsFolder then
        error("Failed to find TBRDS folder after timeout")
      end
    end
  end

  return tbrdsFolder
end

-- =============================================================================
-- PUBLIC METHODS
-- =============================================================================

--[[
    Get or create the remotes folder under TBRDS in ReplicatedStorage.
    
    @return Folder? -- The remotes folder, or nil if unavailable.
]]
function TBRDSRemotes.GetRemotesFolder(): Folder?
  if remotesFolder and remotesFolder.Parent then
    return remotesFolder
  end

  local tbrdsFolder = getTBRDSFolder()
  if not tbrdsFolder then
    Utils.warn(TAG, "Cannot access TBRDS folder")
    return nil
  end

  remotesFolder = tbrdsFolder:FindFirstChild(REMOTES_FOLDER_NAME) :: Folder?

  -- Create new folder if not found
  if not remotesFolder then
    if RunService:IsServer() then
      remotesFolder = Instance.new("Folder")
      remotesFolder.Name = REMOTES_FOLDER_NAME
      remotesFolder.Parent = tbrdsFolder
      Utils.log(TAG, "Created remotes folder")
    else
      remotesFolder = tbrdsFolder:WaitForChild(REMOTES_FOLDER_NAME, 10) :: Folder
      if not remotesFolder then
        Utils.warn(TAG, "Failed to find remotes folder after timeout")
        return nil
      end
    end
  end
  return remotesFolder
end

--[[
    Get or create a RemoteEvent by name, using cache for efficiency.
    
    @param eventName: string -- Name of the remote event.
    
    @return RemoteEvent? -- The RemoteEvent instance, or nil if unavailable.
]]
local function getRemoteEvent(eventName: string): RemoteEvent?
  -- Check cache first
  if remoteEvents[eventName] and remoteEvents[eventName].Parent then
    return remoteEvents[eventName]
  end

  local folder = TBRDSRemotes.GetRemotesFolder()
  if not folder then
    Utils.warn(TAG, "Cannot access remotes folder")
    return nil
  end

  -- Look for existing event
  local remoteEvent = folder:FindFirstChild(eventName) :: RemoteEvent?

  if not remoteEvent then
    if RunService:IsServer() then
      remoteEvent = Instance.new("RemoteEvent")

      -- Ignore type errors here as we are creating a new RemoteEvent
      remoteEvent.Name = eventName
      remoteEvent.Parent = folder
      Utils.log(TAG, "Created remote event: %s", eventName)
    else
      -- Client waits for server to create it
      remoteEvent = folder:WaitForChild(eventName, 10) :: RemoteEvent?
      if not remoteEvent then
        Utils.warn(TAG, "Failed to find remote event '%s' after timeout", eventName)
        return nil
      end
    end
  end

  -- Cache the remote event if it exists
  if remoteEvent then
    remoteEvents[eventName] = remoteEvent
  end
  return remoteEvent
end

--[[
 updates.
    
    @return RemoteEvent? -- Tag update RemoteEvent.
]]
function TBRDSRemotes.GetTagUpdateRemote(): RemoteEvent?
  return getRemoteEvent(Configuration.Remotes.TagUpdate)
end

--[[
    Get the RemoteEvent for tag requests.
    
    @return RemoteEvent? -- Tag request RemoteEvent.
]]
function TBRDSRemotes.GetTagRequestRemote(): RemoteEvent?
  return getRemoteEvent(Configuration.Remotes.TagRequest)
end

--[[
    Get the RemoteEvent for tag validation.
    
    @return RemoteEvent? -- Tag validation RemoteEvent.
]]
function TBRDSRemotes.GetTagValidationRemote(): RemoteEvent?
  return getRemoteEvent(Configuration.Remotes.TagValidation)
end

--[[
    Get the RemoteEvent for security reports.
    
    @return RemoteEvent? -- Security report RemoteEvent.
]]
function TBRDSRemotes.GetSecurityReportRemote(): RemoteEvent?
  return getRemoteEvent(Configuration.Remotes.SecurityReport)
end

--[[
    Initialize all configured RemoteEvents (server only).
    Creates all remotes defined in Configuration.Remotes.
    
    @return boolean -- True if all remotes initialized, false otherwise.
]]
function TBRDSRemotes.InitializeRemotes(): boolean
  if not RunService:IsServer() then
    Utils.warn(TAG, "InitializeRemotes should only be called on the server")
    return false
  end

  Utils.log(TAG, "Initializing remote events...")

  local success = true

  -- Create all configured remote events
  for _, remoteName in pairs(Configuration.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if not remoteEvent then
      Utils.warn(TAG, "Failed to create remote event: %s", remoteName)
      success = false
    end
  end

  if success then
    Utils.log(TAG, "All remote events initialized successfully")
  else
    Utils.warn(TAG, "Some remote events failed to initialize")
  end

  return success
end

--[[
    Wait for all configured RemoteEvents to be available (client only).
    
    @return boolean -- True if all remotes found, false otherwise.
]]
function TBRDSRemotes.WaitForRemotes(): boolean
  if RunService:IsServer() then
    Utils.warn(TAG, "WaitForRemotes should only be called on the client")
    return false
  end

  Utils.log(TAG, "Waiting for remote events...")

  local success = true

  -- Wait for all configured remote events
  for _, remoteName in pairs(Configuration.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if not remoteEvent then
      Utils.warn(TAG, "Failed to find remote event: %s", remoteName)
      success = false
    end
  end

  if success then
    Utils.log(TAG, "All remote events found successfully")
  else
    Utils.warn(TAG, "Some remote events were not found")
  end

  return success
end

--[[
    Clean up all RemoteEvents and folders (server only, for testing/cleanup).
    Destroys the remotes folder and clears cache.
]]
function TBRDSRemotes.CleanupRemotes(): ()
  if not RunService:IsServer() then
    Utils.warn(TAG, "CleanupRemotes should only be called on the server")
    return
  end

  Utils.log(TAG, "Cleaning up remote events...")

  -- Clear cache
  remoteEvents = {}

  -- Remove remotes folder if it exists
  if remotesFolder and remotesFolder.Parent then
    remotesFolder:Destroy()
    remotesFolder = nil
  end

  Utils.log(TAG, "Remote events cleaned up")
end

--[[
    Get a dictionary of all configured RemoteEvents (for debugging).
    
    @return RemoteEventMap -- Map of config keys to RemoteEvent instances.
]]
function TBRDSRemotes.GetAllRemotes(): RemoteEventMap
  local allRemotes: RemoteEventMap = {}

  for configKey, remoteName in pairs(Configuration.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if remoteEvent then
      allRemotes[configKey] = remoteEvent
    end
  end

  return allRemotes
end

--[[
    Check if all configured RemoteEvents are available.
    
    @return boolean -- True if all remotes are ready, false otherwise.
]]
function TBRDSRemotes.AreRemotesReady(): boolean
  for _, remoteName in pairs(Configuration.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if not remoteEvent then
      return false
    end
  end

  return true
end

-- =============================================================================
-- INITIALIZATION
-- =============================================================================
--[[
    On server: initializes all remotes immediately.
    On client: waits for all remotes to be available asynchronously.
]]
if RunService:IsServer() then
  -- Server initializes remotes immediately
  TBRDSRemotes.InitializeRemotes()
else
  -- Client waits for remotes to be available
  task.spawn(function()
    TBRDSRemotes.WaitForRemotes()
  end)
end

-- =============================================================================
-- EXPORTS
-- =============================================================================
return TBRDSRemotes :: TBRDSRemotes
