--!strict

--[[
	- file: TBRDS_PerformanceMonitor.luau

	- version: 2.0.0
	- author: BleckWolf25
	- contributors:

	- copyright: Dynamic Innovative Studio

	- description:
		- Shared module for the Tag-Based Role Display System (TBRDS) performance monitoring.
		- Provides comprehensive real-time performance tracking and analytics for system optimization.
		- Monitors tag assignment efficiency, validation performance, cache usage, and error rates.
		- Implements automatic threshold checking with performance warnings and alerts.
		- Tracks memory usage, frame time, cache efficiency, and validation failure rates.
		- Used by both client and server components for performance analysis and debugging.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService: RunService = game:GetService("RunService")

-- ============================================================================
-- PATHS
-- ============================================================================
local tbrdsFolder = ReplicatedStorage.TBRDS
local sharedFolder = tbrdsFolder.Shared

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Types = require(tbrdsFolder.TBRDS_Types)
local Utils = require(sharedFolder.TBRDS_Utils)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local PerformanceMonitor: Types.PerformanceMonitor = {} :: Types.PerformanceMonitor

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local TAG: string = "PerformanceMonitor"
local PERFORMANCE_THRESHOLDS: Types.PerformanceThresholds = {
  MaxValidationTime = 5.0, -- 5000ms
  MaxTagAssignmentTime = 0.05, -- 50ms
  MaxCacheSize = 1000,
  MaxErrorRate = 0.05, -- 5%
}

-- ============================================================================
-- VARIABLES
-- ============================================================================
local metrics: Types.PerformanceMetrics = {
  TagAssignments = 0,
  ValidationTime = 0,
  SecurityEvents = 0,
  CacheHits = 0,
  CacheMisses = 0,
  ErrorCount = 0,
  LastReset = os.time(),
}
local detailedMetrics: Types.DetailedMetrics = {
  validationTimes = {},
  tagAssignmentsByRole = {},
  errorsByType = {},
  securityEventsByType = {},
  performanceHistory = {},
}

-- ============================================================================
-- PUBLIC METHODS
-- ============================================================================

--[[
    Records a tag assignment.
    
    @param roleName string? The role name assigned (optional).
    
    @returns nil
]]
function PerformanceMonitor.RecordTagAssignment(roleName: string?): ()
  metrics.TagAssignments = metrics.TagAssignments + 1
  if roleName then
    detailedMetrics.tagAssignmentsByRole[roleName] = (
      detailedMetrics.tagAssignmentsByRole[roleName] or 0
    ) + 1
  end
  Utils.log(TAG, "Tag assignment recorded (total: " .. metrics.TagAssignments .. ")")
end

--[[
    Records a validation time.
    
    @param timeMs number The validation time in milliseconds.
    
    @returns nil
]]
function PerformanceMonitor.RecordValidationTime(timeMs: number): ()
  metrics.ValidationTime = metrics.ValidationTime + timeMs
  table.insert(detailedMetrics.validationTimes, timeMs)
  if #detailedMetrics.validationTimes > 100 then
    table.remove(detailedMetrics.validationTimes, 1)
  end
  if timeMs > PERFORMANCE_THRESHOLDS.MaxValidationTime * 1000 then
    Utils.warn(TAG, "Slow validation detected: " .. timeMs .. "ms")
  end
  Utils.log(TAG, "Validation time recorded: " .. timeMs .. "ms")
end

--[[
    Records a security event.
    
    @param eventType string? The type of security event (optional).
    
    @returns nil
]]
function PerformanceMonitor.RecordSecurityEvent(eventType: string?): ()
  metrics.SecurityEvents = metrics.SecurityEvents + 1
  if eventType then
    detailedMetrics.securityEventsByType[eventType] = (
      detailedMetrics.securityEventsByType[eventType] or 0
    ) + 1
  end
  Utils.log(TAG, "Security event recorded: " .. (eventType or "unknown"))
end

--[[
    Records a cache hit.
    
    @returns nil
]]
function PerformanceMonitor.RecordCacheHit(): ()
  metrics.CacheHits = metrics.CacheHits + 1
  Utils.log(TAG, "Cache hit recorded")
end

--[[
    Records a cache miss.
    
    @returns nil
]]
function PerformanceMonitor.RecordCacheMiss(): ()
  metrics.CacheMisses = metrics.CacheMisses + 1
  Utils.log(TAG, "Cache miss recorded")
end

--[[
    Records an error.
    
    @param errorType string? The type of error (optional).
    
    @returns nil
]]
function PerformanceMonitor.RecordError(errorType: string?): ()
  metrics.ErrorCount = metrics.ErrorCount + 1
  if errorType then
    detailedMetrics.errorsByType[errorType] = (detailedMetrics.errorsByType[errorType] or 0) + 1
  end
  Utils.warn(TAG, "Error recorded: " .. (errorType or "unknown"))
end

--[[
    Gets the current performance metrics.
    
    @returns Types.PerformanceMetrics The current metrics.
]]
function PerformanceMonitor.GetMetrics(): Types.PerformanceMetrics
  return {
    TagAssignments = metrics.TagAssignments,
    ValidationTime = metrics.ValidationTime,
    SecurityEvents = metrics.SecurityEvents,
    CacheHits = metrics.CacheHits,
    CacheMisses = metrics.CacheMisses,
    ErrorCount = metrics.ErrorCount,
    LastReset = metrics.LastReset,
  }
end

--[[
    Gets detailed performance metrics.
    
    @returns Types.DetailedMetricsResult The detailed metrics.
]]
function PerformanceMonitor.GetDetailedMetrics(): Types.DetailedMetricsResult
  local cacheHitRate = 0
  if metrics.CacheHits + metrics.CacheMisses > 0 then
    cacheHitRate = metrics.CacheHits / (metrics.CacheHits + metrics.CacheMisses)
  end
  local avgValidationTime = 0
  if #detailedMetrics.validationTimes > 0 then
    local total = 0
    for _, time in detailedMetrics.validationTimes do
      total = total + time
    end
    avgValidationTime = total / #detailedMetrics.validationTimes
  end
  local uptime = os.time() - metrics.LastReset
  local errorRate = uptime > 0 and metrics.ErrorCount / uptime or 0
  return {
    basic = PerformanceMonitor.GetMetrics(),
    cacheHitRate = cacheHitRate,
    averageValidationTime = avgValidationTime,
    errorRate = errorRate,
    uptime = uptime,
    tagAssignmentsByRole = detailedMetrics.tagAssignmentsByRole,
    errorsByType = detailedMetrics.errorsByType,
    securityEventsByType = detailedMetrics.securityEventsByType,
    recentValidationTimes = detailedMetrics.validationTimes,
  }
end

--[[
	Resets all performance metrics.
    
    @returns nil
]]
function PerformanceMonitor.Reset(): ()
  metrics = {
    TagAssignments = 0,
    ValidationTime = 0,
    SecurityEvents = 0,
    CacheHits = 0,
    CacheMisses = 0,
    ErrorCount = 0,
    LastReset = os.time(),
  }
  detailedMetrics = {
    validationTimes = {},
    tagAssignmentsByRole = {},
    errorsByType = {},
    securityEventsByType = {},
    performanceHistory = {},
  }
  Utils.log(TAG, "Performance metrics reset")
end

--[[
    Creates a performance snapshot.
    
    @returns nil
]]
function PerformanceMonitor.CreateSnapshot(): ()
  local snapshot = {
    timestamp = os.time(),
    metrics = PerformanceMonitor.GetMetrics(),
    detailed = PerformanceMonitor.GetDetailedMetrics(),
  }
  table.insert(detailedMetrics.performanceHistory, snapshot)
  if #detailedMetrics.performanceHistory > 24 then
    table.remove(detailedMetrics.performanceHistory, 1)
  end
  Utils.log(TAG, "Performance snapshot created")
end

--[[
    Gets the performance history.
    
    @returns {Types.PerformanceSnapshot} The performance history.
]]
function PerformanceMonitor.GetHistory(): { Types.PerformanceSnapshot }
  return detailedMetrics.performanceHistory
end

--[[
    Checks for performance issues.
    
    @returns {string} An array of performance issue descriptions.
]]
function PerformanceMonitor.CheckPerformanceHealth(): { string }
  local issues: { string } = {}
  local detailed = PerformanceMonitor.GetDetailedMetrics()
  if detailed.averageValidationTime > PERFORMANCE_THRESHOLDS.MaxValidationTime * 1000 then
    table.insert(issues, "High validation time: " .. detailed.averageValidationTime .. "ms")
  end
  if detailed.errorRate > PERFORMANCE_THRESHOLDS.MaxErrorRate then
    table.insert(issues, "High error rate: " .. (detailed.errorRate * 100) .. "%")
  end
  if detailed.cacheHitRate < 0.8 and metrics.CacheHits + metrics.CacheMisses > 100 then
    table.insert(issues, "Low cache hit rate: " .. (detailed.cacheHitRate * 100) .. "%")
  end
  return issues
end

--[[
    Generates a performance report.
    
    @returns string The performance report as a string.
]]
function PerformanceMonitor.GenerateReport(): string
  local detailed = PerformanceMonitor.GetDetailedMetrics()
  local issues = PerformanceMonitor.CheckPerformanceHealth()
  local report: { string } = {
    "=== TBRDS Performance Report ===",
    "Uptime: " .. detailed.uptime .. " seconds",
    "Tag Assignments: " .. metrics.TagAssignments,
    "Average Validation Time: " .. detailed.averageValidationTime .. "ms",
    "Cache Hit Rate: " .. (detailed.cacheHitRate * 100) .. "%",
    "Error Rate: " .. (detailed.errorRate * 100) .. "%",
    "Security Events: " .. metrics.SecurityEvents,
    "",
    "=== Performance Issues ===",
  }
  if #issues > 0 then
    for _, issue in issues do
      table.insert(report, "⚠️ " .. issue)
    end
  else
    table.insert(report, "✅ No performance issues detected")
  end
  return table.concat(report, "\n")
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================
if Configuration.Settings.EnablePerformanceMetrics then
  Utils.log(TAG, "Performance monitoring initialized")
else
  Utils.warn(TAG, "Performance monitoring is disabled in configuration")
end

-- ============================================================================
-- EVENTS
-- ============================================================================
if RunService:IsServer() and Configuration.Settings.EnablePerformanceMetrics then
  task.spawn(function()
    while true do
      -- Create a snapshot every hour
      task.wait(3600)
      PerformanceMonitor.CreateSnapshot()
      local issues = PerformanceMonitor.CheckPerformanceHealth()
      if #issues > 0 then
        Utils.warn(TAG, "TBRDS Performance Issues Detected:")
        for _, issue in issues do
          warn("  " .. issue)
        end
      end
    end
  end)
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return PerformanceMonitor
