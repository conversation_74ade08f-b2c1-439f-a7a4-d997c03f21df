--!strict

--[[
    - file: TBRDS_EventSystem.luau
    - version: 2.1.0
    - author: BleckWolf25
    - contributors:
    - copyright: Dynamic Innovative Studio
    - description:
      - Centralized event system for the Tag-Based Role Display System (TBRDS)
      - Provides a type-safe, performance-oriented interface for event management
      - Supports subscription, firing, metrics, and history tracking
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService: RunService = game:GetService("RunService")

-- ============================================================================
-- PATHS
-- ============================================================================
local tbrdsFolder = ReplicatedStorage.TBRDS
local sharedFolder = tbrdsFolder.Shared

-- ============================================================================
-- MODULES
-- ============================================================================
local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Types = require(tbrdsFolder.TBRDS_Types)
local Utils = require(sharedFolder.TBRDS_Utils)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local EventSystem = {}

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local TAG: string = "EventSystem"
local MAX_HISTORY_SIZE: number = 100
local CLEANUP_INTERVAL: number = 300 -- 5 minutes in seconds
local HISTORY_RETENTION_TIME: number = 3600 -- 1 hour in seconds

-- ============================================================================
-- VARIABLES
-- ============================================================================
local eventSubscribers: Types.EventSubscribers = {}
local eventHistory: { Types.TagEventData } = {}
local subscriptionCounter: number = 0
local eventMetrics: Types.EventMetrics = {
  eventsProcessed = 0,
  subscribersNotified = 0,
  errorCount = 0,
  lastReset = os.time(),
}
local eventTypes: { Types.TagEventType } = {
  Types.TBRDSConstants.EVENT_NAMES.TagChanged,
  Types.TBRDSConstants.EVENT_NAMES.TagAssigned,
  Types.TBRDSConstants.EVENT_NAMES.TagRemoved,
  Types.TBRDSConstants.EVENT_NAMES.RoleValidated,
  Types.TBRDSConstants.EVENT_NAMES.SecurityViolation,
  Types.TBRDSConstants.EVENT_NAMES.ConfigurationChanged,
}

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

--[[
    Generates a unique subscription identifier.
    
    @returns string A unique string identifier for the subscription.
]]
local function generateSubscriptionId(): string
  subscriptionCounter += 1
  return "sub_" .. subscriptionCounter .. "_" .. os.time()
end

--[[
    Validates event data structure and required fields.
    
    @param eventType Types.TagEventType The type of event being validated.
    @param eventData Types.TagEventData The event data to validate.
    
    @returns boolean True if the event data is valid, false otherwise.
]]
local function validateEventData(
  eventType: Types.TagEventType,
  eventData: Types.TagEventData
): boolean
  if not eventData.Player or not eventData.Player:IsA("Player") then
    Utils.warn(TAG, "Invalid Player in " .. eventType .. " event")
    return false
  end
  if not eventData.NewRole or type(eventData.NewRole) ~= "string" or eventData.NewRole == "" then
    Utils.warn(TAG, "Invalid NewRole in " .. eventType .. " event")
    return false
  end
  if
    not eventData.Timestamp
    or type(eventData.Timestamp) ~= "number"
    or eventData.Timestamp <= 0
  then
    Utils.warn(TAG, "Invalid Timestamp in " .. eventType .. " event")
    return false
  end
  if not eventData.Source or type(eventData.Source) ~= "string" or eventData.Source == "" then
    Utils.warn(TAG, "Invalid Source in " .. eventType .. " event")
    return false
  end
  return true
end

--[[
    Adds an event to history with automatic size management.
    
    @param eventData Types.TagEventData The event data to add to history.
    
    @returns nil
]]
local function addToHistory(eventData: Types.TagEventData): ()
  table.insert(eventHistory, eventData)
  if #eventHistory > MAX_HISTORY_SIZE then
    table.remove(eventHistory, 1)
  end
end

--[[
    Safely invokes a callback with error handling.
    
    @param callback Types.EventCallback The callback function to invoke.
    @param eventData Types.TagEventData The event data to pass to the callback.
    @param subscriptionId string The subscription ID for error reporting.
    
    @returns boolean True if the callback was successfully invoked, false otherwise.
]]
local function safelyInvokeCallback(
  callback: Types.EventCallback,
  eventData: Types.TagEventData,
  subscriptionId: string
): boolean
  local success, errorMessage = pcall(callback, eventData)
  if not success then
    Utils.warn(TAG, "Error in callback '" .. subscriptionId .. "': " .. tostring(errorMessage))
    eventMetrics.errorCount += 1
    return false
  end
  return true
end

-- ============================================================================
-- PUBLIC METHODS
-- ============================================================================

--[[
    Subscribes to a specific event type with a callback function.
    
    @param eventType Types.TagEventType The event type to subscribe to.
    @param callback Types.EventCallback The function to call when the event is fired.
    
    @returns string A unique subscription ID, or empty string if subscription failed.
]]
function EventSystem.Subscribe(eventType: Types.TagEventType, callback: Types.EventCallback): string
  if not eventSubscribers[eventType] then
    Utils.warn(TAG, "Unknown event type '" .. eventType .. "'")
    return ""
  end
  if type(callback) ~= "function" then
    Utils.warn(TAG, "Callback must be a function")
    return ""
  end
  local subscriptionId = generateSubscriptionId()
  eventSubscribers[eventType][subscriptionId] = callback
  Utils.log(TAG, "Subscribed to '" .. eventType .. "' with ID '" .. subscriptionId .. "'")
  return subscriptionId
end

--[[
    Removes a subscription using its unique ID.
    
    @param subscriptionId string The subscription ID returned by Subscribe().
    
    @returns boolean True if the unsubscription was successful, false otherwise.
]]
function EventSystem.Unsubscribe(subscriptionId: string): boolean
  for eventType, subscribers in pairs(eventSubscribers) do
    if subscribers[subscriptionId] then
      subscribers[subscriptionId] = nil
      Utils.log(TAG, "Unsubscribed '" .. subscriptionId .. "' from '" .. eventType .. "'")
      return true
    end
  end
  Utils.warn(TAG, "Subscription ID '" .. subscriptionId .. "' not found")
  return false
end

--[[
    Fires an event to all subscribers of the specified type.
    
    @param eventType Types.TagEventType The type of event to fire.
    @param eventData Types.TagEventData The event data to send to subscribers.
    
    @returns nil
]]
function EventSystem.Fire(eventType: Types.TagEventType, eventData: Types.TagEventData): ()
  if not eventSubscribers[eventType] then
    Utils.warn(TAG, "Unknown event type '" .. eventType .. "'")
    return
  end
  if not validateEventData(eventType, eventData) then
    Utils.warn(TAG, "Invalid event data for '" .. eventType .. "'")
    eventMetrics.errorCount += 1
    return
  end
  addToHistory(eventData)
  local subscriberCount = 0
  for subscriptionId, callback in pairs(eventSubscribers[eventType]) do
    subscriberCount += 1
    safelyInvokeCallback(callback, eventData, subscriptionId)
  end
  eventMetrics.eventsProcessed += 1
  eventMetrics.subscribersNotified += subscriberCount
  Utils.log(TAG, "Fired '" .. eventType .. "' event to " .. subscriberCount .. " subscribers")
end

--[[
    Gets all callback functions subscribed to a specific event type.
    
    @param eventType Types.TagEventType The event type to query.
    
    @returns {Types.EventCallback} An array of callback functions, empty if no subscribers or invalid event type.
]]
function EventSystem.GetSubscribers(eventType: Types.TagEventType): { Types.EventCallback }
  if not eventSubscribers[eventType] then
    return {}
  end
  local callbacks = {}
  for _, callback in pairs(eventSubscribers[eventType]) do
    table.insert(callbacks, callback)
  end
  return callbacks
end

--[[
    Gets the complete history of fired events.
    
    @returns {Types.TagEventData} An array of historical event data.
]]
function EventSystem.GetEventHistory(): { Types.TagEventData }
  local historyCopy = {}
  for _, eventData in ipairs(eventHistory) do
    table.insert(historyCopy, eventData)
  end
  return historyCopy
end

--[[
    Gets comprehensive performance and usage metrics.
    
    @returns Types.EventMetrics A table containing performance statistics and system health information.
]]
function EventSystem.GetMetrics(): Types.EventMetrics
  local activeSubscriptions = 0
  for _, subscribers in pairs(eventSubscribers) do
    for _ in pairs(subscribers) do
      activeSubscriptions += 1
    end
  end
  return {
    eventsProcessed = eventMetrics.eventsProcessed,
    subscribersNotified = eventMetrics.subscribersNotified,
    errorCount = eventMetrics.errorCount,
    lastReset = os.time() - eventMetrics.lastReset,
    activeSubscriptions = activeSubscriptions,
    historySize = #eventHistory,
  }
end

--[[
    Resets all performance metrics to zero.
    
    @returns nil
]]
function EventSystem.ResetMetrics(): ()
  eventMetrics.eventsProcessed = 0
  eventMetrics.subscribersNotified = 0
  eventMetrics.errorCount = 0
  eventMetrics.lastReset = os.time()
  Utils.log(TAG, "Event metrics reset")
end

--[[
    Removes all event subscriptions across all event types.
    
    @returns nil
]]
function EventSystem.ClearAllSubscriptions(): ()
  for eventType in pairs(eventSubscribers) do
    eventSubscribers[eventType] = {}
  end
  Utils.log(TAG, "All event subscriptions cleared")
end

--[[
    Creates standardized event data structures.
    
    @param player Player The player associated with this event.
    @param newRole string The new role being assigned.
    @param oldRole string? The previous role (optional).
    @param source string Description of what triggered this event.
    @param metadata { [string]: any }? Additional custom data (optional).
    
    @returns Types.TagEventData A properly formatted event data structure.
]]
function EventSystem.CreateEventData(
  player: Player,
  newRole: string,
  oldRole: string?,
  source: string,
  metadata: { [string]: any }?
): Types.TagEventData
  assert(player and player:IsA("Player"), "Player must be a valid Player instance")
  assert(type(newRole) == "string" and newRole ~= "", "NewRole must be a non-empty string")
  assert(type(source) == "string" and source ~= "", "Source must be a non-empty string")
  return {
    Player = player,
    NewRole = newRole :: Types.RoleName,
    OldRole = oldRole,
    Timestamp = os.time(),
    Source = source,
    Metadata = metadata,
  }
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

--[[
    Initializes the event subscriber tables for all defined event types.
    
    @returns nil
]]
local function initializeEventSubscribers(): ()
  for _, eventName in ipairs(eventTypes) do
    eventSubscribers[eventName] = {}
  end
end

-- Initialize the event system if enabled in configuration
if TBRDSConfig.Settings.EnableEventSystem then
  initializeEventSubscribers()
  Utils.log(TAG, "Event system initialized")
else
  Utils.warn(TAG, "Event system is disabled in configuration")
end

-- ============================================================================
-- EVENTS
-- ============================================================================

-- Set up automatic history cleanup on the server
if RunService:IsServer() and TBRDSConfig.Settings.EnableEventSystem then
  task.spawn(function()
    while true do
      task.wait(CLEANUP_INTERVAL)
      local cutoffTime = os.time() - HISTORY_RETENTION_TIME
      local newHistory = {}
      for _, eventData in ipairs(eventHistory) do
        if eventData.Timestamp > cutoffTime then
          table.insert(newHistory, eventData)
        end
      end
      local removedCount = #eventHistory - #newHistory
      eventHistory = newHistory
      if removedCount > 0 then
        Utils.log(
          TAG,
          "Cleaned "
            .. removedCount
            .. " old events from history, "
            .. #eventHistory
            .. " events remaining"
        )
      end
    end
  end)
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return EventSystem
