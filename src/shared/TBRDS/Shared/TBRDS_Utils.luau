-- --!strict

--[[
    - file: TBRDS_Utils.luau

    - version: 2.1.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Centralized utility functions for the TBRDS (Tag-Based Role Display System)
      - Used for reusability across multiple TBRDS modules

    - Features:
      - Logging
      - Rate Limiting
      - Security & Validation
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Types = require(script.Parent.Parent.TBRDS_Types)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Utils = {}

-- ============================================================================
-- LOGGING UTILITIES
-- ============================================================================

--[[
    Formats a log tag consistently for TBRDS outputs.

    @param tag		-- Module or system name

    @return string	-- Formatted log prefix
]]
function Utils.formatTag(tag: string): string
  return "[TBRDS::" .. tag .. "]"
end

--[[
    Logs a message if DebugMode is enabled in configuration.

    @param tag	-- Source of the log
    @param any	-- Values to print

    @return nil
]]
function Utils.log(tag: string, ...): ()
  if Configuration.Settings.DebugMode then
    print(Utils.formatTag(tag), ...)
  end
end

--[[
    Logs a warning message if DebugMode is enabled in configuration.

    @param tag	-- Source of the warn
    @param any	-- Values to print

    @return nil
]]
function Utils.warn(tag: string, ...): ()
  if Configuration.Settings.DebugMode then
    warn(Utils.formatTag(tag), ...)
  end
end

-- ============================================================================
-- SECURITY & VALIDATION FUNCTIONS
-- ============================================================================

--[[
    Validates a Player instance for use in TBRDS operations.

    @param player					-- The player to validate

    @return Types.ValidationResult	-- Validation success and error details
]]
function Utils.validatePlayer(player: Player): Types.ValidationResult
  local result: Types.ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }

  if not player or not player:IsA("Player") then
    result.ErrorCode = Configuration.ErrorCodes.INVALID_PLAYER
    result.ErrorMessage = "Invalid player object"
    return result
  end

  if not player.Parent then
    result.ErrorCode = Configuration.ErrorCodes.INVALID_PLAYER
    result.ErrorMessage = "Player no longer in game"
    return result
  end

  if not player.UserId or player.UserId <= 0 then
    result.ErrorCode = Configuration.ErrorCodes.INVALID_PLAYER
    result.ErrorMessage = "Invalid player UserId"
    return result
  end

  result.Success = true
  return result
end

-- ======================================================================
-- EXPORTS
-- ======================================================================
return Utils
