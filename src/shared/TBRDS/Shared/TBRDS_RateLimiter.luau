--!strict

--[[
    - file: TBRDS_RateLimiter.luau
    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Centralized rate limiter functions for the TBRDS (Tag-Based Role Display System)
      - Used for reusability across TBRDS modules
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local PerformanceMonitor = require(script.Parent.TBRDS_PerformanceMonitor)
local Types = require(script.Parent.Parent.TBRDS_Types)
local Utils = require(script.Parent.TBRDS_Utils)

-- ============================================================================
-- CONSTANTS & VARIABLES
-- ============================================================================

-- Logging
local TAG: string = "RateLimiter"

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local RateLimiter = {}

-- ============================================================================
-- RATE LIMITER
-- ============================================================================

--[[
    Creates an enhanced rate limiter with configurable window and limit.
    @param window        -- Time window in seconds (optional)
    @param limit         -- Maximum requests in window (optional)
    @return Types.RateLimiter    -- New rate limiter instance
]]
function RateLimiter.createRateLimiter(window: number?, limit: number?): Types.RateLimiter
  local actualWindow = window or Configuration.Settings.RateLimit.Window
  local actualLimit = limit or Configuration.Settings.RateLimit.MaxRequests

  local limiter: Types.RateLimiter = {
    Window = actualWindow,
    Limit = actualLimit,
    Requests = {},

    CheckLimit = function(self: Types.RateLimiter, userId: number): (boolean, number)
      local userData: Types.RateLimitData = self.Requests[userId]
        or {
          Requests = {},
          LastReset = os.time(),
        }
      self.Requests[userId] = userData

      local now: number = os.time()
      local validTimestamps: { number } = {}

      -- Manual loop with guaranteed type
      for i = 1, #userData.Requests do
        local timestamp: number = userData.Requests[i]
        if (now - timestamp) < self.Window then
          table.insert(validTimestamps, timestamp)
        end
      end

      userData.Requests = validTimestamps
      local count: number = #validTimestamps

      if count < self.Limit then
        table.insert(userData.Requests, now)
        PerformanceMonitor.RecordCacheHit()
        return true, count + 1
      else
        PerformanceMonitor.RecordSecurityEvent(Types.TBRDSConstants.ERROR_CODES.RATE_LIMIT_EXCEEDED)
        return false, count
      end
    end,

    Reset = function(self: Types.RateLimiter, userId: number): ()
      self.Requests[userId] = {
        Requests = {},
        LastReset = os.time(),
      }
    end,
  }

  return limiter
end

-- ============================================================================
-- SECURITY & VALIDATION FUNCTIONS
-- ============================================================================

--[[
    Validates a Player instance for use in TBRDS operations.
    @param player                -- The player to validate
    @return Types.ValidationResult    -- Validation success and error details
]]
function RateLimiter.validatePlayer(player: Player): Types.ValidationResult
  local result: Types.ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }

  if not player or not player:IsA("Player") then
    result.ErrorCode = Configuration.ErrorCodes.INVALID_PLAYER
    result.ErrorMessage = "Invalid player object"
    Utils.warn(TAG, "Invalid player object during validation")
    return result
  end

  if not player.Parent then
    result.ErrorCode = Configuration.ErrorCodes.INVALID_PLAYER
    result.ErrorMessage = "Player no longer in game"
    Utils.warn(TAG, "Player no longer in game during validation")
    return result
  end

  if not player.UserId or player.UserId <= 0 then
    result.ErrorCode = Configuration.ErrorCodes.INVALID_PLAYER
    result.ErrorMessage = "Invalid player UserId"
    Utils.warn(TAG, "Invalid player UserId during validation")
    return result
  end

  result.Success = true
  return result
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return RateLimiter
