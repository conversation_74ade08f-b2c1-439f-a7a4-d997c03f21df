--!strict

--[[
    - file: TBRDS_RoleService.luau
    
    - version: 2.1.0
    - author: BleckWolf25
    - contributors:
    
    - copyright: Dynamic Innovative Studio
    
    - description:
      - Role management module for the TBRDS.
      - Provides a centralized, type-safe, and consistent API for managing player roles.
      - Handles role assignment, validation, and retrieval for both server and client.
      - Ensures all roles are initialized and available before use.
]]

-- =============================================================================
-- SERVICES
-- =============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- =============================================================================
-- PATHS
-- =============================================================================
local servicesFolder = script.Parent
local tbrdsFolder = ReplicatedStorage.TBRDS
local sharedFolder = tbrdsFolder.Shared

-- =============================================================================
-- MODULES
-- =============================================================================
local ConfigurationService = require(servicesFolder.TBRDS_ConfigurationService)
local PerformanceMonitor = require(sharedFolder.TBRDS_PerformanceMonitor)
local Types = require(tbrdsFolder.TBRDS_Types)
local Utils = require(sharedFolder.TBRDS_Utils)

-- =============================================================================
-- CONSTANTS
-- =============================================================================

-- Tag for logging
local TAG: string = "RoleService"

-- 5 minutes for cache time-to-live
local CACHE_TTL: number = 300

-- Maximum cache size
local MAX_CACHE_SIZE: number = 1000

-- =============================================================================
-- VARIABLES
-- =============================================================================
local isInitialized: boolean = false
local roleHandlers: { [Types.RoleName]: Types.RoleHandler } = {}
local roleCache: { [Player]: Types.RoleCacheEntry } = {}
local rolePriority: { Types.RoleName } = Types.TBRDSConstants.ROLE_PRIORITY

-- =============================================================================
-- MAIN IMPLEMENTATION
-- =============================================================================
local RoleService = {}

-- =============================================================================
-- PUBLIC METHODS
-- =============================================================================

--[[
    Retrieves the role of a given player, utilizing caching for performance.
    
    @param player	-- The player whose role is to be retrieved
    
    @returns Types.RoleName	-- The role assigned to the player
]]
function RoleService.GetPlayerRole(player: Player): Types.RoleName
  if not player or not player.Parent then
    return "User"
  end

  local cached = roleCache[player]
  if cached and (os.time() - cached.timestamp) < CACHE_TTL then
    PerformanceMonitor.RecordCacheHit()
    return cached.role
  end

  PerformanceMonitor.RecordCacheMiss()

  local startTime = tick()
  local role = RoleService.DeterminePlayerRole(player)
  local validationTime = (tick() - startTime) * 1000

  PerformanceMonitor.RecordValidationTime(validationTime)

  roleCache[player] = {
    role = role,
    timestamp = os.time(),
    validationCount = (cached and cached.validationCount or 0) + 1,
  }

  if RoleService.GetCacheSize() > MAX_CACHE_SIZE then
    RoleService.CleanupCache()
  end

  return role
end

--[[
    Determines the player's role based on priority order of role handlers.
    
    @param player	-- The player whose role is to be determined
    
    @returns Types.RoleName	-- The determined role of the player
]]
function RoleService.DeterminePlayerRole(player: Player): Types.RoleName
  for _, roleName in ipairs(rolePriority) do
    local roleHandler = roleHandlers[roleName]
    if roleHandler and roleHandler.Check then
      local success, result = pcall(roleHandler.Check, player)
      if success and result then
        Utils.log(TAG, "Player %s assigned role: %s", player.Name, roleName)
        return roleName
      end
    end
  end

  Utils.log(TAG, "Player %s assigned default role: User", player.Name)
  return "User"
end

--[[
    Validates if a player qualifies for a specific role.
    
    @param player	-- The player to validate
    @param roleName	-- The role to check against
    
    @returns Types.ValidationResult	-- The result of the validation
]]
function RoleService.ValidatePlayerRole(
  player: Player,
  roleName: Types.RoleName
): Types.ValidationResult
  local result: Types.ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }

  if not player or not player.Parent then
    result.ErrorCode = ConfigurationService.GetErrorCodes().INVALID_PLAYER
    result.ErrorMessage = Utils.warn(TAG, "Invalid player")
    return result
  end

  local roleHandler = roleHandlers[roleName]
  if not roleHandler then
    result.ErrorCode = ConfigurationService.GetErrorCodes().INVALID_ROLE
    result.ErrorMessage = Utils.warn(TAG, "Unknown role: %s", roleName)
    return result
  end

  local success, qualifies = pcall(roleHandler.Check, player)
  if not success then
    result.ErrorCode = ConfigurationService.GetErrorCodes().VALIDATION_FAILED
    result.ErrorMessage = Utils.warn(TAG, "Role validation failed: %s", tostring(qualifies))
    PerformanceMonitor.RecordError("ROLE_VALIDATION_ERROR")
    return result
  end

  if qualifies then
    result.Success = true
    result.Role = roleName
  else
    result.ErrorCode = ConfigurationService.GetErrorCodes().VALIDATION_FAILED
    result.ErrorMessage = Utils.warn(TAG, "Player does not qualify for role: %s", roleName)
  end

  return result
end

--[[
    Retrieves style information for a given role.
    
    @param roleName	-- The role to get style for
    
    @returns Types.RoleStyle?	-- The style of the role, or nil if not found
]]
function RoleService.GetRoleStyle(roleName: Types.RoleName): Types.RoleStyle?
  local roleHandler = roleHandlers[roleName]
  if roleHandler and roleHandler.Style then
    return roleHandler.Style
  end
  return nil
end

--[[
    Checks if a role is valid and exists in the system.
    
    @param roleName	-- The role to check
    
    @returns boolean -- If the role is valid, false otherwise
]]
function RoleService.IsValidRole(roleName: Types.RoleName): boolean
  return roleHandlers[roleName] ~= nil
end

--[[
    Retrieves a list of all available roles.
    
    @returns {Types.RoleName}	-- A list of available role names
]]
function RoleService.GetAvailableRoles(): { Types.RoleName }
  local roles = {}
  for roleName in pairs(roleHandlers) do
    table.insert(roles, roleName)
  end
  return roles
end

--[[
    Retrieves the role priority list.
    
    @returns {Types.RoleName}	-- The list of roles in priority order
]]
function RoleService.GetRolePriority(): { Types.RoleName }
  return rolePriority
end

--[[
    Refreshes a player's role by clearing cache and re-evaluating.
    
    @param player			-- The player whose role is to be refreshed
    
    @returns Types.RoleName	-- The refreshed role of the player
]]
function RoleService.RefreshPlayerRole(player: Player): Types.RoleName
  roleCache[player] = nil
  local role = RoleService.GetPlayerRole(player)
  Utils.log(TAG, "Refreshed role for %s: %s", player.Name, role)
  return role
end

--[[
    Retrieves a list of players with a specific role.
    
    @param roleName		-- The role to filter players by
    
    @returns {Player}	-- A list of players with the specified role
]]
function RoleService.GetPlayersWithRole(roleName: Types.RoleName): { Player }
  local players = {}
  for player in pairs(roleCache) do
    if player.Parent and roleCache[player].role == roleName then
      table.insert(players, player)
    end
  end
  return players
end

--[[
    Retrieves statistics on the number of players per role.
    
    @returns {[Types.RoleName]: number}	-- A dictionary with role names and player counts
]]
function RoleService.GetRoleStatistics(): { [Types.RoleName]: number }
  local stats = {}
  for player in pairs(roleCache) do
    if player.Parent then
      local role = roleCache[player].role
      stats[role] = (stats[role] or 0) + 1
    end
  end
  return stats
end

--[[
    Cleans up expired or invalid cache entries.
    
    @returns nil
]]
function RoleService.CleanupCache(): ()
  local currentTime = os.time()
  local removedCount = 0
  for player, data in pairs(roleCache) do
    if not player.Parent or (currentTime - data.timestamp) > CACHE_TTL then
      roleCache[player] = nil
      removedCount = removedCount + 1
    end
  end
  if removedCount > 0 then
    Utils.log(TAG, "Cleaned up %d expired cache entries", removedCount)
  end
end

--[[
    Retrieves the current size of the cache.
    
    @returns number	-- The number of entries in the cache
]]
function RoleService.GetCacheSize(): number
  local count = 0
  for _ in pairs(roleCache) do
    count = count + 1
  end
  return count
end

--[[
    Retrieves detailed cache information for debugging.
    
    @returns {[string]: any}	-- A dictionary containing cache details
]]
function RoleService.GetCacheInfo(): { [string]: any }
  local info = {
    size = RoleService.GetCacheSize(),
    maxSize = MAX_CACHE_SIZE,
    ttl = CACHE_TTL,
    entries = {},
  }
  for player, data in pairs(roleCache) do
    if player.Parent then
      table.insert(info.entries, {
        playerName = player.Name,
        role = data.role,
        age = os.time() - data.timestamp,
        validationCount = data.validationCount,
      })
    end
  end
  return info
end

--[[
    Handles cleanup when a player leaves the game.
    
    @param player	-- The player who left
    
    @returns nil
]]
function RoleService.HandlePlayerLeaving(player: Player): ()
  if roleCache[player] then
    roleCache[player] = nil
    Utils.log(TAG, "Removed cache entry for leaving player: %s", player.Name)
  end
end

--[[
    Retrieves the current status of the role service.
    
    @returns {[string]: any}	-- A dictionary containing service status information
]]
function RoleService.GetServiceStatus(): { [string]: any }
  return {
    initialized = isInitialized,
    roleHandlersLoaded = (function()
      local count = 0
      for _ in pairs(roleHandlers) do
        count = count + 1
      end
      return count
    end)(),
    cacheSize = RoleService.GetCacheSize(),
    rolePriorityCount = #rolePriority,
  }
end

--[[
    Resets the role service, clearing all data.
    
    @returns nil
]]
function RoleService.Cleanup(): ()
  roleCache = {}
  roleHandlers = {}
  rolePriority = {}
  isInitialized = false
  Utils.log(TAG, "Role service cleaned up")
end

-- =============================================================================
-- INITIALIZATION
-- =============================================================================

--[[
    Initializes the role service by loading configurations and role handlers, setting up cache cleanup.
    
    @returns boolean -- Indicates whether initialization was successful
]]
function RoleService.Initialize(): boolean
  if isInitialized then
    Utils.log(TAG, "Role service already initialized")
    return true
  end

  Utils.log(TAG, "Initializing role service...")

  local config = ConfigurationService.GetConfiguration()
  rolePriority = config.RolePriority

  local success, RoleHandlers = pcall(function()
    return require(script.Parent.Parent.TBRDS_RoleHandler) :: Types.RoleHandlersModule
  end)

  if not success then
    Utils.warn(TAG, "Failed to load role handlers: " .. tostring(RoleHandlers))
    return false
  end

  for _, roleName in ipairs(rolePriority) do
    local handler = RoleHandlers[roleName]
    if
      handler
      and type(handler) == "table"
      and type(handler.Check) == "function"
      and type(handler.Style) == "table"
    then
      roleHandlers[roleName] = handler :: Types.RoleHandler
      Utils.log(TAG, "Loaded role handler: %s", roleName)
    else
      Utils.warn(TAG, "Missing or invalid role handler for: %s", roleName)
    end
  end

  task.spawn(function()
    while isInitialized do
      task.wait(60)
      RoleService.CleanupCache()
    end
  end)

  isInitialized = true
  Utils.log(TAG, "Role service initialized successfully")
  return true
end

-- =============================================================================
-- EXPORTS
-- =============================================================================
return RoleService
