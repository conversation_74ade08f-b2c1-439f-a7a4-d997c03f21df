--!strict

--[[
  - file: TBRDS_ServiceManager.luau

  - version: 2.1.0
  - author: BleckWolf25
  - contributors:

  - description:
    - Service Manager for TBRDS, responsible for initializing, managing, and monitoring services.
    - Provides health checks, periodic validation, and emergency recovery.
    - Handles service dependencies and initialization order.
    - Provides a summary of service statuses and system health.
]]

-- =============================================================================
-- SERVICES
-- =============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService: RunService = game:GetService("RunService")

-- =============================================================================
-- PATHS
-- =============================================================================
local servicesFolder = script.Parent
local tbrdsFolder = ReplicatedStorage.TBRDS
local sharedFolder = tbrdsFolder.Shared

-- =============================================================================
-- Modules
-- =============================================================================
local BillboardService = require(servicesFolder.TBRDS_BillboardService)
local ConfigurationService = require(servicesFolder.TBRDS_ConfigurationService)
local RoleService = require(servicesFolder.TBRDS_RoleService)
local TagService = require(servicesFolder.TBRDS_TagService)

local EventSystem = require(sharedFolder.TBRDS_EventSystem)
local PerformanceMonitor = require(sharedFolder.TBRDS_PerformanceMonitor)
local Types = require(tbrdsFolder.TBRDS_Types)
local Utils = require(sharedFolder.TBRDS_Utils)

-- =============================================================================
-- CONSTANTS
-- =============================================================================

-- Constant tag of logging context
local TAG: string = "ServiceManager"

-- Health Check Interval
local healthCheckInterval: number = 30

-- =============================================================================
-- VARIABLES
-- =============================================================================

-- Order of initialization
local initializationOrder: { string } = { "Configuration", "Role", "Billboard", "Tag" }

-- Services
local services: { [string]: Types.ServiceModule } = {
  Configuration = ConfigurationService :: Types.ServiceModule,
  Role = RoleService :: Types.ServiceModule,
  Billboard = BillboardService :: Types.ServiceModule,
  Tag = TagService :: Types.ServiceModule,
}

-- Service Status
local serviceStatus: {
  [string]: {
    initialized: boolean,
    healthy: boolean,
    lastCheck: number,
  },
} =
  {}

-- =============================================================================
-- STATES
-- =============================================================================

-- State of initialization
local isInitialized: boolean = false

-- =============================================================================
-- MAIN IMPLEMENTATION
-- =============================================================================
local ServiceManager = {}

-- =============================================================================
-- PUBLIC METHODS
-- =============================================================================

--[[
  Retrieves a specific service by name if it is initialized.
  
  @param serviceName	-- The name of the service to retrieve
  
  @returns ServiceModule?	-- The service module if found and initialized, otherwise nil
]]
function ServiceManager.GetService(serviceName: string): Types.ServiceModule?
  local service = services[serviceName]
  if not service then
    Utils.warn(TAG, "Service '%s' not found", serviceName)
    return nil
  end

  local status = serviceStatus[serviceName]
  if not status then
    Utils.warn(TAG, "Service '%s' not found in serviceStatus", serviceName)
    return nil
  elseif not status.initialized then
    Utils.warn(TAG, "Service '%s' not initialized", serviceName)
    return nil
  end

  return service
end

--[[
  Retrieves all services.
  
  @returns { [string]: ServiceModule }	-- A dictionary of all services
]]
function ServiceManager.GetAllServices(): { [string]: Types.ServiceModule }
  return services
end

--[[
  Checks if all services are initialized.
  
  @returns boolean	-- If all services are initialized, false otherwise
]]
function ServiceManager.AreAllServicesInitialized(): boolean
  for serviceName in pairs(services) do
    local status = serviceStatus[serviceName]
    if not status or not status.initialized then
      return false
    end
  end
  return true
end

--[[
  Checks if all services are healthy.
  
  @returns boolean	-- If all services are healthy, false otherwise
]]
function ServiceManager.AreAllServicesHealthy(): boolean
  for serviceName in pairs(services) do
    local status = serviceStatus[serviceName]
    if not status or not status.healthy then
      return false
    end
  end
  return true
end

--[[
  Starts the health monitoring for services.
  
  @returns nil
]]
function ServiceManager.StartHealthMonitoring(): ()
  if not RunService:IsServer() then
    return
  end

  task.spawn(function()
    while isInitialized do
      task.wait(healthCheckInterval)
      ServiceManager.PerformHealthCheck()
    end
  end)

  Utils.log(TAG, "Health monitoring started")
end

--[[
  Performs a health check on all services.
  
  @returns nil
]]
function ServiceManager.PerformHealthCheck(): ()
  for serviceName, service in pairs(services) do
    local status = serviceStatus[serviceName]
    if status and status.initialized then
      local isHealthy = ServiceManager.CheckServiceHealth(service)
      status.healthy = isHealthy
      status.lastCheck = os.time()

      if not isHealthy then
        Utils.warn(TAG, "Service '%s' health check failed", serviceName)
        PerformanceMonitor.RecordError("SERVICE_HEALTH_CHECK_FAILED")
      end
    end
  end
end

--[[
  Checks the health of a specific service.
  
  @param serviceName	-- The name of the service (unused in this function)
  
  @param service	-- The service module to check
  
  @returns boolean	-- If the service is healthy, false otherwise
]]
function ServiceManager.CheckServiceHealth(service: Types.ServiceModule): boolean
  local success, result = pcall(function()
    if service.GetServiceStatus then
      local status = service.GetServiceStatus()
      return status and status.initialized == true
    end

    -- Service exists, assume healthy if no status check
    return true
  end)

  if success then
    return result
  else
    return false
  end
end

--[[
  Starts periodic validation of player tags.
  
  @returns nil
]]
function ServiceManager.StartPeriodicValidation(): ()
  if not RunService:IsServer() then
    return
  end

  task.spawn(function()
    while isInitialized do
      task.wait(60) -- Every minute
      local tagService = ServiceManager.GetService("Tag")
      if tagService and tagService.RefreshPlayerTag then
        local players = game.Players:GetPlayers()
        for _, player in ipairs(players) do
          if player.Parent then
            tagService.RefreshPlayerTag(player)
          end
        end
      end
    end
  end)

  Utils.log(TAG, "Periodic validation started")
end

--[[
  Retrieves a summary of the service statuses.
  
  @returns
]]
function ServiceManager.GetServiceStatusSummary(): {
  managerInitialized: boolean,
  totalServices: number,
  initializedServices: number,
  healthyServices: number,
  services: { [string]: Types.ServiceStatus },
}
  local summary: {
    managerInitialized: boolean,
    totalServices: number,
    initializedServices: number,
    healthyServices: number,
    services: { [string]: Types.ServiceStatus },
  } =
    {
      managerInitialized = isInitialized,
      totalServices = 0,
      initializedServices = 0,
      healthyServices = 0,
      services = {},
    }

  for serviceName, service in pairs(services) do
    summary.totalServices = summary.totalServices + 1
    local status = serviceStatus[serviceName]
    local serviceInfo: Types.ServiceStatus = {
      initialized = status and status.initialized or false,
      healthy = status and status.healthy or false,
      lastCheck = status and status.lastCheck or 0,
      details = nil,
    }
    if service and service.GetServiceStatus then
      local ok, detailedStatus = pcall(service.GetServiceStatus)
      if ok and detailedStatus then
        serviceInfo.details = detailedStatus :: { [string]: any }
      end
    end
    summary.services[serviceName] = serviceInfo
    if serviceInfo.initialized then
      summary.initializedServices = summary.initializedServices + 1
    end
    if serviceInfo.healthy then
      summary.healthyServices = summary.healthyServices + 1
    end
  end

  return summary
end

--[[
  Generates a system health report as a string.
  
  @returns string -- The health report
]]
function ServiceManager.GetSystemHealthReport(): string
  local summary: {
    managerInitialized: boolean,
    totalServices: number,
    initializedServices: number,
    healthyServices: number,
    services: {
      [string]: {
        initialized: boolean,
        healthy: boolean,
        lastCheck: number,
        details: { [string]: any }?,
      },
    },
  } =
    ServiceManager.GetServiceStatusSummary()
  local report = {
    "=== TBRDS System Health Report ===",
    string.format(
      "Manager Initialized: %s",
      tostring(summary.managerInitialized and "✅" or "❌")
    ),
    string.format(
      "Services: %d/%d initialized, %d/%d healthy",
      summary.initializedServices,
      summary.totalServices,
      summary.healthyServices,
      summary.totalServices
    ),
    "",
    "=== Service Details ===",
  }

  for serviceName: string, serviceInfo in pairs(summary.services) do
    local statusIcon = serviceInfo.healthy and "✅" or "❌"
    local initIcon = serviceInfo.initialized and "✅" or "❌"
    table.insert(
      report,
      string.format(
        "%s %s Service - Init: %s, Healthy: %s",
        statusIcon,
        serviceName,
        initIcon,
        serviceInfo.healthy and "Yes" or "No"
      )
    )
    if serviceInfo.details and type(serviceInfo.details) == "table" then
      for key, value in pairs(serviceInfo.details) do
        if value ~= nil and type(value) ~= "table" then
          table.insert(report, string.format("  %s: %s", key, tostring(value)))
        end
      end
    end
  end

  local metrics: Types.DetailedMetricsResult = PerformanceMonitor.GetDetailedMetrics()
  table.insert(report, "")
  table.insert(report, "=== Performance Metrics ===")
  table.insert(report, string.format("Tag Assignments: %d", metrics.basic.TagAssignments))
  table.insert(report, string.format("Security Events: %d", metrics.basic.SecurityEvents))
  table.insert(report, string.format("Cache Hits: %d", metrics.basic.CacheHits))
  table.insert(report, string.format("Cache Misses: %d", metrics.basic.CacheMisses))
  table.insert(report, string.format("Errors: %d", metrics.basic.ErrorCount))

  return table.concat(report, "\n")
end

--[[
  Restarts a specific service.
  
  @param serviceName	-- The name of the service to restart
  
  @returns boolean 	-- If the service was successfully restarted, false otherwise
]]
function ServiceManager.RestartService(serviceName: string): boolean
  local service = services[serviceName]
  if not service or not service.Cleanup then
    Utils.warn(TAG, "Service '%s' not found or missing Cleanup", serviceName)
    return false
  end
  Utils.log(TAG, "Restarting %s service...", serviceName)
  local ok = pcall(service.Cleanup)
  if not ok then
    Utils.warn(TAG, "Error during %s service cleanup", serviceName)
  end
  local reinitSuccess = service.Initialize()
  if reinitSuccess then
    serviceStatus[serviceName] = {
      initialized = true,
      healthy = true,
      lastCheck = os.time(),
    }
    Utils.log(TAG, "%s service restarted successfully", serviceName)
  else
    Utils.warn(TAG, "Failed to restart %s service", serviceName)
    serviceStatus[serviceName] = {
      initialized = false,
      healthy = false,
      lastCheck = os.time(),
    }
  end
  return reinitSuccess
end

--[[
  Shuts down the Service Manager and cleans up all services.
  
  @returns nil
]]
function ServiceManager.Shutdown(): ()
  Utils.log(TAG, "Shutting down TBRDS Service Manager...")

  local reverseOrder: { string } = {}
  for i = #initializationOrder, 1, -1 do
    table.insert(reverseOrder, initializationOrder[i])
  end

  for _, serviceName in ipairs(reverseOrder) do
    local service = services[serviceName]
    if service and service.Cleanup then
      Utils.log(TAG, "Cleaning up %s service...", serviceName)
      local ok = pcall(service.Cleanup)
      if not ok then
        Utils.warn(TAG, "Error during %s service cleanup", serviceName)
      end
    end
    serviceStatus[serviceName] = {
      initialized = false,
      healthy = false,
      lastCheck = os.time(),
    }
  end

  isInitialized = false
  Utils.log(TAG, "TBRDS Service Manager shutdown complete")
end

--[[
  Performs an emergency recovery by attempting to restart all services.
  
  @returns boolean	-- If all services were successfully recovered, false otherwise
]]
function ServiceManager.EmergencyRecovery(): boolean
  Utils.warn(TAG, "Performing emergency recovery...")

  local recoveredServices = 0
  for _, serviceName in ipairs(initializationOrder) do
    local ok = ServiceManager.RestartService(serviceName)
    if ok then
      recoveredServices = recoveredServices + 1
    end
  end
  local success = recoveredServices == #initializationOrder
  if success then
    Utils.log(TAG, "Emergency recovery completed successfully")
  else
    Utils.warn(
      TAG,
      "Emergency recovery partially failed - %d/%d services recovered",
      recoveredServices,
      #initializationOrder
    )
  end
  return success
end

-- =============================================================================
-- INITIALIZATION
-- =============================================================================

--[[
  Initializes the Service Manager and all dependent services in the correct order.
  
  @returns boolean	-- If initialization was successful, true, false otherwise
]]
function ServiceManager.Initialize(): boolean
  if isInitialized then
    Utils.log(TAG, "Service manager already initialized")
    return true
  end

  Utils.log(TAG, "Initializing TBRDS Service Manager...")

  Utils.log(TAG, "Initializing TBRDS EventSystem...")
  if EventSystem.ResetMetrics then
    EventSystem.ResetMetrics()
  end

  for _, serviceName in ipairs(initializationOrder) do
    local service = services[serviceName]
    if not service or type(service) ~= "table" or type(service.Initialize) ~= "function" then
      Utils.warn(TAG, "Service '%s' not found or missing Initialize", serviceName)
      return false
    end
    Utils.log(TAG, "Initializing %s service...", serviceName)
    local success = service.Initialize()
    if not success then
      error(string.format("Failed to initialize %s service", serviceName))
      return false
    end
    serviceStatus[serviceName] = {
      initialized = true,
      healthy = true,
      lastCheck = os.time(),
    }
    Utils.log(TAG, "%s service initialized successfully", serviceName)
  end

  ServiceManager.StartHealthMonitoring()
  ServiceManager.StartPeriodicValidation()

  isInitialized = true
  Utils.log(TAG, "TBRDS Service Manager initialized successfully")

  return true
end

-- =============================================================================
-- EXPORTS
-- =============================================================================
return ServiceManager
