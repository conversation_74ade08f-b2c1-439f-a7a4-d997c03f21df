--!strict

--[[
    - file: TBRDS_TagService.luau

    - version: 2.1.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Tag Service Module for Tag-Based Role Display System (TBRDS).
      - Manages player tags, including assignment, validation, and retrieval.
      - Provides a centralized API for tag-related operations.
]]

-- ==================================================================
-- SERVICES
-- ==================================================================
local DataStoreService: DataStoreService = game:GetService("DataStoreService")
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- PATHS
-- ============================================================================
local servicesFolder = script.Parent
local tbrdsFolder = ReplicatedStorage.TBRDS
local sharedFolder = tbrdsFolder.Shared

-- ============================================================================
-- MODULES
-- ============================================================================
local BillboardService = require(servicesFolder.TBRDS_BillboardService)
local ConfigurationService = require(servicesFolder.TBRDS_ConfigurationService)
local PerformanceMonitor = require(sharedFolder.TBRDS_PerformanceMonitor)
local RateLimiter = require(sharedFolder.TBRDS_RateLimiter)
local RoleService = require(servicesFolder.TBRDS_RoleService)
local TBRDSRemotes = require(tbrdsFolder.TBRDS_Remotes)
local Types = require(tbrdsFolder.TBRDS_Types)
local Utils = require(sharedFolder.TBRDS_Utils)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local TagService = {}

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local TAG: string = "TagService"

-- ==================================================================
-- STATES
-- ==================================================================
local isInitialized: boolean = false
local playerTags: { [Player]: Types.PlayerTagData } = {}
local rateLimiter = nil
local specialTagsStore: GlobalDataStore? = nil

-- ==================================================================
-- REMOTE EVENTS
-- ==================================================================
local tagUpdateRemote = nil
local tagRequestRemote = nil

-- ==================================================================
-- INITIALIZATION
-- ==================================================================

-- Initialize the tag service
function TagService.Initialize(): boolean
  if isInitialized then
    Utils.log(TAG, "Tag service already initialized")
    return true
  end

  Utils.log(TAG, "Initializing tag service...")

  -- Initialize dependencies
  if not ConfigurationService.Initialize() then
    Utils.warn(TAG, "Failed to initialize ConfigurationService")
    return false
  end

  if not RoleService.Initialize() then
    Utils.warn(TAG, "Failed to initialize RoleService")
    return false
  end

  if not BillboardService.Initialize() then
    Utils.warn(TAG, "Failed to initialize BillboardService")
    return false
  end

  -- Initialize rate limiter
  local rateLimitConfig = ConfigurationService.GetRateLimitConfig()
  rateLimiter = RateLimiter.createRateLimiter(rateLimitConfig.Window, rateLimitConfig.MaxRequests)

  -- Initialize DataStore
  specialTagsStore = DataStoreService:GetDataStore("SpecialTags")

  -- Initialize remote events
  tagUpdateRemote = TBRDSRemotes.GetTagUpdateRemote()
  tagRequestRemote = TBRDSRemotes.GetTagRequestRemote()

  if not tagUpdateRemote or not tagRequestRemote then
    Utils.warn(TAG, "Failed to initialize remote events")
    return false
  end

  -- Set up event handlers
  TagService.SetupEventHandlers()

  -- Set up player management
  TagService.SetupPlayerManagement()

  isInitialized = true
  Utils.log(TAG, "Tag service initialized successfully")
  return true
end

-- ==================================================================
-- SETUP EVENT HANDLERS
-- ==================================================================

function TagService.SetupEventHandlers(): ()
  -- Handle tag requests from clients
  if tagRequestRemote then
    tagRequestRemote.OnServerEvent:Connect(function(player: Player)
      TagService.HandleTagRequest(player)
    end)
  else
    Utils.warn(TAG, "tagRequestRemote is nil, cannot set up OnServerEvent handler")
  end

  -- Subscribe to configuration changes
  ConfigurationService.SubscribeToChanges(function(newConfig)
    Utils.log(TAG, "Configuration changed, updating rate limiter")
    rateLimiter = RateLimiter.createRateLimiter(
      newConfig.Settings.RateLimit.Window,
      newConfig.Settings.RateLimit.MaxRequests
    )
  end)
end

-- ==================================================================
-- SETUP PLAYER MANAGEMENT
-- ==================================================================

function TagService.SetupPlayerManagement(): ()
  -- Handle new players
  Players.PlayerAdded:Connect(function(player)
    TagService.HandlePlayerJoined(player)
  end)

  -- Handle players leaving
  Players.PlayerRemoving:Connect(function(player)
    TagService.HandlePlayerLeaving(player)
  end)

  -- Handle existing players
  for _, player in ipairs(Players:GetPlayers()) do
    TagService.HandlePlayerJoined(player)
  end
end

-- ==================================================================
-- PLAYER EVENTS
-- ==================================================================

-- Handle player joining
function TagService.HandlePlayerJoined(player: Player): ()
  Utils.log(TAG, "Player joined: " .. player.Name)

  -- Assign initial tag
  TagService.AssignTag(player)

  -- Set up character spawning handler
  player.CharacterAdded:Connect(function(character)
    TagService.HandleCharacterSpawned(player, character)
  end)

  -- Set up property change handlers
  player:GetPropertyChangedSignal("Team"):Connect(function()
    TagService.RefreshPlayerTag(player)
  end)

  -- Start group rank monitoring
  TagService.StartGroupRankMonitoring(player)
end

-- Handle character spawning
function TagService.HandleCharacterSpawned(player: Player, character: Model): ()
  Utils.log(TAG, "Character spawned for: " .. player.Name)

  -- Wait for head to load
  local head = character:WaitForChild("Head", 5)
  if not head then
    Utils.warn(TAG, "Head not found for " .. player.Name)
    return
  end

  -- Get current tag data
  local tagData = playerTags[player]
  if not tagData then
    TagService.AssignTag(player)
    tagData = playerTags[player]
  end

  if tagData then
    -- Create billboard
    local roleStyle = RoleService.GetRoleStyle(tagData.Role)
    if roleStyle then
      local billboard = BillboardService.CreateBillboard(player, tagData.Role, roleStyle)
      if billboard then
        tagData.BillboardGUI = billboard
      end
    end

    -- Broadcast to clients
    TagService.BroadcastTagUpdate(player, tagData.Role, roleStyle)
  end
end

-- Handle player leaving
function TagService.HandlePlayerLeaving(player: Player): ()
  Utils.log(TAG, "Player left: " .. player.Name)

  -- Clean up services
  RoleService.HandlePlayerLeaving(player)
  BillboardService.RemoveBillboard(player)

  -- Clean up tag data
  playerTags[player] = nil
end

-- ==================================================================
-- TAG ASSIGNMENTS
-- ==================================================================

-- Assign a tag to a player
function TagService.AssignTag(player: Player): Types.ValidationResult
  local result: Types.ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }

  local startTime = tick()
  local playerValidation = Utils.validatePlayer(player)
  if not playerValidation.Success then
    PerformanceMonitor.RecordValidationTime((tick() - startTime) * 1000)
    return playerValidation
  end

  -- Check cache (TTL 60s)
  local currentTagData = playerTags[player]
  if currentTagData and (os.time() - currentTagData.LastUpdated) < 60 then
    PerformanceMonitor.RecordCacheHit()
    PerformanceMonitor.RecordValidationTime((tick() - startTime) * 1000)
    result.Success = true
    result.Role = currentTagData.Role
    return result
  end
  PerformanceMonitor.RecordCacheMiss()

  -- Defer only the expensive operations asynchronously
  task.spawn(function()
    local config = ConfigurationService.GetConfiguration()
    local groupId = config.Groups.Primary.Id
    local groupRank = 0
    local specialTag = nil
    local success, rank = pcall(function()
      return player:GetRankInGroup(groupId)
    end)
    if success then
      groupRank = rank
    end
    -- Add 200ms delay to respect API rate limi
    task.wait(0.2)
    specialTag = TagService.LoadSpecialTag(player.UserId)

    -- Determine role (uses groupRank if needed)
    local role = RoleService.GetPlayerRole(player)
    local roleStyle = RoleService.GetRoleStyle(role)
    if not roleStyle then
      result.ErrorCode = ConfigurationService.GetErrorCodes().INVALID_ROLE
      result.ErrorMessage = "No style found for role: " .. role
      PerformanceMonitor.RecordValidationTime((tick() - startTime) * 1000)
      return
    end

    -- Update tag data
    playerTags[player] = {
      Role = role,
      BillboardGUI = nil,
      LastUpdated = os.time(),
      ValidationCount = (currentTagData and currentTagData.ValidationCount or 0) + 1,
      SecurityFlags = nil,
      GroupRank = groupRank,
      SpecialTag = specialTag,
    } :: Types.PlayerTagData

    -- Billboard and broadcast
    if player.Character and player.Character:FindFirstChild("Head") then
      local billboard = BillboardService.CreateBillboard(player, role, roleStyle)
      if billboard then
        playerTags[player].BillboardGUI = billboard
      end
    end
    TagService.BroadcastTagUpdate(player, role, roleStyle)
    Utils.log(TAG, "Assigned tag '" .. role .. "' to player: " .. player.Name)
    PerformanceMonitor.RecordTagAssignment(role)
    PerformanceMonitor.RecordValidationTime((tick() - startTime) * 1000)
  end)
  -- Return immediately for async operation
  result.Success = true
  result.Role = currentTagData and currentTagData.Role or nil
  return result
end

-- Refresh a player's tag
function TagService.RefreshPlayerTag(player: Player): Types.ValidationResult
  -- Force role re-evaluation
  local _role = RoleService.RefreshPlayerRole(player)

  -- Assign the refreshed role
  return TagService.AssignTag(player)
end

-- Handle tag request from client
function TagService.HandleTagRequest(player: Player): ()
  if not Players:GetPlayerByUserId(player.UserId) then
    return
  end

  -- Check rate limit
  if rateLimiter then
    local allowed, count = rateLimiter:CheckLimit(player.UserId)
    if not allowed then
      Utils.log(TAG, "Rate limit exceeded for " .. player.Name .. " (" .. count .. " requests)")
      PerformanceMonitor.RecordSecurityEvent("RATE_LIMIT_EXCEEDED")
      return
    end
  else
    Utils.warn(TAG, "rateLimiter is nil, cannot check rate limit")
    return
  end

  -- Get current tag data
  local tagData = playerTags[player]
  if not tagData then
    TagService.AssignTag(player)
    tagData = playerTags[player]
  end

  if tagData then
    local roleStyle = RoleService.GetRoleStyle(tagData.Role)
    if roleStyle then
      -- Send tag information to the requesting client
      if tagUpdateRemote then
        tagUpdateRemote:FireClient(player, player, tagData.Role, roleStyle, roleStyle.Image)
      else
        Utils.warn(TAG, "tagUpdateRemote is nil, cannot fire client")
      end
    end
  end
end

-- Broadcast tag update to all clients
function TagService.BroadcastTagUpdate(player: Player, role: string, style: Types.RoleStyle): ()
  if tagUpdateRemote then
    tagUpdateRemote:FireAllClients(player, role, style, style.Image)
    Utils.log(TAG, "Broadcasted tag update for " .. player.Name .. ": " .. role)
  end
end

-- ==================================================================
-- SETUP GROUP RANKING
-- ==================================================================

-- Start monitoring group rank changes for a player
function TagService.StartGroupRankMonitoring(player: Player): ()
  -- Re-capture player in correct type scope
  local monitoredPlayer = player

  task.spawn(function()
    local config = ConfigurationService.GetConfiguration()
    local groupId = config.Groups.Primary.Id
    local checkInterval = config.Settings.GroupRankCheckInterval

    local lastKnownRank = (player :: Player):GetRankInGroup(groupId)

    while monitoredPlayer.Parent == Players do
      task.wait(checkInterval)

      local currentRank = monitoredPlayer:GetRankInGroup(groupId)
      if currentRank ~= lastKnownRank then
        lastKnownRank = currentRank
        Utils.log(TAG, monitoredPlayer.Name .. "'s group rank changed to " .. currentRank)
        TagService.RefreshPlayerTag(monitoredPlayer)
      end
    end
  end)
end

-- Get player tag data
function TagService.GetPlayerTagData(player: Player): Types.PlayerTagData?
  return playerTags[player]
end

-- Get all player tags
function TagService.GetAllPlayerTags(): { [Player]: Types.PlayerTagData }
  return playerTags
end

-- Get tag statistics
function TagService.GetTagStatistics(): Types.TagStatistics
  local stats: Types.TagStatistics = {
    totalPlayers = 0,
    roleDistribution = {},
  }

  for player, tagData in pairs(playerTags) do
    if player.Parent then
      stats.totalPlayers = stats.totalPlayers + 1
      stats.roleDistribution[tagData.Role] = (stats.roleDistribution[tagData.Role] or 0) + 1
    end
  end

  return stats
end

-- Save special tag to DataStore
function TagService.SaveSpecialTag(userId: number, tag: string): boolean
  if not specialTagsStore then
    Utils.warn(TAG, "specialTagsStore is nil, cannot save tag")
    return false
  end

  local success = pcall(function()
    if specialTagsStore then
      specialTagsStore:SetAsync(tostring(userId), tag)
    end
  end)

  if not success then
    Utils.warn(TAG, "Failed to save tag for " .. userId)
    PerformanceMonitor.RecordError("DATASTORE_SAVE_ERROR")
  end

  return success
end

-- Load special tag from DataStore
function TagService.LoadSpecialTag(userId: number): string?
  if not specialTagsStore then
    Utils.warn(TAG, "specialTagsStore is nil, cannot load tag")
    return nil
  end

  local success, result = pcall(function()
    if specialTagsStore then
      return specialTagsStore:GetAsync(tostring(userId)) :: string?
    end
    return nil
  end)

  if success then
    return result
  else
    Utils.warn(TAG, "Failed to load tag for " .. userId .. ": " .. tostring(result))
    PerformanceMonitor.RecordError("DATASTORE_LOAD_ERROR")
    return nil
  end
end

-- Get service status
function TagService.GetServiceStatus(): Types.TagServiceStatus
  local stats = TagService.GetTagStatistics()

  return {
    initialized = isInitialized,
    totalPlayers = stats.totalPlayers,
    roleDistribution = stats.roleDistribution,
    rateLimiterActive = rateLimiter ~= nil,
    dataStoreActive = specialTagsStore ~= nil,
    remoteEventsActive = tagUpdateRemote ~= nil and tagRequestRemote ~= nil,
  } :: Types.TagServiceStatus
end

-- ==================================================================
-- SERVICE CLEANUP
-- ==================================================================

-- Cleanup service
function TagService.Cleanup(): ()
  playerTags = {}
  isInitialized = false
  Utils.log(TAG, "Tag service cleaned up")
end

-- ==================================================================
-- EXPORTS
-- ==================================================================
return TagService
