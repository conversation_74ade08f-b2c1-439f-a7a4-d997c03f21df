--!strict

--[[
	- file: DCPS_Configuration.luau

	- version: 1.0.0
	- author: BleckWolf25
	- contributors:

	- copyright: Dynamic Innovative Studio

	- description:
        - Centralized Configuration for Diurnal Cycle Processor System (DCPS).
        - Defines time segments and their lighting profiles, including:
            - Atmosphere
            - Skybox
            - SunRays
            - Brightness
            - ExposureCompensation
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ServerStorage = game:GetService("ServerStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ServerStorage.DCPS.DCPS_Types)

-- ============================================================================
-- CONFIGURATION
-- ============================================================================
local DCPS_Configuration: Types.DCPSConfiguration = {
  DebugMode = true,
  TransitionTime = 5, -- seconds for smooth transitions
  UpdateInterval = 0.5, -- seconds between TimeOfDay checks

  CycleSegments = {
    Sunrise = {
      Definition = {
        Name = "Sunrise",
        StartTime = 5.5, -- 5:30 AM
        EndTime = 7.0, -- 7:00 AM
      },
      Lighting = {
        Atmosphere = {
          Density = 0.3,
          Offset = 0.25,
          Color = Color3.fromRGB(255, 183, 76), -- Warm sunrise hue
          Decay = Color3.fromRGB(255, 145, 0), -- Orange decay
          Glare = 1.5,
          Haze = 0.5,
        },
        Skybox = {
          SkyboxBk = "rbxassetid://99354603595367",
          SkyboxDn = "rbxassetid://79889705306193",
          SkyboxFt = "rbxassetid://86101517493986",
          SkyboxLf = "rbxassetid://129064288628090",
          SkyboxRt = "rbxassetid://87701210669912",
          SkyboxUp = "rbxassetid://106303787463886",
          StarCount = 10,
          SunAngularSize = 16,
          MoonAngularSize = 2,
        },
        SunRays = {
          Intensity = 0.2,
          Spread = 0.6,
        },
        Brightness = 1.5, -- Moderate brightness for dawn
        ExposureCompensation = 0.3, -- Slight boost for warm lighting
      },
    },

    Morning = {
      Definition = {
        Name = "Morning",
        StartTime = 7.0,
        EndTime = 12.0,
      },
      Lighting = {
        Atmosphere = {
          Density = 0.25,
          Offset = 0,
          Color = Color3.fromRGB(255, 200, 150), -- Soft morning light
          Decay = Color3.fromRGB(255, 180, 120),
          Glare = 2,
          Haze = 0.3,
        },
        Skybox = {
          SkyboxBk = "rbxassetid://135738192422294",
          SkyboxDn = "rbxassetid://95503746009401",
          SkyboxFt = "rbxassetid://109962904696393",
          SkyboxLf = "rbxassetid://133195923243798",
          SkyboxRt = "rbxassetid://85267438194878",
          SkyboxUp = "rbxassetid://115254328481546",
          StarCount = 10,
          SunAngularSize = 18,
          MoonAngularSize = 2,
        },
        SunRays = {
          Intensity = 0.3,
          Spread = 0.5,
        },
        Brightness = 2.0, -- Brighter for clear morning
        ExposureCompensation = 0.5, -- Natural daylight exposure
      },
    },

    Afternoon = {
      Definition = {
        Name = "Afternoon",
        StartTime = 12.0,
        EndTime = 16.0,
      },
      Lighting = {
        Atmosphere = {
          Density = 0.2,
          Offset = 0,
          Color = Color3.fromRGB(255, 255, 255), -- Neutral daylight
          Decay = Color3.fromRGB(255, 255, 200),
          Glare = 2,
          Haze = 0.2,
        },
        Skybox = {
          SkyboxBk = "rbxassetid://98321680616521",
          SkyboxDn = "rbxassetid://78753345085430",
          SkyboxFt = "rbxassetid://116493112077163",
          SkyboxLf = "rbxassetid://86095614301704",
          SkyboxRt = "rbxassetid://101934941409089",
          SkyboxUp = "rbxassetid://74413641574456",
          StarCount = 10,
          SunAngularSize = 22,
          MoonAngularSize = 2,
        },
        SunRays = {
          Intensity = 0.4,
          Spread = 0.3,
        },
        Brightness = 2.5, -- Peak brightness for midday
        ExposureCompensation = 0.7, -- High exposure for vibrant light
      },
    },

    Sunset = {
      Definition = {
        Name = "Sunset",
        StartTime = 16.0,
        EndTime = 19.0,
      },
      Lighting = {
        Atmosphere = {
          Density = 0.35,
          Offset = 0.3,
          Color = Color3.fromRGB(255, 94, 77), -- Warm sunset glow
          Decay = Color3.fromRGB(255, 64, 32),
          Glare = 1.2,
          Haze = 0.4,
        },
        Skybox = {
          SkyboxBk = "rbxassetid://93271955626195",
          SkyboxDn = "rbxassetid://109487577235551",
          SkyboxFt = "rbxassetid://92869309516190",
          SkyboxLf = "rbxassetid://107225228193968",
          SkyboxRt = "rbxassetid://135080015421083",
          SkyboxUp = "rbxassetid://86766722247894",
          StarCount = 10,
          SunAngularSize = 20,
          MoonAngularSize = 2,
        },
        SunRays = {
          Intensity = 0.3,
          Spread = 0.6,
        },
        Brightness = 1.2, -- Dimmer for evening
        ExposureCompensation = 0.2, -- Reduced for softer light
      },
    },

    Night = {
      Definition = {
        Name = "Night",
        StartTime = 19.0,
        EndTime = 0.0, -- wrap around
      },
      Lighting = {
        Atmosphere = {
          Density = 0.4,
          Offset = -0.3,
          Color = Color3.fromRGB(25, 25, 112), -- Deep blue night
          Decay = Color3.fromRGB(0, 0, 64),
          Glare = 0,
          Haze = 0.6,
        },
        Skybox = {
          SkyboxBk = "rbxassetid://121023603935482",
          SkyboxDn = "rbxassetid://105027864716169",
          SkyboxFt = "rbxassetid://116156170786737",
          SkyboxLf = "rbxassetid://76864820168417",
          SkyboxRt = "rbxassetid://98282097619410",
          SkyboxUp = "rbxassetid://74165511082480",
          StarCount = 1000,
          SunAngularSize = 0,
          MoonAngularSize = 10,
        },
        SunRays = {
          Intensity = 0,
          Spread = 0,
        },
        Brightness = 0.5, -- Low brightness for night
        ExposureCompensation = -0.5, -- Darker exposure for nighttime
      },
    },

    Midnight = {
      Definition = {
        Name = "Midnight",
        StartTime = 0.0,
        EndTime = 5.5,
      },
      Lighting = {
        Atmosphere = {
          Density = 0.45,
          Offset = -0.4,
          Color = Color3.fromRGB(10, 10, 30), -- Deep midnight blue
          Decay = Color3.fromRGB(0, 0, 40),
          Glare = 0,
          Haze = 0.7,
        },
        Skybox = {
          SkyboxBk = "rbxassetid://127040436632215",
          SkyboxDn = "rbxassetid://132821929761162",
          SkyboxFt = "rbxassetid://131527499999601",
          SkyboxLf = "rbxassetid://75250602212643",
          SkyboxRt = "rbxassetid://127300742065983",
          SkyboxUp = "rbxassetid://136417059786411",
          StarCount = 1500,
          SunAngularSize = 0,
          MoonAngularSize = 12,
        },
        SunRays = {
          Intensity = 0,
          Spread = 0,
        },
        Brightness = 0.3, -- Minimal brightness for midnight
        ExposureCompensation = -0.7, -- Darkest exposure
      },
    },
  },
}

-- ============================================================================
-- EXPORTS
-- ============================================================================
return DCPS_Configuration
