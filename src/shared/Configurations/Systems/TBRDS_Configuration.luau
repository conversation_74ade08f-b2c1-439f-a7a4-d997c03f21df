--!strict

--[[
    - file: TBRDS_Configuration.luau

    - version: 1.1.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - TBRDS Configuration Module
      - Central configuration for the Tag-Based Role Display System (TBRDS)
      - Manages role priority and validation rules
      - Enables debug mode and performance monitoring
      - Defines security and rate limiting parameters
      - Used by both client and server components
]]

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(game.ReplicatedStorage.TBRDS.TBRDS_Types)

-- ============================================================================
-- TYPES
-- ============================================================================
local TBRDSConstants = Types.TBRDSConstants

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local TBRDS_Config = {} :: Types.TBRDSConfiguration

-- ============================================================================
-- SETTINGS
-- ============================================================================
TBRDS_Config.Settings = {
  DebugMode = false,
  EnablePerformanceMetrics = true,
  EnableEventSystem = true,
  RateLimit = {
    Window = 60,
    MaxRequests = 5,
  },
  MaxTagLength = 50,
  MaxDisplayNameLength = 100,
  TagValidationInterval = 60,
  GroupRankCheckInterval = 30,
  MaxCachedPlayers = 200,
  BillboardSettings = {
    MaxDistance = 15,
    StudsOffset = Vector3.new(0, 3.5, 0),
    Size = UDim2.new(8, 0, 2, 0),
    AlwaysOnTop = true,
    LightInfluence = 1,
  },
  AntiExploit = {
    EnablePositionValidation = true,
    EnableRoleValidation = true,
    EnableDataStoreValidation = true,
    MaxRoleChangesPerMinute = 3,
    SuspiciousActivityThreshold = 10,
  },
} :: Types.TBRDSSettings

-- ============================================================================
-- GROUP CONFIGURATION
-- ============================================================================
TBRDS_Config.Groups = {
  Primary = {
    Id = TBRDSConstants.GROUP_ID,
    Name = TBRDSConstants.GROUP_NAME,
  },
}

-- ============================================================================
-- ROLE PRIORITY CONFIGURATION (highest to lowest)
-- ============================================================================
TBRDS_Config.RolePriority = TBRDSConstants.ROLE_PRIORITY

-- ============================================================================
-- GAMEPASS CONFIGURATION
-- ============================================================================
TBRDS_Config.GamePasses = TBRDSConstants.GAMEPASS_IDS

-- ============================================================================
-- EVENT NAMES
-- ============================================================================
TBRDS_Config.Events = TBRDSConstants.EVENT_NAMES

-- ============================================================================
-- REMOTE EVENT NAMES
-- ============================================================================
TBRDS_Config.Remotes = TBRDSConstants.REMOTE_NAMES

-- ============================================================================
-- ERROR CODES
-- ============================================================================
TBRDS_Config.ErrorCodes = TBRDSConstants.ERROR_CODES

-- ============================================================================
-- PERFORMANCE METRICS CONFIGURATION
-- ============================================================================
TBRDS_Config.Metrics = {
  TrackTagAssignments = true,
  TrackValidationTime = true,
  TrackSecurityEvents = true,
  TrackPerformance = true,
  MetricsRetentionTime = 3600, -- 1 hour in seconds
} :: Types.MetricsConfig

-- ============================================================================
-- DEBUG CONFIGURATION
-- ============================================================================
TBRDS_Config.Debug = {
  LogTagAssignments = true,
  LogValidationEvents = true,
  LogSecurityEvents = true,
  LogPerformanceMetrics = false,
  VerboseLogging = false,
} :: Types.DebugConfig

-- ============================================================================
-- VALIDATION RULES
-- ============================================================================
TBRDS_Config.ValidationRules = {
  RequireCharacter = true,
  RequireValidUserId = true,
  RequireGroupMembership = false, -- For default users
  ValidateRolePermissions = true,
  CheckGamePassOwnership = true,
} :: Types.ValidationRules

-- ============================================================================
-- CACHE CONFIGURATION
-- ============================================================================
TBRDS_Config.Cache = {
  PlayerDataTTL = 300, -- 5 minutes
  RoleDataTTL = 600, -- 10 minutes
  GroupDataTTL = 1800, -- 30 minutes
  MaxCacheSize = 1000,
} :: Types.CacheConfig

-- ============================================================================
-- EXPORTS
-- ============================================================================
return TBRDS_Config
