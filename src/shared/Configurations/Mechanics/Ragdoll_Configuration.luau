--!strict

--[[
	- file: Ragdoll_Configuration.luau

	- version: 1.0.0
	- author: BleckWolf25
	- contributors:

	- copyright: Dynamic Innovative Studio

	- description:
		- Centralized Configuration for Ragdoll Mechanic.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ReplicatedStorage.Mechanics.Ragdoll:WaitForChild("Ragdoll_Types"))

-- ============================================================================
-- CONFIGURATION
-- ============================================================================
local RagdollConfig: Types.RagdollConfig = {

  -- Enable debug logging to output status/warnings
  Debug = true,

  -- Maximum simultaneous ragdolls allowed in the world
  MaxRagdolls = 15,

  -- Time (in seconds) before ragdolls are automatically cleaned up
  MaxRagdollLifetime = 90,

  -- Force applied to limbs on ragdoll start (for realism)
  PhysicsForce = 100,

  -- Should the ragdoll parts collide with the environment?
  CollisionEnabled = true,

  -- Max distance (in studs) from the character to replicate ragdoll event to players
  MaxBroadcastDistance = 100,
}

-- ============================================================================
-- EXPORTS
-- ============================================================================
return RagdollConfig
