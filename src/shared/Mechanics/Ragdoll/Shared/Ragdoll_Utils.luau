--!strict

--[[
  - file: Ragdoll_Utils.luau

  - version: 1.0.0
  - author: BleckWolf25
  - contributors: Silver

  - copyright: Dynamic Innovative Studio

  - description:
    - Utility functions for the Ragdoll mechanic, used by both client and server.
    - Provides logging, rig type checking, and ragdoll-specific operations like enabling/disabling ragdolls.
]]

-- Ragdoll_Utils.luau
-- Utility functions for the Ragdoll mechanic, used by both client and server.
-- Provides logging, rig type checking, and ragdoll-specific operations like enabling/disabling ragdolls.

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration =
  require(ReplicatedStorage.Configurations.Mechanics:WaitForChild("Ragdoll_Configuration"))
local Types = require(script.Parent.Parent:WaitForChild("Ragdoll_Types"))

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Utils = {}

-- ============================================================================
-- LOGGING UTILITIES
-- ============================================================================

--[[
    Formats a log tag consistently

    @param tag string - Module or mechanic name
    @return string - Formatted log prefix
]]
function Utils.formatTag(tag: string): string
  return "[Ragdoll::" .. tag .. "]"
end

--[[
    Logs a message if DebugMode is enabled

    @param tag string - Source of the log
    @param ... any - Values to print
]]
function Utils.log(tag: string, ...): ()
  if Configuration.Debug then
    print(Utils.formatTag(tag), ...)
  end
end

--[[
    Logs a warning message if DebugMode is enabled

    @param tag string - Source of the log
    @param ... any - Values to warn
]]
function Utils.warn(tag: string, ...): ()
  if Configuration.Debug then
    warn(Utils.formatTag(tag), ...)
  end
end

-- ============================================================================
-- TABLE UTILITIES
-- ============================================================================

--[[
    Returns the string keys of a dictionary as a formatted string for logging

    @param tbl table - A dictionary with string keys
    @return string - Formatted string of keys
]]
function Utils.TableKeys<T>(tbl: { [string]: T }): string
  local keys = {}
  for k, _ in pairs(tbl) do
    table.insert(keys, k)
  end
  return table.concat(keys, ", ")
end

-- ============================================================================
-- RIG UTILITIES
-- ============================================================================

--[[
    Gets the rig type of the given character.

    @param char Model - The character to check
    @return "R15" | "R6" - The rig type of the character
]]
function Utils.GetRigType(char: Model): "R15" | "R6"
  local hum = char:FindFirstChild("Humanoid") :: Humanoid?
  if hum and hum.RigType == Enum.HumanoidRigType.R15 then
    return "R15"
  end
  return "R6"
end

-- ============================================================================
-- RAGDOLL UTILITIES
-- ============================================================================

--[[
    Enables ragdoll on an R6 character by replacing Motor6D joints with BallSocketConstraints.

    @param character Model - The character to ragdoll
    @param config Configuration - Configuration for ragdoll settings
    @return RagdollState - The state of the ragdolled character
]]
function Utils.EnableRagdoll(character: Model): Types.RagdollState
  Utils.log("EnableRagdoll", "Starting on", character.Name)

  local humanoid = character:FindFirstChild("Humanoid") :: Humanoid?
  if not humanoid then
    Utils.warn("EnableRagdoll", "No Humanoid found in", character.Name)
    return {
      Character = character,
      IsRagdolled = false,
      RagdollStartTime = 0,
      OriginalJoints = {},
      RagdollJoints = {},
    }
  end

  local originalJoints: { [string]: Motor6D } = {}
  local ragdollJoints: { [string]: BallSocketConstraint } = {}

  for _, descendant in character:GetDescendants() do
    if descendant:IsA("Motor6D") then
      local motor = descendant
      local part0 = motor.Part0
      local part1 = motor.Part1

      if part0 and part1 and part0:IsA("BasePart") and part1:IsA("BasePart") then
        part0.CanCollide = true
        part0.Massless = false
        part0.CustomPhysicalProperties = PhysicalProperties.new(1, 0.3, 0.5, 1, 1)

        part1.CanCollide = true
        part1.Massless = false
        part1.CustomPhysicalProperties = PhysicalProperties.new(1, 0.3, 0.5, 1, 1)

        local parent = motor.Parent
        local name = motor.Name

        originalJoints[name] = motor

        if parent then
          local attachment0 = Instance.new("Attachment")
          attachment0.Name = name .. "_Attachment0"
          attachment0.CFrame = motor.C0
          attachment0.Parent = part0

          local attachment1 = Instance.new("Attachment")
          attachment1.Name = name .. "_Attachment1"
          attachment1.CFrame = motor.C1
          attachment1.Parent = part1

          local socket = Instance.new("BallSocketConstraint")
          socket.Name = name .. "_BallSocket"
          socket.Attachment0 = attachment0
          socket.Attachment1 = attachment1
          socket.Parent = parent

          socket.LimitsEnabled = true
          socket.TwistLimitsEnabled = true
          socket.UpperAngle = 90
          socket.TwistLowerAngle = -45
          socket.TwistUpperAngle = 45
          socket.Restitution = 0

          ragdollJoints[name] = socket
        end

        motor:Destroy()
      end
    elseif descendant:IsA("BasePart") then
      descendant.CanCollide = true
      descendant.Massless = false
      descendant.CustomPhysicalProperties = PhysicalProperties.new(1, 0.3, 0.5, 1, 1)
    end
  end

  return {
    Character = character,
    IsRagdolled = true,
    RagdollStartTime = tick(),
    OriginalJoints = originalJoints,
    RagdollJoints = ragdollJoints,
  }
end

--[[
    Disables ragdoll on a character by restoring Motor6D joints and removing BallSocketConstraints.

    @param ragdollState RagdollState - The current state of the ragdolled character
]]
function Utils.DisableRagdoll(state: Types.RagdollState): ()
  if not state.IsRagdolled then
    Utils.warn("DisableRagdoll", "Not ragdolled:", state.Character.Name)
    return
  end

  Utils.log("DisableRagdoll", "Restoring joints for", state.Character.Name)

  -- Remove all BallSocketConstraints and their attachments
  for _, socket in state.RagdollJoints do
    if socket then
      local att0 = socket.Attachment0
      local att1 = socket.Attachment1
      socket:Destroy()
      if att0 then
        att0:Destroy()
      end
      if att1 then
        att1:Destroy()
      end
    end
  end

  -- Restore all Motor6Ds
  for _, motor in state.OriginalJoints do
    if motor then
      local newMotor = Instance.new("Motor6D")
      newMotor.Name = motor.Name
      newMotor.Part0 = motor.Part0
      newMotor.Part1 = motor.Part1
      newMotor.C0 = motor.C0
      newMotor.C1 = motor.C1
      newMotor.Parent = motor.Parent or motor.Part0 -- fallback
    end
  end

  state.IsRagdolled = false
end

--[[
    Cleans up a ragdoll by removing the character model and associated constraints.

    @param ragdollState RagdollState - The state of the ragdolled character
]]
function Utils.CleanupRagdoll(state: Types.RagdollState): ()
  if not state.IsRagdolled then
    Utils.warn("CleanupRagdoll", "Not ragdolled:", state.Character.Name)
    return
  end

  Utils.log("CleanupRagdoll", "Destroying ragdoll for", state.Character.Name)

  -- Remove BallSocketConstraints and attachments
  for _, socket in state.RagdollJoints do
    if socket then
      local att0 = socket.Attachment0
      local att1 = socket.Attachment1
      socket:Destroy()
      if att0 then
        att0:Destroy()
      end
      if att1 then
        att1:Destroy()
      end
    end
  end

  state.OriginalJoints = {}
  state.RagdollJoints = {}
  state.IsRagdolled = false

  if state.Character and state.Character.Parent then
    state.Character:Destroy()
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Utils
