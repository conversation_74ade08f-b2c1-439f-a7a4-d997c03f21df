--!strict

--[[
  - file: Ragdoll_Types.luau

  - version: 1.0.0
  - author: BleckWolf25
  - contributors: Silver

  - copyright: Dynamic Innovative Studio

  - description:
	- Defines strict types for the Ragdoll mechanic, used by both client and server.
]]

-- Ragdoll_Types.luau
-- Defines strict types for the Ragdoll mechanic, used by both client and server.

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Ragdoll_Types = {}

-- ============================================================================
-- TYPES
-- ============================================================================
export type RagdollConfig = {
  Debug: boolean, -- Whether debug logging is enabled
  MaxRagdolls: number, -- Maximum number of ragdoll bodies allowed in the scene
  MaxRagdollLifetime: number, -- Maximum time (seconds) a ragdoll remains before cleanup
  PhysicsForce: number, -- Force applied to ragdoll parts for realistic falling
  CollisionEnabled: boolean, -- Whether ragdoll parts collide with environment
  MaxBroadcastDistance: number, -- Distance-limited replication
}

export type RagdollRemotes = {
  RagdollEvent: RemoteEvent, -- RemoteEvent for triggering ragdoll state
}

export type RagdollState = {
  Character: Model, -- The character model in ragdoll state
  IsRagdolled: boolean, -- Whether the character is currently in ragdoll mode
  RagdollStartTime: number, -- Timestamp when ragdoll was initiated
  OriginalJoints: { [string]: Motor6D }, -- Original Motor6D joints before ragdolling
  RagdollJoints: { [string]: BallSocketConstraint }, -- BallSocketConstraints replacing Motor6Ds
}

export type RagdollMechanic = {
  EnableRagdoll: (character: Model) -> (), -- Function to enable ragdoll on a character
  DisableRagdoll: (character: Model) -> (), -- Function to disable ragdoll on a character
  CleanupRagdoll: (character: Model) -> (), -- Function to remove ragdoll and clean up
}

export type PerfStats = {
  FrameTime: number, -- Average frame time for ragdoll processing
  ActiveRagdolls: number, -- Number of active ragdolls in the scene
  PhysicsSteps: number, -- Number of physics steps per frame for ragdolls
}

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Ragdoll_Types
