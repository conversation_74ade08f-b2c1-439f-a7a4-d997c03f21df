--!strict

--[[
	- file: Ragdoll_Remotes.luau
	
	- version: 1.0.0
	- author: BleckWolf25
	- contributors: Silver
	
	- copyright: Dynamic Innovative Studio
	
	- description: 
		- Creates and manages RemoteEvents for the Ragdoll mechanic.
		- Initializes RagdollEvent upon module load and exposes it directly.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ReplicatedStorage.Mechanics.Ragdoll:WaitForChild("Ragdoll_Types"))
local Utils = require(ReplicatedStorage.Mechanics.Ragdoll.Shared:WaitForChild("Ragdoll_Utils"))

-- ============================================================================
-- VARIABLES
-- ============================================================================
local TAG = "Remotes" :: string

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

-- Get or create the Remotes folder
local function getRemotesFolder(): Folder
  Utils.log(TAG, "Getting remotes folder")

  local ragdollFolder = ReplicatedStorage.Mechanics:WaitForChild("Ragdoll")
  local folder = ragdollFolder:FindFirstChild("Remotes") :: Folder?

  if folder then
    Utils.log(TAG, "Remotes folder found")
    return folder
  else
    Utils.log(TAG, "Remotes folder not found, creating")
    local newFolder = Instance.new("Folder")
    newFolder.Name = "Remotes"
    newFolder.Parent = ragdollFolder
    Utils.log(TAG, "Remotes folder created")
    return newFolder
  end
end

-- Create or retrieve a RemoteEvent
local function createOrGetRemoteEvent(remotesFolder: Folder, name: string): RemoteEvent
  local remote = remotesFolder:FindFirstChild(name) :: RemoteEvent?

  if remote and remote:IsA("RemoteEvent") then
    Utils.log(TAG, `RemoteEvent {name} already exists`)
    return remote
  else
    Utils.log(TAG, `Creating RemoteEvent: {name}`)
    local newRemote = Instance.new("RemoteEvent")
    newRemote.Name = name
    newRemote.Parent = remotesFolder
    return newRemote
  end
end

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local RagdollRemotes: Types.RagdollRemotes = {
  RagdollEvent = createOrGetRemoteEvent(getRemotesFolder(), "RagdollEvent"),
}

Utils.log(TAG, "Ragdoll remotes initialized")

-- ============================================================================
-- EXPORTS
-- ============================================================================
return RagdollRemotes
