--!strict

--[[
    - file: MAFS_Remotes.luau

    - version: 1.1.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Centralized remote event management for the MAFS System.
      - Provides type-safe remote event creation and access.
      - Handles automatic folder creation and remote event initialization.

    - dependencies:
      - MAFS_Types
      - MAFS_Utils

    - notes:
      - Creates remote events on-demand with proper error handling.
      - Provides centralized access to all MAFS remote events.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService: RunService = game:GetService("RunService")

-- ============================================================================
-- PATHS
-- ============================================================================
local mainPath = ReplicatedStorage:WaitForChild("MAFS")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(mainPath:WaitForChild("MAFS_Types"))
local Utils = require(mainPath:WaitForChild("Shared"):WaitForChild("MAFS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================

-- Name of the Remotes resource folder
local REMOTES_FOLDER_NAME: string = "Remotes"

-- Timeout for creating Remotes
local REMOTE_CREATION_TIMEOUT: number = 10

-- Logging tag for Remotes context
local TAG: string = "Remotes"

-- Define all remote events used by MAFS
local RemoteEventNames = {
  PlayFootstep = "PlayFootstep",
  StartFootstepSound = "StartFootstepSound",
  StopFootstepSound = "StopFootstepSound",
  UpdateFootstepSound = "UpdateFootstepSound",
  ReplicateFootprint = "ReplicateFootprint",
  StartReplicatedFootstepSound = "StartReplicatedFootstepSound",
  StopReplicatedFootstepSound = "StopReplicatedFootstepSound",
}

-- ============================================================================
-- TYPES
-- ============================================================================
-- Configuration type alias for Remotes module.
type FootstepRemotes = Types.FootstepRemotes

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

--[[
  Gets or creates the MAFS folder inside ReplicatedStorage. On the server, it
  will be created if missing; on the client, it waits for creation.

  @returns Folder	-- Reference to the MAFS folder
]]
local function getMAFSFolder(): Folder
  local mafsFolder = ReplicatedStorage:FindFirstChild("MAFS")
  if not mafsFolder then
    if RunService:IsServer() then
      mafsFolder = Instance.new("Folder")
      mafsFolder.Name = "MAFS"
      mafsFolder.Parent = ReplicatedStorage
      Utils.log(TAG, "Created MAFS folder")
    else
      mafsFolder = ReplicatedStorage:WaitForChild("MAFS", REMOTE_CREATION_TIMEOUT)
      if not mafsFolder then
        Utils.log(TAG, "Failed to find MAFS folder after timeout")
      end
    end
  end
  return mafsFolder :: Folder
end

--[[
  Gets or creates the Remotes folder inside the MAFS folder. On the server,
  it will create it if missing; on the client, it waits for creation.

  @returns Folder	-- Reference to the Remotes folder
]]
local function getRemotesFolder(): Folder
  local mafsFolder = getMAFSFolder()
  local remotesFolder = mafsFolder:FindFirstChild(REMOTES_FOLDER_NAME)

  if not remotesFolder then
    if RunService:IsServer() then
      remotesFolder = Instance.new("Folder")
      remotesFolder.Name = REMOTES_FOLDER_NAME
      remotesFolder.Parent = mafsFolder
      Utils.log(TAG, "Created remotes folder")
    else
      remotesFolder = mafsFolder:WaitForChild(REMOTES_FOLDER_NAME, REMOTE_CREATION_TIMEOUT)
      if not remotesFolder then
        Utils.log(TAG, "Failed to find remotes folder after timeout")
      end
    end
  end

  return remotesFolder :: Folder
end

--[[
  Gets or creates a RemoteEvent by name. If one exists but is not a RemoteEvent,
  it will be destroyed and replaced (server-side). Clients wait for server creation.

  @params name			-- The name of the RemoteEvent
  @returns RemoteEvent	-- Valid RemoteEvent reference
]]
local function getOrCreateRemoteEvent(name: string): RemoteEvent
  local remotesFolder = getRemotesFolder()
  local existingRemote = remotesFolder:FindFirstChild(name)

  if existingRemote then
    if existingRemote:IsA("RemoteEvent") then
      return existingRemote :: RemoteEvent
    else
      Utils.warn(TAG, `Found non-RemoteEvent instance named '{name}', removing it`)
      existingRemote:Destroy()
    end
  end

  -- Create new remote event
  if RunService:IsServer() then
    local newRemote = Instance.new("RemoteEvent")
    newRemote.Name = name
    newRemote.Parent = remotesFolder
    Utils.log(TAG, `Created remote event: {name}`)
    return newRemote
  else
    -- Client waits for server to create it
    local remote = remotesFolder:WaitForChild(name, REMOTE_CREATION_TIMEOUT)
    if not remote or not remote:IsA("RemoteEvent") then
      Utils.warn(TAG, `Failed to find or invalid remote event: {name}`)
    end
    return remote :: RemoteEvent
  end
end

-- ============================================================================
-- MAIN IMPLEMENTATION & HELPER METHODS
-- ============================================================================

--[[
  Remote events table created using validated instances.
]]
local MAFSRemotes: FootstepRemotes = {
  PlayFootstep = getOrCreateRemoteEvent(RemoteEventNames.PlayFootstep),
  StartFootstepSound = getOrCreateRemoteEvent(RemoteEventNames.StartFootstepSound),
  StopFootstepSound = getOrCreateRemoteEvent(RemoteEventNames.StopFootstepSound),
  UpdateFootstepSound = getOrCreateRemoteEvent(RemoteEventNames.UpdateFootstepSound),
  ReplicateFootprint = getOrCreateRemoteEvent(RemoteEventNames.ReplicateFootprint),
  StartReplicatedFootstepSound = getOrCreateRemoteEvent(
    RemoteEventNames.StartReplicatedFootstepSound
  ),
  StopReplicatedFootstepSound = getOrCreateRemoteEvent(
    RemoteEventNames.StopReplicatedFootstepSound
  ),
}

--[[
  Returns a shallow clone of all remote events.

  @returns FootstepRemotes	-- Copy of the remotes table
]]
local function getAllRemotes(): FootstepRemotes
  return table.clone(MAFSRemotes)
end

--[[
  Checks if all remote events are valid instances.

  @returns boolean	-- True if all remotes are ready, false otherwise
]]
local function areAllRemotesReady(): boolean
  local remotes = {
    { name = "PlayFootstep", remote = MAFSRemotes.PlayFootstep },
    { name = "StartFootstepSound", remote = MAFSRemotes.StartFootstepSound },
    { name = "StopFootstepSound", remote = MAFSRemotes.StopFootstepSound },
    { name = "UpdateFootstepSound", remote = MAFSRemotes.UpdateFootstepSound },
    { name = "ReplicateFootprint", remote = MAFSRemotes.ReplicateFootprint },
    { name = "StartReplicatedFootstepSound", remote = MAFSRemotes.StartReplicatedFootstepSound },
    { name = "StopReplicatedFootstepSound", remote = MAFSRemotes.StopReplicatedFootstepSound },
  }

  for _, entry in ipairs(remotes) do
    if not Utils.isValidInstance(entry.remote) then
      Utils.warn(TAG, `Remote event '{entry.name}' is not ready`)
      return false
    end
  end
  return true
end

--[[
  Waits for all remote events to be valid for use (client-side only).

  @params timeout	-- Optional timeout override (default = REMOTE_CREATION_TIMEOUT)
  @returns boolean	-- True if all remotes are ready within timeout, false otherwise
]]
local function waitForAllRemotes(timeout: number?): boolean
  local actualTimeout = timeout or REMOTE_CREATION_TIMEOUT
  local startTime = os.clock()

  while not areAllRemotesReady() do
    if os.clock() - startTime > actualTimeout then
      Utils.warn(TAG, "Timeout waiting for remote events to be ready")
      return false
    end
    task.wait(0.1)
  end

  Utils.log(TAG, "All remote events are ready")
  return true
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

--[[
  Client initialization wrapper for remote event setup.
  Logs confirmation or failure messages based on readiness.

  @returns nil
]]
if RunService:IsClient() then
  task.spawn(function()
    if waitForAllRemotes() then
      Utils.log(TAG, "MAFS remotes initialized successfully")
    end
  end)
else
  Utils.log(TAG, "MAFS remotes created on server")
end

-- ============================================================================
-- EXPORTS
-- ============================================================================

-- Export the remote events table with utility functions
return {
  -- Remote events
  PlayFootstep = MAFSRemotes.PlayFootstep,
  StartFootstepSound = MAFSRemotes.StartFootstepSound,
  StopFootstepSound = MAFSRemotes.StopFootstepSound,
  UpdateFootstepSound = MAFSRemotes.UpdateFootstepSound,
  ReplicateFootprint = MAFSRemotes.ReplicateFootprint,
  StartReplicatedFootstepSound = MAFSRemotes.StartReplicatedFootstepSound,
  StopReplicatedFootstepSound = MAFSRemotes.StopReplicatedFootstepSound,

  -- Utility functions
  getAllRemotes = getAllRemotes,
  areAllRemotesReady = areAllRemotesReady,
  waitForAllRemotes = waitForAllRemotes,
}
