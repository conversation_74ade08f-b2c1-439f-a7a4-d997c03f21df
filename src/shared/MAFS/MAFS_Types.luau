--!strict

--[[
    - file: MAFS_Types.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Complete type definitions for the MAFS (Material Audio Footstep System).
      - Includes configuration types, performance monitoring types, and utility types.
      - Used by all MAFS components for strict type safety and better code quality.

    - dependencies:
      - None (base types module)

    - notes:
      - This module provides the foundation for all MAFS type safety.
      - All MAFS modules should import types from this module.
]]

-- ============================================================================
-- CORE CONFIGURATION TYPES
-- ============================================================================

export type FootprintConfigMap = {
  [Enum.Material]: string,
}

export type SoundConfigMap = {
  [Enum.Material]: string,
}

export type MAFSConfiguration = {
  -- Core Settings
  performanceMonitor: boolean,
  debugMode: boolean,
  enableFootsteps: boolean,
  enableFootprints: boolean,

  -- Audio Settings
  soundRadius: number,
  defaultVolume: number,
  volumeRange: { min: number, max: number },

  -- Footprint Settings
  footprintLifetime: number,
  footprintOffset: Vector3,

  -- Performance Settings
  updateInterval: number,
  velocityThreshold: number,
  maxConcurrentSounds: number,

  -- Material Mappings
  footstepSoundMap: SoundConfigMap,
  footprintMaterials: FootprintConfigMap,

  -- Default Values
  defaultMaterial: string,
  fallbackSound: string,
}

-- ============================================================================
-- REMOTE EVENT TYPES
-- ============================================================================

export type FootstepRemotes = {
  StartFootstepSound: RemoteEvent,
  UpdateFootstepSound: RemoteEvent,
  StopFootstepSound: RemoteEvent,
  PlayFootstep: RemoteEvent,
  ReplicateFootprint: RemoteEvent,
  StartReplicatedFootstepSound: RemoteEvent,
  StopReplicatedFootstepSound: RemoteEvent,
}

-- ============================================================================
-- PERFORMANCE MONITORING TYPES
-- ============================================================================

export type PerformanceMetrics = {
  basic: {
    SoundsCreated: number,
    SoundsDestroyed: number,
    FootprintsCreated: number,
    MaterialResolutions: number,
    NetworkRequests: number,
    ErrorCount: number,
    LastReset: number,
  },
  soundPoolEfficiency: number,
  averageResolutionTime: number,
  errorRate: number,
  uptime: number,
  materialUsageByType: { [string]: number },
  errorsByType: { [string]: number },
  recentResolutionTimes: { number },
}

export type PerformanceThresholds = {
  maxSoundsPerSecond: number,
  maxFootprintsPerSecond: number,
  maxResolutionTimeMs: number,
  maxErrorRate: number,
  warningThresholds: {
    soundCreation: number,
    footprintCreation: number,
    resolutionTime: number,
    errorRate: number,
  },
}

export type PerformanceMonitor = {
  -- Instance fields
  _config: MAFSConfiguration,
  _enabled: boolean,
  _startTime: number,
  _metrics: PerformanceMetrics,
  _lastWarningTime: { [string]: number },
  _connections: { RBXScriptConnection },

  -- Private methods
  _initializeMonitoring: (self: PerformanceMonitor) -> (),
  _updateMetrics: (self: PerformanceMonitor) -> (),
  _updateEfficiency: (self: PerformanceMonitor) -> (),
  _updateErrorRate: (self: PerformanceMonitor) -> (),
  _checkWarning: (self: PerformanceMonitor, warningType: string, message: string) -> (),

  -- Metatable and methods
  __index: PerformanceMonitor,
  new: (config: MAFSConfiguration) -> PerformanceMonitor,
  isEnabled: (self: PerformanceMonitor) -> boolean,
  recordSoundCreation: (self: PerformanceMonitor) -> (),
  recordSoundDestruction: (self: PerformanceMonitor) -> (),
  recordFootprintCreation: (self: PerformanceMonitor) -> (),
  recordMaterialResolution: (self: PerformanceMonitor, resolutionTime: number) -> (),
  recordNetworkRequest: (self: PerformanceMonitor) -> (),
  recordError: (self: PerformanceMonitor, errorType: string) -> (),
  getMetrics: (self: PerformanceMonitor) -> PerformanceMetrics,
  generateReport: (self: PerformanceMonitor) -> string,
  reset: (self: PerformanceMonitor) -> (),
  destroy: (self: PerformanceMonitor) -> (),
}

-- ============================================================================
-- MATERIAL RESOLUTION TYPES
-- ============================================================================

export type MaterialResolutionResult = {
  materialName: string,
  soundName: string,
  footprintName: string?,
  resolutionTime: number,
  source: "CustomAttribute" | "ParentAttribute" | "FloorMaterial" | "Default",
}

export type MaterialResolver = {
  __index: MaterialResolver,
  new: (config: MAFSConfiguration) -> MaterialResolver,
  resolveMaterial: (
    self: MaterialResolver,
    position: Vector3,
    humanoid: Humanoid
  ) -> MaterialResolutionResult,
  destroy: (self: MaterialResolver) -> (),
}

-- ============================================================================
-- SOUND MANAGEMENT TYPES
-- ============================================================================

export type SoundInstance = {
  sound: Sound,
  part: Part,
  isLooped: boolean,
  createdAt: number,
  lastUsed: number,
}

export type SoundPool = {
  __index: SoundPool,
  new: (config: MAFSConfiguration) -> SoundPool,
  getSound: (self: SoundPool, soundName: string, isLooped: boolean) -> SoundInstance?,
  returnSound: (self: SoundPool, soundInstance: SoundInstance) -> (),
  cleanup: (self: SoundPool) -> (),
  destroy: (self: SoundPool) -> (),
}

-- ============================================================================
-- PLAYER STATE TYPES
-- ============================================================================

export type PlayerFootstepState = {
  isPlaying: boolean,
  currentMaterial: Enum.Material?,
  lastFootprintTime: number,
  currentSoundInstance: SoundInstance?,
}

export type PlayerStateManager = {
  __index: PlayerStateManager,
  new: (config: MAFSConfiguration) -> PlayerStateManager,
  getPlayerState: (self: PlayerStateManager, player: Player) -> PlayerFootstepState,
  updatePlayerState: (self: PlayerStateManager, player: Player, updates: { [string]: any }) -> (),
  removePlayer: (self: PlayerStateManager, player: Player) -> (),
  destroy: (self: PlayerStateManager) -> (),
}

-- ============================================================================
-- UTILITY TYPES
-- ============================================================================

export type MAFSUtils = {
  formatTime: (timestamp: number) -> string,
  clampNumber: (value: number, min: number, max: number) -> number,
  isValidPosition: (position: Vector3) -> boolean,
  createSoundPart: (position: Vector3, name: string?) -> Part,
}

-- ============================================================================
-- API TYPES
-- ============================================================================

export type MAFSAPI = {
  SetVolume: (volume: number) -> (),
  GetVolume: () -> number,
  SetEnabled: (enabled: boolean) -> (),
  IsEnabled: () -> boolean,
  SetPartMaterial: (part: BasePart, materialName: string) -> boolean,
  SetModelMaterial: (model: Model, materialName: string) -> number,
  SetPartsByPattern: (parent: Instance, pattern: string, materialName: string) -> number,
  SetPartsByMaterial: (parent: Instance, material: Enum.Material, materialName: string) -> number,
  GetPerformanceMetrics: () -> PerformanceMetrics?,
}

-- ============================================================================
-- VALIDATION TYPES
-- ============================================================================

export type ValidationResult<T> = {
  success: true,
  value: T,
} | {
  success: false,
  error: string,
}

-- ============================================================================
-- EXPORTS
-- ============================================================================
return {}
