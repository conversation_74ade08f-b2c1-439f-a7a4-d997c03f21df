--!strict

--[[
    - file: MAFS_Utils.luau

    - version: 1.1.1
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Utility functions for the MAFS (Material Audio Footstep System).
      - Provides logging, validation, and helper functions.
      - Designed to avoid cyclic dependencies with configuration modules.

    - dependencies:
      - MAFS_Types

    - notes:
      - This module must NOT require MAFS_Configuration to avoid cyclic dependencies.
      - All configuration-dependent utilities should be in MAFS_Configuration instead.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- PATHS
-- ============================================================================
local mainPath = ReplicatedStorage:WaitForChild("MAFS")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration =
  require(ReplicatedStorage.Configurations.Systems:WaitForChild("MAFS_Configuration"))
local Types = require(mainPath:WaitForChild("MAFS_Types"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================

-- Tag for logging (this makes the entire system logging consistent & easy to debug)
local LOG_TAG: string = "MAFS"

-- Size of the sound part used for 3D sound emitters
local SOUND_PART_SIZE: Vector3 = Vector3.new(0.1, 0.1, 0.1)

-- ============================================================================
-- TYPES
-- ============================================================================

-- Type the entire MAFS Utils
type MAFSUtils = Types.MAFSUtils

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Utils = {}

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

--[[
  Formats a log tag consistently.
  @params tag string: The log tag.
  @returns string: Formatted log prefix.
]]
local function formatTag(tag: string): string
  return "[" .. LOG_TAG .. "::" .. tag .. "]"
end

-- ============================================================================
-- LOGGING UTILITIES
-- ============================================================================

--[[
  Logs a message with a given tag if debug mode is enabled.
  @params tag string: The log tag.
  @params ... any: Additional arguments to log.
  @returns (): None
]]
function Utils.log(tag: string, ...): ()
  if Configuration.Config.debugMode ~= false then
    print(formatTag(tag), ...)
  end
end

--[[
  Warns with a message and tag if debug mode is enabled.
  @params tag string: The warning tag.
  @params ... any: Additional arguments to warn.
  @returns (): None
]]
function Utils.warn(tag: string, ...): ()
  if Configuration.Config.debugMode ~= false then
    warn(formatTag(tag), ...)
  end
end

--[[
  Formats a timestamp into a time string (HH:MM:SS).
  @params timestamp number: The timestamp to format.
  @returns string: Formatted time string.
]]
function Utils.formatTime(timestamp: number): string
  return tostring(os.date("%H:%M:%S", timestamp))
end

-- ============================================================================
-- MATHEMATICAL UTILITIES
-- ============================================================================

--[[
  Clamps a number between a minimum and maximum value.
  @params value number: The value to clamp.
  @params min number: Minimum allowed value.
  @params max number: Maximum allowed value.
  @returns number: Clamped value.
]]
function Utils.clampNumber(value: number, min: number, max: number): number
  return math.clamp(value, min, max)
end

--[[
  Calculates the distance between two Vector3 positions.
  @params pos1 Vector3: The first position.
  @params pos2 Vector3: The second position.
  @returns number: The distance between the positions.
]]
function Utils.getDistance(pos1: Vector3, pos2: Vector3): number
  return (pos1 - pos2).Magnitude
end

--[[
  Linearly interpolates between two numbers, clamping t between 0 and 1.
  @params a number: Start value.
  @params b number: End value.
  @params t number: Interpolation factor.
  @returns number: Interpolated value.
]]
function Utils.lerp(a: number, b: number, t: number): number
  return a + (b - a) * Utils.clampNumber(t, 0, 1)
end

-- ============================================================================
-- POSITION AND INSTANCE UTILITIES
-- ============================================================================

--[[
  Checks if a Vector3 position is valid (not NaN or infinite).
  @params position Vector3: The position to validate.
  @returns boolean: True if valid, false otherwise.
]]
function Utils.isValidPosition(position: Vector3): boolean
  -- Check for NaN or infinite values
  if position.X ~= position.X or position.Y ~= position.Y or position.Z ~= position.Z then
    return false
  end
  if
    math.abs(position.X) == math.huge
    or math.abs(position.Y) == math.huge
    or math.abs(position.Z) == math.huge
  then
    return false
  end
  return true
end

--[[
  Creates an invisible, anchored sound emitter part at a given position.
  @params position Vector3: The position for the part.
  @params name string? (optional): The name of the part.
  @returns Part: The created sound emitter part.
]]
function Utils.createSoundPart(position: Vector3, name: string?): Part
  if not Utils.isValidPosition(position) then
    Utils.warn(LOG_TAG, "Invalid position provided to createSoundPart, using origin")
    position = Vector3.new(0, 0, 0)
  end
  local part = Instance.new("Part")
  part.Name = name or "MAFSSoundEmitter"
  part.Anchored = true
  part.CanCollide = false
  part.Transparency = 1
  part.Size = SOUND_PART_SIZE
  part.CFrame = CFrame.new(position)
  return part
end

--[[
  Safely waits for a child instance with a timeout, warns if not found.
  @params parent Instance: The parent instance.
  @params childName string: The name of the child to find.
  @params timeout number? (optional): Timeout in seconds.
  @returns Instance?: The found child instance or nil.
]]
function Utils.safeWaitForChild(parent: Instance, childName: string, timeout: number?): Instance?
  local actualTimeout = if timeout ~= nil then timeout else 5
  local child = parent:WaitForChild(childName, actualTimeout)
  if not child then
    Utils.warn(
      LOG_TAG,
      `Failed to find child '{childName}' in {parent:GetFullName()} after {actualTimeout} seconds`
    )
  end
  return child
end

--[[
  Checks if an instance is valid (not nil and has a parent).
  @params instance Instance?: The instance to check.
  @returns boolean: True if valid, false otherwise.
]]
function Utils.isValidInstance(instance: Instance?): boolean
  return instance ~= nil and instance.Parent ~= nil
end

--[[
  Safely destroys an instance if it is valid.
  @params instance Instance?: The instance to destroy.
  @returns (): None
]]
function Utils.safeDestroy(instance: Instance?): ()
  if instance and Utils.isValidInstance(instance) then
    instance:Destroy()
  end
end

--[[
  Checks if two positions are within a specified range.
  @params pos1 Vector3: The first position.
  @params pos2 Vector3: The second position.
  @params range number: The range to check.
  @returns boolean: True if within range, false otherwise.
]]
function Utils.isWithinRange(pos1: Vector3, pos2: Vector3, range: number): boolean
  return Utils.getDistance(pos1, pos2) <= range
end

--[[
  Rounds a number to a specified number of decimal places.
  @params number number: The number to round.
  @params decimals number? (optional): Number of decimal places.
  @returns number: Rounded number.
]]
function Utils.round(number: number, decimals: number?): number
  local actualDecimals = decimals or 0
  local multiplier = 10 ^ actualDecimals
  return math.floor(number * multiplier + 0.5) / multiplier
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Utils
