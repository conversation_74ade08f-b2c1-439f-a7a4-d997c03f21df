--!strict

--[[
    - file: MAFS_PerformanceMonitor.luau

    - version: 1.1.1
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Performance monitoring system for the MAFS (Material Audio Footstep System).
      - Tracks sound usage, efficiency metrics, and system performance.
      - Provides conditional activation based on configuration settings.

    - dependencies:
      - MAFS_Types
      - MAFS_Utils

    - notes:
      - Only activates when performanceMonitor is enabled in configuration.
      - Provides comprehensive metrics collection and reporting capabilities.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService: RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ReplicatedStorage.MAFS.MAFS_Types)
local Utils = require(ReplicatedStorage.MAFS.Shared.MAFS_Utils)

-- ============================================================================
-- CONSTANTS
-- ============================================================================
-- Max resolution times stored for averaging
local MAX_RECENT_TIMES: number = 100

-- Minimum seconds between repeated warnings
local WARNING_COOLDOWN: number = 30 -- seconds

-- Threshold in ms to trigger slow warnings
local MIN_RESOLUTION_TIME_WARNING: number = 5 -- milliseconds

-- Max allowed error rate before warning
local MAX_ERROR_RATE_WARNING: number = 0.05 -- 5%

-- Logging tag used by this module
local TAG: string = "PerformanceMonitor"

-- ============================================================================
-- TYPES
-- ============================================================================
-- Monitor class reference
type PerformanceMonitor = Types.PerformanceMonitor

-- Metrics data structure
type PerformanceMetrics = Types.PerformanceMetrics

-- Module configuration
type MAFSConfiguration = Types.MAFSConfiguration

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

--[[
  Calculates the average of a list of numbers.

  @params numbers	-- Table of numeric values
  @returns number	-- Average value or 0 if empty
]]
local function calculateAverage(numbers: { number }): number
  if #numbers == 0 then
    return 0
  end

  local sum = 0
  for _, num in numbers do
    sum += num
  end

  return sum / #numbers
end

--[[
  Computes sound pool efficiency as a percentage.

  @params created	-- Total sounds created
  @params destroyed	-- Total sounds destroyed
  @returns number	-- Efficiency percentage (max 100)
]]
local function calculateEfficiency(created: number, destroyed: number): number
  if created == 0 then
    return 100
  end
  return math.min(100, (destroyed / created) * 100)
end

--[[
  Formats a number using compact unit suffixes (K, M).

  @params num		-- Raw number to format
  @returns string	-- String representation with unit
]]
local function formatNumber(num: number): string
  if num >= 1000000 then
    return string.format("%.1fM", num / 1000000)
  elseif num >= 1000 then
    return string.format("%.1fK", num / 1000)
  else
    return tostring(math.floor(num))
  end
end

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================

-- Implement the module to then be re-usable by other files on the MAFS
local PerformanceMonitor = {}
PerformanceMonitor.__index = PerformanceMonitor

-- ============================================================================
-- PUBLIC METHODS
-- ============================================================================

--[[
  Initializes a new PerformanceMonitor instance and sets baseline metrics.

  @params config MAFSConfiguration  -- Module configuration settings
  @returns PerformanceMonitor       -- Constructed monitor object
]]
function PerformanceMonitor.new(config: MAFSConfiguration): PerformanceMonitor
  -- Initialize self with all instance fields and the correct type
  local self: PerformanceMonitor = {
    _config = config,
    _enabled = config.performanceMonitor,
    _startTime = os.clock(),
    _metrics = {
      basic = {
        SoundsCreated = 0,
        SoundsDestroyed = 0,
        FootprintsCreated = 0,
        MaterialResolutions = 0,
        NetworkRequests = 0,
        ErrorCount = 0,
        LastReset = os.clock(),
      },
      soundPoolEfficiency = 0,
      averageResolutionTime = 0,
      errorRate = 0,
      uptime = 0,
      materialUsageByType = {} :: { [string]: number },
      errorsByType = {} :: { [string]: number },
      recentResolutionTimes = {} :: { number },
    } :: Types.PerformanceMetrics,
    _lastWarningTime = {} :: { [string]: number },
    _connections = {} :: { RBXScriptConnection },
  } :: PerformanceMonitor

  -- Set the metatable to provide methods
  setmetatable(self, PerformanceMonitor)

  -- Initialize monitoring if enabled
  if self._enabled then
    self:_initializeMonitoring()
    Utils.log(TAG, "Performance Monitor initialized")
  else
    Utils.log(TAG, "Performance Monitor disabled by configuration")
  end

  return self
end

--[[
  Returns whether monitoring is currently enabled.

  @returns boolean -- True if enabled, false otherwise
]]
function PerformanceMonitor:isEnabled(): boolean
  return self._enabled
end

--[[
  Increments sound creation metric and updates efficiency.

  @returns nil
]]
function PerformanceMonitor:recordSoundCreation(): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.SoundsCreated = (self._metrics.basic.SoundsCreated :: number) + 1
  self:_updateEfficiency()
end

--[[
  Increments sound destruction metric and updates efficiency.

  @returns nil
]]
function PerformanceMonitor:recordSoundDestruction(): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.SoundsDestroyed = (self._metrics.basic.SoundsDestroyed :: number) + 1
  self:_updateEfficiency()
end

--[[
  Increments footprint creation metric.

  @returns nil
]]
function PerformanceMonitor:recordFootprintCreation(): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.FootprintsCreated = (self._metrics.basic.FootprintsCreated :: number) + 1
end

--[[
  Records a material resolution time and updates average calculation.
  Also checks for excessive delay warnings if resolution time is high.

  @params resolutionTime	-- Time (ms) it took to resolve material
  @returns nil
]]
function PerformanceMonitor:recordMaterialResolution(resolutionTime: number): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.MaterialResolutions = (self._metrics.basic.MaterialResolutions :: number) + 1

  -- Add to recent times (keep only last MAX_RECENT_TIMES)
  local recentTimes = self._metrics.recentResolutionTimes
  table.insert(recentTimes, resolutionTime)
  if #recentTimes > MAX_RECENT_TIMES then
    table.remove(recentTimes, 1)
  end

  -- Update average
  self._metrics.averageResolutionTime = calculateAverage(recentTimes)

  -- Check for performance warnings
  if resolutionTime > MIN_RESOLUTION_TIME_WARNING then
    self:_checkWarning("resolution_time", "Material resolution took " .. resolutionTime .. "ms")
  end
end

--[[
  Increments the count of network requests made for monitoring purposes.

  @returns nil
]]
function PerformanceMonitor:recordNetworkRequest(): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.NetworkRequests = (self._metrics.basic.NetworkRequests :: number) + 1
end

--[[
  Records an error occurrence by type and updates error statistics.
  Triggers warning if error rate exceeds threshold.

  @params errorType	-- A label describing the type of error
  @returns nil
]]
function PerformanceMonitor:recordError(errorType: string): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.ErrorCount = (self._metrics.basic.ErrorCount :: number) + 1

  -- Track error by type
  if not self._metrics.errorsByType[errorType] then
    self._metrics.errorsByType[errorType] = 0
  end
  self._metrics.errorsByType[errorType] = (self._metrics.errorsByType[errorType] :: number) + 1

  -- Update error rate
  self:_updateErrorRate()

  -- Check for error rate warnings
  if self._metrics.errorRate > MAX_ERROR_RATE_WARNING then
    self:_checkWarning(
      "error_rate",
      "High error rate: " .. string.format("%.2f%%", self._metrics.errorRate * 100)
    )
  end
end

--[[
  Returns the current performance metrics.
  If monitoring is disabled, returns zeroed default metrics.

  @returns PerformanceMetrics -- Table containing all tracked performance data
]]
function PerformanceMonitor:getMetrics(): PerformanceMetrics
  if not self._enabled then
    return {
      basic = {
        SoundsCreated = 0,
        SoundsDestroyed = 0,
        FootprintsCreated = 0,
        MaterialResolutions = 0,
        NetworkRequests = 0,
        ErrorCount = 0,
        LastReset = 0,
      },
      soundPoolEfficiency = 0,
      averageResolutionTime = 0,
      errorRate = 0,
      uptime = 0,
      materialUsageByType = {} :: { [string]: number },
      errorsByType = {} :: { [string]: number },
      recentResolutionTimes = {} :: { number },
    } :: Types.PerformanceMetrics
  end

  -- Update uptime
  self._metrics.uptime = os.clock() - self._startTime

  return self._metrics
end

--[[
  Generates a formatted performance report string summarizing current metrics.

  @returns string -- Multi-line performance report or disabled message
]]
function PerformanceMonitor:generateReport(): string
  if not self._enabled then
    return "Performance monitoring is disabled"
  end

  local metrics = self:getMetrics()
  local report = {}

  table.insert(report, "=== MAFS Performance Report ===")
  table.insert(report, "Uptime: " .. string.format("%.1f", metrics.uptime :: number) .. " seconds")
  table.insert(report, "")

  table.insert(report, "Sound Management:")
  table.insert(report, "  Created: " .. formatNumber(metrics.basic.SoundsCreated :: number))
  table.insert(report, "  Destroyed: " .. formatNumber(metrics.basic.SoundsDestroyed :: number))
  table.insert(
    report,
    "  Pool Efficiency: " .. string.format("%.1f%%", metrics.soundPoolEfficiency :: number)
  )
  table.insert(report, "")

  table.insert(report, "System Performance:")
  table.insert(
    report,
    "  Footprints Created: " .. formatNumber(metrics.basic.FootprintsCreated :: number)
  )
  table.insert(
    report,
    "  Material Resolutions: " .. formatNumber(metrics.basic.MaterialResolutions :: number)
  )
  table.insert(
    report,
    "  Average Resolution Time: "
      .. string.format("%.2f", metrics.averageResolutionTime :: number)
      .. "ms"
  )
  table.insert(
    report,
    "  Network Requests: " .. formatNumber(metrics.basic.NetworkRequests :: number)
  )
  table.insert(report, "")

  table.insert(report, "Error Tracking:")
  table.insert(report, "  Total Errors: " .. formatNumber(metrics.basic.ErrorCount :: number))
  table.insert(
    report,
    "  Error Rate: " .. string.format("%.2f%%", (metrics.errorRate :: number) * 100)
  )

  if next(metrics.errorsByType) then
    table.insert(report, "  Errors by Type:")
    local errorEntries: { { errorType: string, count: number } } = {}
    for errorType, count in pairs(metrics.errorsByType) do
      table.insert(errorEntries, { errorType = errorType, count = count :: number })
    end
    for _, entry in ipairs(errorEntries) do
      table.insert(report, "    " .. entry.errorType .. ": " .. formatNumber(entry.count))
    end
  end

  return table.concat(report, "\n")
end

--[[
  Resets all performance metrics to zero and updates the last reset timestamp.

  @returns nil
]]
function PerformanceMonitor:reset(): ()
  if not self._enabled then
    return
  end

  self._metrics.basic = {
    SoundsCreated = 0,
    SoundsDestroyed = 0,
    FootprintsCreated = 0,
    MaterialResolutions = 0,
    NetworkRequests = 0,
    ErrorCount = 0,
    LastReset = os.clock(),
  }

  self._metrics.soundPoolEfficiency = 0
  self._metrics.averageResolutionTime = 0
  self._metrics.errorRate = 0
  self._metrics.materialUsageByType = {}
  self._metrics.errorsByType = {}
  self._metrics.recentResolutionTimes = {}

  Utils.log(TAG, "Performance metrics reset")
end

--[[
  Destroys the monitor: disconnects connections, clears metrics, disables monitoring.

  @returns nil
]]
function PerformanceMonitor:destroy(): ()
  if self._enabled then
    Utils.log(TAG, "Performance Monitor destroyed")
  end

  for _, connection in self._connections do
    connection:Disconnect()
  end
  table.clear(self._connections)

  self._metrics = {
    basic = {
      SoundsCreated = 0,
      SoundsDestroyed = 0,
      FootprintsCreated = 0,
      MaterialResolutions = 0,
      NetworkRequests = 0,
      ErrorCount = 0,
      LastReset = os.clock(),
    },
    soundPoolEfficiency = 0,
    averageResolutionTime = 0,
    errorRate = 0,
    uptime = 0,
    materialUsageByType = {},
    errorsByType = {},
    recentResolutionTimes = {},
  }

  self._enabled = false
end

--[[
  Initializes monitoring by connecting to RunService.Heartbeat for updates.

  @returns nil
]]
function PerformanceMonitor:_initializeMonitoring(): ()
  local connection = RunService.Heartbeat:Connect(function()
    self:_updateMetrics()
  end)
  table.insert(self._connections, connection)
end

--[[
  Updates calculated metrics: efficiency and error rate.

  @returns nil
]]
function PerformanceMonitor:_updateMetrics(): ()
  self:_updateEfficiency()
  self:_updateErrorRate()
end

--[[
  Recalculates sound pool efficiency from created vs destroyed counts.

  @returns nil
]]
function PerformanceMonitor:_updateEfficiency(): ()
  self._metrics.soundPoolEfficiency =
    calculateEfficiency(self._metrics.basic.SoundsCreated, self._metrics.basic.SoundsDestroyed)
end

--[[
  Recalculates error rate based on total operations and errors.

  @returns nil
]]
function PerformanceMonitor:_updateErrorRate(): ()
  local totalOperations = self._metrics.basic.SoundsCreated :: number
    + (self._metrics.basic.FootprintsCreated :: number)
    + (self._metrics.basic.MaterialResolutions :: number)
    + self._metrics.basic.NetworkRequests :: number

  if totalOperations > 0 then
    self._metrics.errorRate = (self._metrics.basic.ErrorCount :: number) / totalOperations
  else
    self._metrics.errorRate = 0
  end
end

--[[
  Emits a warning if the specified type has not been warned recently.

  @params warningType	-- Unique key for cooldown tracking
  @params message		-- Warning message to log
  @returns nil
]]
function PerformanceMonitor:_checkWarning(warningType: string, message: string): ()
  local now = os.clock()
  local lastWarning = self._lastWarningTime[warningType] or 0

  if now - (lastWarning :: number) >= WARNING_COOLDOWN then
    self._lastWarningTime[warningType] = now
    Utils.warn("Performance Warning (" .. warningType .. "): " .. message)
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return PerformanceMonitor
