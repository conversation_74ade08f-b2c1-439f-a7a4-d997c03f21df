--!strict

--[[
    - file: CGS_API.luau

    - version: 1.0.0
    - author: <PERSON><PERSON><PERSON>Wolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
        Centralized API for the Core Game System (CGS), providing access to various sub-systems.
        Currently supports the Observation Sub-system for cinematic camera effects.

    - dependencies:
        - CGS_CinematicEffects
        - CGS_Types

    - usage:
        CGS_API.Shake("Explosion") -- Triggers the "Explosion" preset shake
        CGS_API.ShakeCustom({duration = 2, intensity = 0.5, frequency = 10}) -- Triggers a custom shake
        CGS_API.StopShake(id) -- Stops a specific shake by ID
        CGS_API.StopAllShakes() -- Stops all active shakes
        CGS_API.ObservationSetEnabled(false) -- Disables the observation system
        CGS_API.GetPresets() -- Returns list of preset names
        CGS_API.GetPreset("Explosion") -- Returns the "Explosion" preset configuration
        CGS_API.AddPreset("CustomShake", {duration = 1, intensity = 0.3, frequency = 5}) -- Adds a new preset
        CGS_API.IsShakeActive(id) -- Checks if a shake is active
        CGS_API.GetActiveShakeCount() -- Returns the number of active shakes
        CGS_API.Explosion(0.7) -- Triggers an explosion effect with custom intensity
        CGS_API.Earthquake(5) -- Triggers an earthquake effect with custom duration
        CGS_API.Impact() -- Triggers a quick impact effect
        CGS_API.Dramatic() -- Triggers a dramatic effect
        CGS_API.Subtle() -- Triggers a subtle effect
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ReplicatedStorage.CGS:WaitForChild("CGS_Types"))

-- ============================================================================
-- OBSERVATION MODULES
-- ============================================================================
local CinematicEffects =
  require(ReplicatedStorage.CGS.Observation:WaitForChild("CGS_CinematicEffects"))

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local CGS_API = {}

-- ============================================================================
-- OBSERVATION SYSTEM API
-- ============================================================================

--[[
    Triggers a camera shake effect using a predefined preset.

    @param presetName string - The name of the preset to use
    @param customSettings Types.PartialShakePreset? - Optional custom settings to override the preset
    @return string - The ID of the started shake, or an empty string if the preset is invalid
]]
function CGS_API.Shake(presetName: string, customSettings: Types.PartialShakePreset?): string
  return CinematicEffects.Shake(presetName, customSettings)
end

--[[
    Triggers a camera shake effect with a fully custom preset.

    @param preset Types.ShakePreset - The custom shake preset to use
    @return string - The ID of the started shake
]]
function CGS_API.ShakeCustom(preset: Types.ShakePreset): string
  return CinematicEffects.ShakeCustom(preset)
end

--[[
    Stops a specific shake effect by its ID.

    @param id string - The ID of the shake to stop
]]
function CGS_API.StopShake(id: string): ()
  CinematicEffects.StopShake(id)
end

--[[
    Stops all active shake effects.
]]
function CGS_API.StopAllShakes(): ()
  CinematicEffects.StopAll()
end

--[[
    Enables or disables the observation system, which controls cinematic effects.

    @param enabled boolean - Whether to enable or disable the system
]]
function CGS_API.ObservationSetEnabled(enabled: boolean): ()
  CinematicEffects.SetEnabled(enabled)
end

-- ============================================================================
-- PRESET MANAGEMENT
-- ============================================================================

--[[
    Returns a list of available preset names.

    @return {string} - List of preset names
]]
function CGS_API.GetPresets(): { string }
  return CinematicEffects.GetPresets()
end

--[[
    Returns the configuration of a specific preset.

    @param name string - The name of the preset
    @return Types.ShakePreset? - The preset configuration, or nil if not found
]]
function CGS_API.GetPreset(name: string): Types.ShakePreset?
  return CinematicEffects.GetPreset(name)
end

--[[
    Adds a new preset to the system.

    @param name string - The name of the new preset
    @param preset Types.ShakePreset - The preset configuration
]]
function CGS_API.AddPreset(name: string, preset: Types.ShakePreset): ()
  CinematicEffects.AddPreset(name, preset)
end

-- ============================================================================
-- STATUS QUERIES
-- ============================================================================

--[[
    Checks if a specific shake is active.

    @param id string - The ID of the shake
    @return boolean - True if the shake is active, false otherwise
]]
function CGS_API.IsShakeActive(id: string): boolean
  return CinematicEffects.IsShakeActive(id)
end

--[[
    Returns the number of active shake effects.

    @return number - The count of active shakes
]]
function CGS_API.GetActiveShakeCount(): number
  return CinematicEffects.GetActiveShakeCount()
end

-- ============================================================================
-- CONVENIENCE METHODS
-- ============================================================================

--[[
    Triggers a quick explosion effect with optional custom intensity.

    @param intensity number? - Optional custom intensity for the explosion
    @return string - The ID of the started shake, or an empty string if the preset is invalid
]]
function CGS_API.Explosion(intensity: number?): string
  local customSettings: Types.PartialShakePreset? = if intensity
    then { intensity = intensity }
    else nil
  return CGS_API.Shake("Explosion", customSettings)
end

--[[
    Triggers a quick earthquake effect with optional custom duration.

    @param duration number? - Optional custom duration for the earthquake
    @return string - The ID of the started shake, or an empty string if the preset is invalid
]]
function CGS_API.Earthquake(duration: number?): string
  local customSettings: Types.PartialShakePreset? = if duration
    then { duration = duration }
    else nil
  return CGS_API.Shake("Earthquake", customSettings)
end

--[[
    Triggers a quick impact effect.

    @return string - The ID of the started shake, or an empty string if the preset is invalid
]]
function CGS_API.Impact(): string
  return CGS_API.Shake("Impact", nil)
end

--[[
    Triggers a dramatic effect.

    @return string - The ID of the started shake, or an empty string if the preset is invalid
]]
function CGS_API.Dramatic(): string
  return CGS_API.Shake("Dramatic", nil)
end

--[[
    Triggers a subtle effect.

    @return string - The ID of the started shake, or an empty string if the preset is invalid
]]
function CGS_API.Subtle(): string
  return CGS_API.Shake("Subtle", nil)
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return CGS_API
