--!strict

--[[
    - file: CGS_Proximity.luau

    - version: 1.0.0
    - author: Silver
    - contributors: BleckWolf25

    - copyright: Dynamic Innovative Studio

    - description:
      - Core Game System (CGS) Interaction Sub-System Proximity Prompt Module
      - Highlight management for better user experience
]]

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local CGS_Proximity = {}

-- ============================================================================
-- INTERNAL UTILS
-- ============================================================================
local function getHighlightTarget(proximity: ProximityPrompt): BasePart?
  local placementObj = proximity:FindFirstChild("HighlightPlacementObj")

  if placementObj and placementObj:IsA("ObjectValue") then
    local value = placementObj.Value
    if value and value:IsA("BasePart") then
      return value
    end
  end

  local parent = proximity.Parent
  if parent and parent:IsA("BasePart") then
    return parent
  end

  return nil
end

-- ============================================================================
-- FUNCTIONS
-- ============================================================================

-- Create Highlight
function CGS_Proximity.HighLightCreate(proximity: ProximityPrompt): ()
  local part = getHighlightTarget(proximity)
  if not part then
    return
  end

  if part:FindFirstChild("ProximityHighlight") then
    return
  end

  local highlight = Instance.new("Highlight") :: Highlight
  highlight.Name = "ProximityHighlight"
  highlight.DepthMode = Enum.HighlightDepthMode.Occluded
  highlight.FillColor = Color3.fromRGB(255, 255, 255)
  highlight.FillTransparency = 0.7
  highlight.Parent = part
end

-- Delete Highlight
function CGS_Proximity.HighLightDelete(proximity: ProximityPrompt): ()
  local part = getHighlightTarget(proximity)
  if not part then
    return
  end

  local highlight = part:FindFirstChild("ProximityHighlight")
  if not highlight or not highlight:IsA("Highlight") then
    warn("[CGS_Proximity] Highlight not found for deletion.")
    return
  end

  highlight:Destroy()
end

-- Change Highlight Color
function CGS_Proximity.HighLightChangeColor(proximity: ProximityPrompt, color: Color3): ()
  local part = getHighlightTarget(proximity)
  if not part then
    return
  end

  local highlight = part:FindFirstChild("ProximityHighlight")
  if not highlight or not highlight:IsA("Highlight") then
    warn("[CGS_Proximity] Highlight not found for color change.")
    return
  end

  highlight.FillColor = color
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return CGS_Proximity
