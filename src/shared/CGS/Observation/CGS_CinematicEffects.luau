--!strict

--[[
    - file: CGS_CinematicEffects.luau
    
    - version: 1.0.0
    - author: <PERSON><PERSON>ckWolf25
    - contributors:
    
    - copyright: Dynamic Innovative Studio
    
    - description:
        Provides preset-based camera shake effects for cinematic purposes, such as cutscenes, explosions, and dramatic moments.
        Uses CameraOffsetManager to apply positional shake effects.
    
    - dependencies:
        - CGS_Types
        - CGS_Configuration
        - CGS_Utils
    
    - usage:
        CinematicEffects.Shake("Explosion") -- Starts the "Explosion" preset shake
        CinematicEffects.ShakeCustom({duration = 2, intensity = 0.5, frequency = 10}) -- Starts a custom shake
        CinematicEffects.StopShake(id) -- Stops a specific shake by ID
        CinematicEffects.StopAll() -- Stops all active shakes
        CinematicEffects.SetEnabled(false) -- Disables the system
        CinematicEffects.GetPresets() -- Returns list of preset names
        CinematicEffects.GetPreset("Explosion") -- Returns the "Explosion" preset configuration
        CinematicEffects.AddPreset("CustomShake", {duration = 1, intensity = 0.3, frequency = 5}) -- Adds a new preset
        CinematicEffects.IsShakeActive(id) -- Checks if a shake is active
        CinematicEffects.GetActiveShakeCount() -- Returns the number of active shakes
        CinematicEffects.Reset() -- Stops all active shakes
    
    - configuration:
        Loaded from CGS_Configuration:
        - ShakePresets: Table of shake presets with names as keys and ShakePreset tables as values
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration =
  require(ReplicatedStorage.Configurations.Systems:WaitForChild("CGS_Configuration"))
local Types = require(ReplicatedStorage.CGS:WaitForChild("CGS_Types"))
local Utils = require(ReplicatedStorage.CGS.Shared:WaitForChild("CGS_Utils"))

-- ============================================================================
-- CONFIGURATION
-- ============================================================================

-- Table of predefined shake presets loaded from configuration
local PRESETS: { [string]: Types.ShakePreset } = Configuration.Config.Observation.ShakePresets or {}

-- Intensify for visibility (global)
local INTENSITY_MULTIPLIER = Configuration.Config.Observation.IntensityMultiplier
local MAGNITUDE_THRESHOLD = 0.0001

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local CinematicEffects = {}

-- ============================================================================
-- STATE
-- ============================================================================
local activeShakes: { [string]: Types.ShakeInstance } = {}
local connection: RBXScriptConnection? = nil
local enabled: boolean = true
local currentShakeOffset: Vector3 = Vector3.zero

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

--[[
	Gets the currect Shake Offset
	
	@return Current shake offset number
]]
function CinematicEffects.GetShakeOffset()
  return currentShakeOffset
end

--[[
    Generates Perlin noise for a given axis using 2D noise.
    
    @param time Current time for noise progression
    @param frequency Noise frequency for oscillation speed
    @param seed Random seed for unique noise pattern
    @param roughness Scales noise amplitude
    
    @return Noise value in [-1, 1]
]]
local function generateNoise(
  time: number,
  frequency: number,
  seed: number,
  roughness: number
): number
  -- Use 2D noise with time and seed-based coordinates
  local x = time * frequency
  local y = seed * 0.01 -- Scale seed to avoid extreme values
  local noiseValue = math.noise(x, y) * 2 -- Scale to [-1, 1]
  return noiseValue * roughness
end

--[[
    Calculates the current intensity of a shake based on elapsed time, with fade-in and fade-out effects.
    
    @param shake Types.ShakeInstance - The shake instance
    @param elapsed number - Time elapsed since the shake started
    @return number - Current intensity of the shake
]]
local function calculateIntensity(shake: Types.ShakeInstance, elapsed: number): number
  local preset = shake.preset
  local totalDuration = preset.duration
  local fadeIn = preset.fadeIn or 0
  local fadeOut = preset.fadeOut or 0
  local intensity = preset.intensity * INTENSITY_MULTIPLIER

  -- Less aggressive fade-in
  if elapsed < fadeIn then
    local fadeProgress = elapsed / fadeIn
    intensity = intensity * fadeProgress -- Linear fade-in
  end

  -- Apply fade-out using an exponential curve for smooth decay
  local fadeOutStart = totalDuration - fadeOut
  if elapsed > fadeOutStart then
    local fadeProgress = (elapsed - fadeOutStart) / fadeOut
    intensity = intensity * (1 - fadeProgress) -- Linear fade-out
  end

  return math.max(intensity, 0)
end

--[[
    Updates all active shakes and applies the combined shake offset via CameraOffsetManager.
    Runs every frame via RunService.Heartbeat when there are active shakes.
    
    @param deltaTime number - Time since the last frame
]]
local function updateShakes(deltaTime: number)
  if not enabled then
    currentShakeOffset = Vector3.zero
    Utils.log("CinematicEffects", "Disabled, clearing shake offset")
    return
  end

  local totalOffset = Vector3.zero
  local currentTime = tick()

  -- Process each active shake
  for id, shake in pairs(activeShakes) do
    -- Decrease remaining time
    shake.timeRemaining = shake.timeRemaining - deltaTime

    if shake.timeRemaining <= 0 then
      activeShakes[id] = nil
      Utils.log("CinematicEffects", "Shake " .. id .. " expired")
      continue
    end

    -- Calculate elapsed time since shake started
    local elapsed = shake.preset.duration - shake.timeRemaining
    -- Get current intensity with fade effects
    local intensity = calculateIntensity(shake, elapsed)

    -- Generate noise for each axis using the shake's seed
    local noiseX =
      generateNoise(currentTime, shake.preset.frequency, shake.seed, shake.preset.roughness or 0.5)
    local noiseY = generateNoise(
      currentTime,
      shake.preset.frequency,
      shake.seed + 100,
      shake.preset.roughness or 0.5
    )
    local noiseZ = generateNoise(
      currentTime,
      shake.preset.frequency,
      shake.seed + 200,
      shake.preset.roughness or 0.5
    )

    -- Get direction and damping from preset, with defaults
    local direction = shake.preset.direction or Vector3.new(1, 1, 1)
    local damping = shake.preset.damping or 0.95

    -- Compute shake offset with direction and intensity
    local shakeOffset = Vector3.new(
      noiseX * direction.X * intensity,
      noiseY * direction.Y * intensity,
      noiseZ * direction.Z * intensity
    )

    -- Apply damping over time to smooth the shake
    shakeOffset = shakeOffset * (damping ^ deltaTime)

    -- Accumulate total offset from all shakes
    totalOffset = totalOffset + shakeOffset
  end

  -- Apply combined shake offset via CameraOffsetManager if significant
  if totalOffset.Magnitude > MAGNITUDE_THRESHOLD then
    currentShakeOffset = Vector3.zero
    Utils.log(
      "CinematicEffects",
      "Applying shake offset: "
        .. tostring(totalOffset)
        .. " (Magnitude: "
        .. string.format("%.6f", totalOffset.Magnitude)
        .. ")"
    )
  else
    currentShakeOffset = Vector3.zero
    Utils.log(
      "CinematicEffects",
      "Clearing shake offset (Magnitude: " .. string.format("%.6f", totalOffset.Magnitude) .. ")"
    )
  end

  -- Stop the update loop if no active shakes remain
  if next(activeShakes) == nil and connection then
    connection:Disconnect()
    connection = nil
    currentShakeOffset = Vector3.zero
    Utils.log("CinematicEffects", "No active shakes, stopping update loop")
  end
end

-- ============================================================================
-- PUBLIC METHODS
-- ============================================================================

--[[
    Starts a camera shake effect using a predefined preset.
    
    @param presetName string - The name of the preset to use
    @param customSettings Types.PartialShakePreset? - Optional custom settings to override the preset
    @return string - The ID of the started shake, or an empty string if the preset is invalid
]]
function CinematicEffects.Shake(
  presetName: string,
  customSettings: Types.PartialShakePreset?
): string
  local preset = PRESETS[presetName]
  if not preset then
    Utils.warn("CinematicEffects", "Unknown preset '" .. presetName .. "'")
    return ""
  end

  -- Merge custom settings with the preset
  if customSettings then
    local cloned = table.clone(preset :: any) :: Types.ShakePreset
    for key, value in pairs(customSettings :: { [string]: any }) do
      (cloned :: any)[key] = value
    end
    preset = cloned
  end

  -- Generate a unique ID for the shake
  local id = presetName .. "_" .. tick()
  activeShakes[id] = {
    preset = preset,
    timeRemaining = preset.duration,
    currentIntensity = 0,
    seed = math.random(1000, 9999),
    id = id,
  } :: Types.ShakeInstance

  -- Start the update loop if not already running
  if not connection then
    connection = RunService.Heartbeat:Connect(updateShakes)
    Utils.log("CinematicEffects", "Started shake update loop")
  end

  Utils.log("CinematicEffects", "Started shake: " .. id .. " with preset: " .. presetName)
  return id
end

--[[
    Starts a camera shake effect with a fully custom preset.
    
    @param preset Types.ShakePreset - The custom shake preset to use
    @return string - The ID of the started shake
]]
function CinematicEffects.ShakeCustom(preset: Types.ShakePreset): string
  local id = "custom_" .. tick()
  activeShakes[id] = {
    preset = preset,
    timeRemaining = preset.duration,
    currentIntensity = 0,
    seed = math.random(1000, 9999),
    id = id,
  }

  if not connection then
    connection = RunService.Heartbeat:Connect(updateShakes)
    Utils.log("CinematicEffects", "Started shake update loop for custom shake")
  end

  Utils.log("CinematicEffects", "Started custom shake: " .. id)
  return id
end

--[[
    Stops a specific shake effect by its ID.
    
    @param id string - The ID of the shake to stop
]]
function CinematicEffects.StopShake(id: string)
  if activeShakes[id] then
    activeShakes[id] = nil
    Utils.log("CinematicEffects", "Stopped shake: " .. id)
  end
end

--[[
    Stops all active shake effects.
]]
function CinematicEffects.StopAll()
  activeShakes = {}
  if connection then
    connection:Disconnect()
    connection = nil
  end
  currentShakeOffset = Vector3.zero
  Utils.log("CinematicEffects", "Stopped all shakes and cleared offset")
end

--[[
    Enables or disables the cinematic effects system.
    When disabled, all active shakes are stopped.
    
    @param state boolean - Whether to enable or disable the system
]]
function CinematicEffects.SetEnabled(state: boolean)
  enabled = state
  if not enabled then
    CinematicEffects.StopAll()
    Utils.log("CinematicEffects", "System disabled")
  else
    Utils.log("CinematicEffects", "System enabled")
  end
end

--[[
    Returns a list of available preset names.
    
    @return {string} - List of preset names
]]
function CinematicEffects.GetPresets(): { string }
  local presetNames = {}
  for name in pairs(PRESETS) do
    table.insert(presetNames, name)
  end
  return presetNames
end

--[[
    Returns the configuration of a specific preset.
    
    @param name string - The name of the preset
    @return Types.ShakePreset? - The preset configuration, or nil if not found
]]
function CinematicEffects.GetPreset(name: string): Types.ShakePreset?
  return PRESETS[name]
end

--[[
    Adds a new preset to the system.
    
    @param name string - The name of the new preset
    @param preset Types.ShakePreset - The preset configuration
]]
function CinematicEffects.AddPreset(name: string, preset: Types.ShakePreset)
  PRESETS[name] = preset
  Utils.log("CinematicEffects", "Added preset: " .. name)
end

--[[
    Checks if a specific shake is active.
    
    @param id string - The ID of the shake
    @return boolean - True if the shake is active, false otherwise
]]
function CinematicEffects.IsShakeActive(id: string): boolean
  return activeShakes[id] ~= nil
end

--[[
    Returns the number of active shake effects.
    
    @return number - The count of active shakes
]]
function CinematicEffects.GetActiveShakeCount(): number
  local count = 0
  for _ in pairs(activeShakes) do
    count = count + 1
  end
  return count
end

--[[
    Resets the system by stopping all active shakes.
    Useful when resetting for a new character or scene.
]]
function CinematicEffects.Reset(): ()
  if CinematicEffects.GetActiveShakeCount() > 0 then
    CinematicEffects.StopAll()
    Utils.log("CinematicEffects", "Reset all active shakes")
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return CinematicEffects
