--!strict

--[[
    - file: CGS_Signal.luau

    - version: 1.0.1
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Simple Event system for connecting, firing and disconnecting listeners of Movement Sub-System.

    - dependencies:
      - CGS_Types
      - CGS_Configuration

    - usage:

      -- Call once on client startup
      InputHandler.Initialize()

      -- Each frame or input cycle, call:
      local inputData = InputHandler.GetInputData()
      -- inputData.MoveVector : Vector3 (X left/right, Z forward/back normalized)
      -- inputData.SprintHeld : boolean
      -- inputData.CrouchHeld : boolean
      -- inputData.ProneHeld : boolean
      -- inputData.JumpPressed : boolean (true only on the frame jump was requested)
]]

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(script.Parent.Parent.CGS_Types)
local Utils = require(script.Parent.Parent.Shared.CGS_Utils)

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local TAG: string = "Signal"

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Signal = {}
Signal.__index = Signal

-- ============================================================================
-- PUBLIC METHODS
-- ============================================================================

-- Creates a new Signal instance
function Signal.new(): Types.Signal
  local self = setmetatable({ _listeners = {} :: { Types.Listener } }, Signal) :: Types.Signal
  return self
end

--[[
	Connects a callback to the signal

	@param callback (...any) -> (): function to call when signal fires

	@return Connection object with Disconnect method
]]
function Signal:Connect(callback: (...any) -> ()): Types.Connection
  assert(type(callback) == "function", "Callback must be a function")

  local listener: Types.Listener = {
    Connected = true,
    Callback = callback,
  }

  table.insert(self._listeners, listener)

  local function disconnect()
    listener.Connected = false
    for i, v in ipairs(self._listeners :: { any }) do
      if v == listener then
        table.remove(self._listeners, i)
        break
      end
    end
  end

  return {
    Disconnect = disconnect,
  }
end

--[[
	Fires the signal, calling all connected callbacks

	@param ...any: variable arguments to pass to callbacks
]]
function Signal:Fire(...: any)
  for _, listenerRaw in ipairs(self._listeners) do
    local listener = listenerRaw :: Types.Listener
    if listener.Connected then
      local success = pcall(listener.Callback, ...)
      if not success then
        Utils.warn(TAG, "Signal listener error")
      end
    end
  end
end

-- Destroys all listeners and cleans up the signal
function Signal:Destroy()
  self._listeners = {} :: { Types.Listener }
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Signal
