--!strict

--[[
    - file: CGS_Types.luau

    - version: 1.1.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Centralized Types for the Core Game System (CGS)
      - Makes the system more consistent and prevents runtime errors
      - Additionally, makes maintainability & scalability easy
]]

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local CGS_Types = {}

-- ============================================================================
-- REMOTES
-- ============================================================================

--[[
  CGS_Remotes:
  Table of remote function/event definitions for CGS networking.
]]
export type CGS_Remotes = {
	init: () -> (),
}

-- ============================================================================
-- MOVEMENT TYPES
-- ============================================================================

--[[
  MovementState:
  Enum-like type for player movement states.
]]
export type MovementState = "Idle" | "Walk" | "Sprint" | "Crouch" | "Prone" | "Jump"

--[[
  JointName:
  Enum-like type for humanoid joint names used in animation.
]]
export type JointName =
	"RootJoint"
| "Neck"
| "RightShoulder"
| "LeftShoulder"
| "RightHip"
| "LeftHip"

--[[
  KeyName:
  Enum-like type for movement-related key names.
]]
export type KeyName = "W" | "A" | "S" | "D" | "LeftShift" | "LeftControl" | "C"

--[[
  MovementDirection:
  Enum-like type for movement directions.
]]
export type MovementDirection =
	"None"
| "Forward"
| "Backward"
| "Left"
| "Right"
| "ForwardLeft"
| "ForwardRight"
| "BackwardLeft"
| "BackwardRight"

--[[
  JointData:
  Stores data for a single humanoid joint for animation.
]]
export type JointData = {
	Joint: Motor6D,
	OriginalC0: CFrame,
	CurrentTilt: CFrame,
}

--[[
  BodyAnimator:
  Contains all joints and tweening info for body animation.
]]
export type BodyAnimator = {
	Joints: { [JointName]: JointData },
	TweenInfo: TweenInfo,
	ActiveTweens: { [JointName]: RBXScriptConnection? },
}

--[[
  LegJointName:
  Enum-like type for R6 leg joint names used in procedural animation.
]]
export type LegJointName = "RightHip" | "LeftHip"

--[[
  ProceduralLegData:
  Stores data for a single leg's procedural animation.
]]
export type ProceduralLegData = {
	Hip: Motor6D,
	OriginalHipC0: CFrame,
	StepPhase: number, -- Current phase in the step cycle (0-1)
	StepHeight: number, -- Maximum height of the step
	StepLength: number, -- Length of each step
	LastPosition: Vector3, -- Last known position for step detection
}

--[[
  LegAnimator:
  Contains all leg joints and animation data for procedural leg movement.
]]
export type LegAnimator = {
	RightLeg: ProceduralLegData?,
	LeftLeg: ProceduralLegData?,
	StepFrequency: number, -- Steps per second based on velocity
	IsEnabled: boolean,
	LastUpdateTime: number,
}

--[[
  AnimationReplicationData:
  Data structure for replicating animation states to other clients.
]]
export type AnimationReplicationData = {
	Player: Player,
	MovementState: MovementState,
	InputDirection: Vector3,
	Velocity: Vector3,
	LegPhases: { RightLeg: number, LeftLeg: number }?, -- Optional procedural leg phases
	Timestamp: number,
}

--[[
  ReplicationConfig:
  Configuration for animation replication system.
]]
export type ReplicationConfig = {
	ReplicationDistance: number, -- Maximum distance for replication (studs)
	ReplicationInterval: number, -- Time between replication updates (seconds)
	EnableProceduralLegs: boolean,
	EnableDirectionalMovement: boolean,
}

--[[
  MovementData:
  Stores current movement state and related data for a player.
]]
export type MovementData = {
	State: MovementState,
	Velocity: Vector3,
	Stamina: number,
	LastJumpTime: number,
	InputDirection: Vector3,
	IsGrounded: boolean,
	IsExhausted: boolean,
}

--[[
  InputData:
  Stores current input state for movement controls.
]]
export type InputData = {
	MoveVector: Vector3,
	Direction: MovementDirection,
	SprintHeld: boolean,
	CrouchHeld: boolean,
	ProneHeld: boolean,
	JumpPressed: boolean,
}

--[[
  KeyPressedMap:
  Map of key names to their pressed state.
]]
export type KeyPressedMap = {
	[KeyName]: boolean,
}

--[[
  Connection:
  Represents a connection to a signal/event.
]]
export type Connection = {
	Disconnect: () -> (),
}

--[[
  Signal:
  Custom signal/event type for callback connections.
]]
export type Signal = {
	Connect: (self: Signal, callback: (...any) -> ()) -> Connection,
	Fire: (self: Signal, ...any) -> (),
	Destroy: (self: Signal) -> (),
}

--[[
  Listener:
  Represents a callback listener for a signal.
]]
export type Listener = {
	Connected: boolean,
	Callback: (...any) -> (),
}

--[[
  MovementConfig:
  Configuration for all movement-related parameters.
]]
export type MovementConfig = {
	-- Speed multipliers (studs/second)
	IdleSpeed: number,
	WalkSpeed: number,
	SprintSpeed: number,
	CrouchSpeed: number,
	ProneSpeed: number,

	-- Directional modifiers
	StrafeSpeedMultiplier: number, -- A/D movement cap (15%)
	BackwardSpeedMultiplier: number, -- S movement cap (50%)

	-- Physics
	Acceleration: number,
	Deceleration: number,

	-- Stamina system
	MaxStamina: number,
	StaminaRegenRate: number,
	SprintStaminaDrain: number,
	JumpStaminaCost: number,

	-- Jump mechanics
	JumpPower: number,
	JumpCooldown: number,

	-- State transition speeds
	TransitionSpeed: number,

	-- Gamepad
	Gamepad_Deadzone: number,

	-- Exhaustion
	DepletionWindow: number,
	MaxDepletions: number,
	ExhaustDuration: number,
	RegenMultiplierExhausted: number,
}

-- ============================================================================
-- OBSERVATION TYPES
-- ============================================================================

--[[
  CameraEffect:
  Interface for camera effect modules.
]]
export type CameraEffect = {
	Enabled: boolean,
	Priority: number,
	Init: ((self: CameraEffect) -> ())?,
	Reset: ((self: CameraEffect) -> ())?,
	Update: (self: CameraEffect, deltaTime: number, camera: Camera) -> (),
	OnEnable: ((self: CameraEffect) -> ())?,
	OnDisable: ((self: CameraEffect) -> ())?,
	[string]: any, -- Allow additional fields like Turn, Character, etc.
}

--[[
  BodyView:
  Configuration for body view rendering.
]]
export type BodyView = {
	Enabled: boolean,
	Priority: number,
}

--[[
  BobConfig:
  Configuration for camera bobbing effect.
]]
export type BobConfig = {
	Enabled: boolean,
	Priority: number,
	MovementThreshold: number,
}

--[[
  SwayConfig:
  Configuration for camera sway effect.
]]
export type SwayConfig = {
	Enabled: boolean,
	Priority: number,
	HeadTurnAmount: number,
	TurnSpeed: number,
	TurnClamp: number,
	IdleAmount: Vector3,
	IdleSpeed: number,
	IdleThreshold: number,
}

--[[
  FOVConfig:
  Configuration for dynamic field of view changes.
]]
export type FOVConfig = {
	Enabled: boolean,
	Priority: number,
	DefaultFOV: number,
	SprintFOV: number,
	JumpFOV: number,
	FreeFallFOV: number,
	SwimFOV: number,
	CrouchFOV: number,
	ProneFOV: number,
	TransitionSpeed: number,
}

--[[
  DirectionalHeadConfig:
  Configuration for directional head movement.
]]
export type DirectionalHeadConfig = {
	Enabled: boolean,
	Priority: number,
	ActivationDistance: number,
}

--[[
  MotionBlurConfig:
  Configuration for motion blur effect.
]]
export type MotionBlurConfig = {
	Enabled: boolean,
	Priority: number,
	maxBlur: number,
	rotationSensitivity: number,
	speedThreshold: number,
	FadeSpeed: number,
}

--[[
  UnderwaterEffectConfig:
  Configuration for underwater visual effects.
]]
export type UnderwaterEffectConfig = {
	Enabled: boolean,
	Priority: number,
}

--[[
  ShakePreset:
  Preset configuration for camera shake effects.
]]
export type ShakePreset = {
	intensity: number,
	frequency: number,
	duration: number,
	fadeIn: number,
	fadeOut: number,
	direction: Vector3,
	damping: number,
	roughness: number,
}

--[[
  ShakeInstance:
  Runtime instance of a camera shake effect.
]]
export type ShakeInstance = {
	preset: ShakePreset,
	timeRemaining: number,
	currentIntensity: number,
	seed: number,
	id: string,
}

--[[
  ShakePresets:
  Map of shake preset names to ShakePreset configs.
]]
export type ShakePresets = { [string]: ShakePreset }

--[[
  PartialShakePreset:
  Partial configuration for shake presets (for overrides).
]]
export type PartialShakePreset = {
	intensity: number?,
	frequency: number?,
	duration: number?,
	fadeIn: number?,
	fadeOut: number?,
	direction: Vector3?,
	damping: number?,
	roughness: number?,
}

--[[
  IgnoredAccessories:
  Map of accessory names to ignore for rendering/effects.
]]
export type IgnoredAccessories = { [string]: boolean }

--[[
  ObservationConfig:
  Configuration for all observation/camera effects.
]]
export type ObservationConfig = {
	BodyView: BodyView,
	Bobbing: BobConfig,
	Sway: SwayConfig,
	DynamicFOV: FOVConfig,
	MotionBlur: MotionBlurConfig,
	DirectionalHead: DirectionalHeadConfig,
	UnderwaterEffect: UnderwaterEffectConfig,
	ShakePresets: ShakePresets,
	IgnoredAccessories: IgnoredAccessories,
	IntensityMultiplier: number,
}

-- ============================================================================
-- ANTI-EXPLOIT TYPES
-- ============================================================================
--[[
  ExploitFlags:
  Flags indicating detected exploit types for a player.
]]
export type ExploitFlags = {
	CanSpeed: boolean,
	CanSuperJump: boolean,
}

--[[
  NoExploitsMovementConfig:
  Movement thresholds for anti-exploit checks.
]]
export type NoExploitsMovementConfig = {
	SpeedThreshold: number,
	JumpHeightMax: number,
}

--[[
  PlayerFlagsMap:
  Map of Player objects to their exploit flags.
]]
export type PlayerFlagsMap = {
	[Player]: ExploitFlags,
}

--[[
  ExploitFlagKey:
  Enum-like type for exploit flag keys.
]]
export type ExploitFlagKey = "CanSpeed" | "CanSuperJump"

--[[
  SpeedDetectionConfig:
  Configuration for speed exploit detection.
]]
export type SpeedDetectionConfig = {
	CheckInterval: number,
	SampleSize: number,
	ToleranceMultiplier: number,
}

--[[
  JumpDetectionConfig:
  Configuration for jump exploit detection.
]]
export type JumpDetectionConfig = {
	CheckInterval: number,
	MaxConsecutiveJumps: number,
	Cooldown: number,
}

--[[
  ViolationConfig:
  Configuration for violation tracking and thresholds.
]]
export type ViolationConfig = {
	MaxViolations: number,
	Decay: number,
	Thresholds: {
		Warning: number,
		Kick: number,
		Ban: number,
	},
}

--[[
  PerformanceConfig:
  Configuration for anti-exploit system performance.
]]
export type PerformanceConfig = {
	UpdateRate: number,
	MaxPlayersPerFrame: number,
}

--[[
  DetectionConfig:
  Aggregates all detection-related configs.
]]
export type DetectionConfig = {
	Speed: SpeedDetectionConfig,
	Jump: JumpDetectionConfig,
	Violation: ViolationConfig,
	Performance: PerformanceConfig,
	KillYThreshold: number,
	playerCheckIndex: number,
	maxChecksPerFrame: number,
}

--[[
  SystemToggles:
  Toggles for enabling/disabling anti-exploit subsystems.
]]
export type SystemToggles = {
	EnableSpeedCheck: boolean,
	EnableJumpCheck: boolean,
	EnableViolationSystem: boolean,
	EnableLogging: boolean,
	EnableYKillCheck: boolean,
}

--[[
  AntiExploitConfig:
  Main configuration for anti-exploit systems.
]]
export type AntiExploitConfig = {
	SpecialUsers: { number },
	Movement: NoExploitsMovementConfig,
	Detection: DetectionConfig,
	Toggles: SystemToggles,
	IgnorePrivateServers: boolean,
}

-- ============================================================================
-- PLAYER TRACKING TYPES
-- ============================================================================
--[[
  PlayerTrackingData:
  Tracks player movement and violation history for anti-exploit.
]]
export type PlayerTrackingData = {
	positionHistory: { Vector3 },
	timeHistory: { number },
	historyIndex: number,
	lastGrounded: number,
	airTime: number,
	jumpCount: number,
	lastJumpTime: number,
	violations: number,
	lastViolationTime: number,
	noClipSamples: number,
	lastValidPosition: Vector3,
}

-- ============================================================================
-- MAIN CONFIGURATION TYPE
-- ============================================================================
--[[
  Configuration:
  Main configuration type for the CGS system.
]]
export type Configuration = {
	General: {
		DebugMode: boolean,
		PerformanceMonitor: boolean,
	},
	Movement: MovementConfig,
	Observation: ObservationConfig,
	AntiExploit: AntiExploitConfig,
}

-- ============================================================================
-- EXPORTS
-- ============================================================================
return CGS_Types
