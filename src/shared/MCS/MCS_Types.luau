--!strict

--[[
    - file: MCS_Types.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Modular Command System (MCS) Centralized Types
      - Makes the system more consistent and prevents runtime errors
      - Additionally, makes maintainability & scalability easy
]]

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local MCS_Types = {}

-- ============================================================================
-- CORE TYPES
-- ============================================================================
export type SettingsType = {
  DebugMode: boolean,
  EnablePerformanceMetrics: boolean,
  AutocompleteCacheDuration: number,
}

export type ConfigType = {
  GROUP_ID: number,
  RANK_TO_LEVEL: { [number]: number },
  SPECIAL_USERS: { [number]: any },
  CACHE_DURATION: number,
}

-- ============================================================================
-- CONSTANT TYPES
-- ============================================================================
export type CommandsType = {
  PREFIX: string,
  MAX_COMMAND_LENGTH: number,
  MAX_ARGS: number,
  MAX_SUGGESTIONS: number,
  PLAYER_ARGUMENT_COMMANDS: { string },
}

export type UI_ConstantsType = {
  BACKGROUND: Color3,
  BORDER: Color3,
  SUCCESS_TEXT: Color3,
  ERROR_TEXT: Color3,
  TEXT: Color3,
  CORNER_RADIUS: UDim,
  MAX_SUGGESTION_HEIGHT: number,
  SUGGESTION_BUTTON_HEIGHT: number,
  SUGGESTION_PADDING: number,
  ANIMATION_TIME: number,
  FADE_IN_TIME: number,
  FADE_OUT_TIME: number,
  SCALE_TIME: number,
  DISPLAY_TIME: number,
  MIN_WIDTH: number,
  MAX_WIDTH: number,
  HEIGHT: number,
  PADDING_X: number,
  PADDING_Y: number,
}

export type BanData = {
  endTime: number,
  reason: string,
}

export type AnalyticsConfig = {
  ANALYTICS_DATASTORE_NAME: string,
  PLAYER_ANALYTICS_DATASTORE_NAME: string,
  SAVE_INTERVAL: number,
  MAX_RETRIES: number,
  RETRY_DELAY: number,
}

export type Middleware = {
  Initialization: {
    MAX_MIDDLEWARE_EXECUTION_TIME: number,
    MAX_RETRIES: number,
    MIDDLEWARE_TIMEOUT: number,
  },
  RateLimiter: {
    CLEANUP_INTERVAL: number,
    CLEANUP_PROBABILITY: number,
    HISTORY_RETENTION_TIME: number,
  },
  Logger: {
    CONSOLE_LOGGING: boolean,
    DATASTORE_LOGGING: boolean,
    WEBHOOK_LOGGING: boolean,
    WEBHOOK_URL: string?,
    DATASTORE_NAME: string,
    MAX_LOG_ENTRIES: number,
    SENSITIVE_COMMANDS: { [string]: boolean },
    LOG_RETENTION_DAYS: number,
  },
  Analytics: AnalyticsConfig,
}

-- ============================================================================
-- MIDDLEWARE INITIALIZATION LAYER TYPES
-- ============================================================================
export type MiddlewareResult = {
  success: boolean,
  errorMessage: string?,
  data: any?,
}

export type MiddlewareModule = {
  name: string,
  priority: number,
  enabled: boolean,
  process: (player: Player, commandName: string, args: { string }?) -> (boolean, string?, any?),
  init: (() -> boolean)?,
  cleanup: (() -> ())?,
  version: string?,
  getStatistics: (() -> { [string]: any })?,
  forceCleanup: (() -> ())?,
  setCommandRateLimit: ((string, RateLimitConfig) -> ())?,
}

export type MiddlewareChainStats = {
  totalExecutions: number,
  successfulExecutions: number,
  failedExecutions: number,
  averageExecutionTime: number,
  lastExecutionTime: number,
}

-- ============================================================================
-- MIDDLEWARE ANALYTICS LAYER TYPES
-- ============================================================================
export type CommandStatistics = {
  totalUses: number,
  uniqueUsers: number,
  lastUsed: number,
  firstUsed: number,
  averageArgsCount: number,
  errorCount: number,
  successCount: number,
  averageExecutionTime: number?,
}

export type PlayerAnalytics = {
  totalCommands: number,
  uniqueCommands: { [string]: number },
  firstCommandTime: number,
  lastCommandTime: number,
  mostUsedCommand: string?,
  averageSessionLength: number?,
  commandsPerSession: number?,
  errorRate: number,
}

export type SystemMetrics = {
  totalCommandsProcessed: number,
  totalPlayers: number,
  averageCommandsPerPlayer: number,
  mostPopularCommand: string?,
  peakUsageTime: number,
  systemUptime: number,
  totalErrors: number,
  successRate: number,
}

-- ============================================================================
-- MIDDLEWARE LOGGER LAYER TYPES
-- ============================================================================

export type LoggerConfig = {
  CONSOLE_LOGGING: boolean,
  DATASTORE_LOGGING: boolean,
  WEBHOOK_LOGGING: boolean,
  WEBHOOK_URL: string?,
  DATASTORE_NAME: string,
  MAX_LOG_ENTRIES: number,
  SENSITIVE_COMMANDS: { [string]: boolean },
  LOG_RETENTION_DAYS: number,
}

export type LogEntry = {
  timestamp: number,
  dateString: string,
  player: {
    userId: number,
    name: string,
    displayName: string?,
  },
  command: string,
  arguments: { string }?,
  sensitive: boolean,
  executionTime: number?,
}

export type WebhookPayload = {
  content: string,
  embeds: {
    {
      title: string,
      description: string,
      color: number,
      timestamp: string,
      fields: {
        {
          name: string,
          value: string,
          inline: boolean,
        }
      },
    }
  }?,
}

-- ============================================================================
-- MIDDLEWARE RATE LIMITER LAYER TYPES
-- ============================================================================
export type RateLimitConfig = {
  maxCommands: number,
  windowSeconds: number,
  burstAllowance: number,
}

export type CommandHistoryEntry = {
  timestamp: number,
  commandName: string,
}

export type UserRateLimitData = {
  commandHistory: { CommandHistoryEntry },
  lastCleanup: number,
  totalCommands: number,
  violationCount: number,
  lastViolation: number?,
}

-- ============================================================================
-- COMMAND DISPATCHER TYPES
-- ============================================================================
export type CommandModule = {
  Execute: (
    player: Player,
    args: { string },
    prefix: string
  ) -> {
    success: boolean,
    message: string?,
  },
  Aliases: { string }?,
  Description: string?,
  Usage: string?,
  PermissionLevel: number?,
}

export type CommandRegistryEntry = {
  module: CommandModule,
  version: number,
  aliases: { string },
  path: string,
}

export type CommandRegistry = {
  [string]: {
    module: CommandModule,
    version: number,
    aliases: { string },
    path: string,
  },
}

export type AliasLookup = { [string]: string }

export type CommandCategory = "System" | "Moderation" | "Utility" | "Developer"

export type AvailableCommand = {
  name: string,
  description: string,
  usage: string,
  aliases: { string },
}

-- ============================================================================
-- PERMISSION SERVICE TYPES
-- ============================================================================
export type PermissionLevels = {
  PLAYER: number,
  DISDevelopers: number,
  JuniorModerators: number,
  GameModerators: number,
  SeniorModerators: number,
  Anonmancer: number,
  BleckWolf25: number,
}

export type CommandPermissions = { [string]: number }

export type PermissionCacheEntry = {
  level: number,
  lastUpdated: number,
}

export type PermissionCache = { [number]: PermissionCacheEntry }

export type AccessibleCommands = { string }

-- ============================================================================
-- PRIVATE SERVER PERMISSIONS TYPES
-- ============================================================================
export type PermissionsData = { [number]: number }

-- ============================================================================
-- REMOTE TYPES
-- ============================================================================
export type CommandRequest = {
  commandName: string,
  args: { string }?,
}

export type CommandResponse = {
  success: boolean,
  message: string?,
  data: any?,
}

export type QueryResponse = {
  success: boolean,
  message: string?,
  data: { AvailableCommand }?,
}

-- ============================================================================
-- EXPORTS
-- ============================================================================
return MCS_Types
