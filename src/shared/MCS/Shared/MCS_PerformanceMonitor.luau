--!strict

--[[
    - file: MCS_PerformanceMonitor.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Performance monitoring module for the Modular Command System (MCS)
      - Tracks execution times and errors for system operations
      - Provides functions to start/stop timers, record errors, and retrieve statistics
]]

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local PerformanceMonitor = {}

-- ============================================================================
-- TYPES (should not be moved to MCS_Types)
-- ============================================================================

-- Private tables to store timing data
local activeTimers: { [string]: number } = {} -- Stores start times for active operations
local statistics: {
  [string]: {
    count: number,
    totalTime: number,
    minTime: number,
    maxTime: number,
    errors: number,
  },
} =
  {} -- Stores aggregated stats per identifier

-- ============================================================================
-- PERFORMANCE MONITOR FUNCTIONS
-- ============================================================================

-- Start timing an operation
function PerformanceMonitor.StartTimer(identifier: string)
  activeTimers[identifier] = os.clock()
end

-- End timing an operation, update stats, and return elapsed time
function PerformanceMonitor.EndTimer(identifier: string): number?
  local startTime = activeTimers[identifier]
  if startTime then
    local elapsed = os.clock() - startTime
    activeTimers[identifier] = nil -- Clear the timer

    -- Initialize stats for this identifier if not present
    local stats = statistics[identifier]
    if not stats then
      stats = {
        count = 0,
        totalTime = 0,
        minTime = math.huge, -- Start with infinity for min
        maxTime = 0,
        errors = 0,
      }
      statistics[identifier] = stats
    end

    -- Update statistics
    stats.count += 1
    stats.totalTime += elapsed
    if elapsed < stats.minTime then
      stats.minTime = elapsed
    end
    if elapsed > stats.maxTime then
      stats.maxTime = elapsed
    end

    return elapsed
  end
  return nil -- No timer was started for this identifier
end

-- Record an error for an operation
function PerformanceMonitor.RecordError(identifier: string)
  local stats = statistics[identifier]
  if not stats then
    stats = {
      count = 0,
      totalTime = 0,
      minTime = 0,
      maxTime = 0,
      errors = 0,
    }
    statistics[identifier] = stats
  end
  stats.errors += 1
end

-- Get statistics for a specific operation
function PerformanceMonitor.GetStatistics(identifier: string): {
  count: number,
  averageTime: number,
  minTime: number,
  maxTime: number,
  errors: number,
}
  local stats = statistics[identifier]
  if stats then
    local averageTime = stats.count > 0 and (stats.totalTime / stats.count) or 0
    return {
      count = stats.count,
      averageTime = averageTime,
      minTime = stats.minTime,
      maxTime = stats.maxTime,
      errors = stats.errors,
    }
  else
    return {
      count = 0,
      averageTime = 0,
      minTime = 0,
      maxTime = 0,
      errors = 0,
    }
  end
end

-- Get statistics for all operations
function PerformanceMonitor.GetAllStatistics(): {
  [string]: {
    count: number,
    averageTime: number,
    minTime: number,
    maxTime: number,
    errors: number,
  },
}
  local allStats = {}
  for identifier, stats in pairs(statistics) do
    local averageTime = stats.count > 0 and (stats.totalTime / stats.count) or 0
    allStats[identifier] = {
      count = stats.count,
      averageTime = averageTime,
      minTime = stats.minTime,
      maxTime = stats.maxTime,
      errors = stats.errors,
    }
  end
  return allStats
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return PerformanceMonitor
