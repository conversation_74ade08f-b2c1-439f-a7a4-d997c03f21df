--!strict

--[[
    - file: MCS_Remotes.luau

    - version: 1.0.3
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Remote Event and Function handler for Modular Command System (MCS)
      - Manages client-server communication for command execution and autocompletion
      - Creates remote structure under ReplicatedStorage.MCS.Remotes
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")

-- ============================================================================
-- MODULES
-- ============================================================================
local CommandDispatcher =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_CommandDispatcher"))
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local PermissionService =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PermissionService"))
local Types = require(ReplicatedStorage:WaitForChild("MCS"):WaitForChild("MCS_Types"))
local Utils =
  require(ReplicatedStorage:WaitForChild("MCS"):WaitForChild("Shared"):WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local MODULE_NAME = "MCS_Remotes"
local DEFAULT_PREFIX = Configuration.Commands.PREFIX

-- ============================================================================
-- VARIABLES
-- ============================================================================
local isInitialized: boolean = false
local remoteFolder: Folder?
local commandRemote: RemoteEvent?
local autocompleteRemote: RemoteFunction?
local checkConsolePermission: RemoteFunction?

-- ============================================================================
-- PRIVATE FUNCTIONS
-- ============================================================================

-- Validate command request structure
local function validateCommandRequest(request: Types.CommandRequest): (boolean, string?)
  if type(request) ~= "table" then
    return false, "Invalid request format"
  end

  if type(request.commandName) ~= "string" or request.commandName == "" then
    return false, "Invalid command name"
  end

  if request.args and type(request.args) ~= "table" then
    return false, "Invalid arguments format"
  end

  local sanitizedCommandName = Utils.sanitize(request.commandName) :: string
  if sanitizedCommandName == "" then
    return false, "Sanitized command name is empty"
  end
  request.commandName = sanitizedCommandName

  if request.args then
    local maxArgs = Configuration.Commands.MAX_ARGS or 10
    if #request.args > maxArgs then
      return false, string.format("Too many arguments. Maximum allowed: %d", maxArgs)
    end

    for i, arg in ipairs(request.args) do
      if type(arg) ~= "string" then
        return false, "Invalid argument type at index " .. tostring(i)
      end
      local sanitizedArg = Utils.sanitize(arg) :: string
      if sanitizedArg == "" then
        return false, "Sanitized argument at index " .. tostring(i) .. " is empty"
      end
      request.args[i] = sanitizedArg
    end
  end

  return true, nil
end

-- Handle command submission from client
local function handleCommandRequest(
  player: Player,
  request: Types.CommandRequest
): Types.CommandResponse
  Utils.startTimer(MODULE_NAME, "HandleCommandRequest")

  if not player or not player.Parent then
    Utils.endTimer(MODULE_NAME, "HandleCommandRequest")
    return { success = false, message = "Invalid player" }
  end

  local isValid, errorMessage = validateCommandRequest(request)
  if not isValid then
    Utils.print(
      MODULE_NAME,
      string.format("Invalid command request from %s: %s", player.Name, errorMessage or "unknown")
    )
    Utils.endTimer(MODULE_NAME, "HandleCommandRequest")
    return { success = false, message = errorMessage or "Invalid request" }
  end

  -- Use CommandDispatcher to process the command (includes middleware + execution)
  local commandText = Configuration.Commands.PREFIX .. request.commandName
  if request.args and #request.args > 0 then
    commandText = commandText .. " " .. table.concat(request.args, " ")
  end

  local success, message = CommandDispatcher.processCommand(player, commandText)

  if not success then
    Utils.print(
      MODULE_NAME,
      string.format(
        "Command '%s' failed for %s: %s",
        request.commandName,
        player.Name,
        message or "unknown"
      )
    )
  else
    Utils.print(
      MODULE_NAME,
      string.format("Command '%s' processed successfully for %s", request.commandName, player.Name)
    )
  end

  Utils.endTimer(MODULE_NAME, "HandleCommandRequest")
  return { success = success, message = message }
end

-- Handle autocomplete requests from client
local function handleAutocompleteRequest(player: Player, input: string): Types.QueryResponse
  Utils.startTimer(MODULE_NAME, "HandleAutocompleteRequest")

  -- Validate player.Parent (player itself is guaranteed to be Player by the type signature)
  if player.Parent == nil then
    Utils.endTimer(MODULE_NAME, "HandleAutocompleteRequest")
    return { success = false, message = "Invalid player" }
  end

  -- Validate input
  if typeof(input) ~= "string" then
    Utils.endTimer(MODULE_NAME, "HandleAutocompleteRequest")
    return { success = false, message = "Invalid input type" }
  end

  local sanitizedInput = Utils.sanitize(input)
  assert(typeof(sanitizedInput) == "string", "Sanitized input must be a string")
  if sanitizedInput == "" then
    Utils.endTimer(MODULE_NAME, "HandleAutocompleteRequest")
    return { success = false, message = "Empty input" }
  end

  -- Retrieve accessible commands
  local ok, accessibleCommands = pcall(PermissionService.getAccessibleCommands, player)
  if not ok then
    Utils.print(
      MODULE_NAME,
      string.format(
        "Failed to retrieve commands for %s: %s",
        player.Name,
        tostring(accessibleCommands)
      )
    )
    Utils.endTimer(MODULE_NAME, "HandleAutocompleteRequest")
    return { success = false, message = "Failed to retrieve commands" }
  end

  local suggestions: { Types.AvailableCommand } = {}
  local maxSuggestions = Configuration.Commands.MAX_SUGGESTIONS or 5

  -- Try CommandDispatcher autocomplete
  local autocompleteResults = CommandDispatcher.getAutocompleteSuggestions(sanitizedInput)
  for i, suggestionText in ipairs(autocompleteResults) do
    if i > maxSuggestions then
      break
    end

    local prefix = Configuration.Commands.PREFIX or DEFAULT_PREFIX
    local commandName = suggestionText:gsub("^" .. Utils.escapePattern(prefix), ""):match("^(%S+)")

    if commandName then
      local commandModule = CommandDispatcher.getCommandModule(commandName)
      if not commandModule then
        local resolved = CommandDispatcher.resolveAlias(commandName)
        if resolved then
          commandModule = CommandDispatcher.getCommandModule(resolved)
        end
      end

      local info: Types.AvailableCommand = if commandModule
        then {
          name = commandName,
          description = commandModule.Description or "No description available",
          usage = suggestionText,
          aliases = commandModule.Aliases or {},
        }
        else {
          name = suggestionText,
          description = "Suggestion",
          usage = suggestionText,
          aliases = {},
        }

      table.insert(suggestions, info)
    end
  end

  -- Fallback suggestion logic
  if #suggestions == 0 then
    local prefix = Configuration.Commands.PREFIX or DEFAULT_PREFIX
    local inputWithoutPrefix = (Utils.stripPrefix(sanitizedInput, prefix) or "") :: string
    local tokens = Utils.splitCommandText(inputWithoutPrefix)
    local inputLower = inputWithoutPrefix:lower()

    if #tokens == 1 then
      -- Suggest commands
      local commandList = CommandDispatcher.getCommandList()
      for _, cmdName in ipairs(commandList) do
        local module = CommandDispatcher.getCommandModule(cmdName)
        if module then
          local match = cmdName:lower():sub(1, #inputLower) == inputLower
          if not match then
            for _, alias in ipairs(module.Aliases or {}) do
              if alias:lower():sub(1, #inputLower) == inputLower then
                match = true
                break
              end
            end
          end

          if match and table.find(accessibleCommands :: Types.AccessibleCommands, cmdName) then
            local usage = module.Usage or (cmdName .. " [args]")
            table.insert(suggestions, {
              name = cmdName,
              description = module.Description or "No description available",
              usage = prefix .. usage,
              aliases = module.Aliases or {},
            })
            if #suggestions >= maxSuggestions then
              break
            end
          end
        end
      end
    elseif #tokens >= 2 then
      -- Suggest players for commands requiring a player name
      local commandName = tokens[1]:lower()
      local playerCommands = Configuration.Commands.PLAYER_ARGUMENT_COMMANDS

      local isPlayerCommand = false
      if typeof(playerCommands) == "table" then
        isPlayerCommand = table.find(playerCommands, commandName) ~= nil
      end

      if isPlayerCommand then
        local partial = tokens[2]:lower()
        for _, otherPlayer in ipairs(Players:GetPlayers()) do
          if Utils.playerNameMatches(otherPlayer, partial) then
            local module = CommandDispatcher.getCommandModule(commandName)
            if
              module and table.find(accessibleCommands :: Types.AccessibleCommands, commandName)
            then
              table.insert(suggestions, {
                name = commandName,
                description = module.Description or "No description available",
                usage = prefix .. commandName .. " " .. otherPlayer.Name,
                aliases = module.Aliases or {},
              })
              if #suggestions >= maxSuggestions then
                break
              end
            end
          end
        end
      end
    end
  end

  -- Sort alphabetically
  table.sort(suggestions, function(a, b)
    return a.name < b.name
  end)

  Utils.print(
    MODULE_NAME,
    string.format("Returned %d autocomplete suggestions for %s", #suggestions, player.Name)
  )
  Utils.endTimer(MODULE_NAME, "HandleAutocompleteRequest")

  return { success = true, data = suggestions }
end

-- ============================================================================
-- PUBLIC FUNCTIONS
-- ============================================================================

--[[
  Initializes the Remotes system for handling client-server communication.

  @desc
    - Ensures required folders and remote instances exist in ReplicatedStorage
    - Connects remote handlers for command dispatch, autocomplete, and permission checks

  @return boolean - True if initialization succeeds, false if an error occurs
]]
local function init(): boolean
  if isInitialized then
    Utils.print(MODULE_NAME, "Remotes system already initialized")
    return true
  end

  Utils.startTimer(MODULE_NAME, "Init")

  local success = pcall(function()
    -- Ensure "MCS" folder exists
    local mcsFolder = ReplicatedStorage:FindFirstChild("MCS")
    if not mcsFolder then
      mcsFolder = Instance.new("Folder")
      mcsFolder.Name = "MCS"
      mcsFolder.Parent = ReplicatedStorage
    end
    assert(mcsFolder:IsA("Folder"), "MCS folder is not a Folder instance")

    -- Ensure "Remotes" folder exists
    remoteFolder = mcsFolder:FindFirstChild("Remotes")
    if not remoteFolder then
      remoteFolder = Instance.new("Folder")
      remoteFolder.Name = "Remotes"
      remoteFolder.Parent = mcsFolder
    end
    assert(remoteFolder:IsA("Folder"), "Remotes folder is not a Folder instance")

    -- Create RemoteEvent and RemoteFunctions
    commandRemote = Instance.new("RemoteEvent")
    commandRemote.Name = "CommandRemote"
    commandRemote.Parent = remoteFolder

    autocompleteRemote = Instance.new("RemoteFunction")
    autocompleteRemote.Name = "AutocompleteRemote"
    autocompleteRemote.Parent = remoteFolder

    checkConsolePermission = Instance.new("RemoteFunction")
    checkConsolePermission.Name = "CheckConsolePermission"
    checkConsolePermission.Parent = remoteFolder

    -- Handle incoming command requests
    commandRemote.OnServerEvent:Connect(function(player: Player, request: Types.CommandRequest)
      Utils.print(MODULE_NAME, "Received command request from " .. player.Name)
      Utils.print(MODULE_NAME, "Request type: " .. typeof(request))
      if request and request.commandName then
        Utils.print(MODULE_NAME, "Command name: " .. request.commandName)
      end

      local response = handleCommandRequest(player, request)
      Utils.print(
        MODULE_NAME,
        "Sending response to " .. player.Name .. ": " .. tostring(response.success)
      )

      if commandRemote then
        commandRemote:FireClient(player, response)
      else
        Utils.print(MODULE_NAME, "Warning: commandRemote is nil during FireClient")
      end
    end)

    -- Handle autocomplete requests
    autocompleteRemote.OnServerInvoke = function(player: Player, input: string): Types.QueryResponse
      Utils.print(
        MODULE_NAME,
        "Autocomplete request from " .. player.Name .. ": " .. tostring(input)
      )
      local result = handleAutocompleteRequest(player, input)
      Utils.print(MODULE_NAME, "Autocomplete response success: " .. tostring(result.success))
      if result.data then
        Utils.print(MODULE_NAME, "Autocomplete suggestions count: " .. #result.data)
      end
      return result
    end

    -- Check permission level for console access
    checkConsolePermission.OnServerInvoke = function(player: Player): boolean
      local effectiveLevel = PermissionService.getEffectivePermissionLevel(player)
      return effectiveLevel >= PermissionService.Levels.DISDevelopers
    end

    isInitialized = true
    Utils.print(MODULE_NAME, "Remotes system initialized successfully")
  end)

  Utils.endTimer(MODULE_NAME, "Init")

  if not success then
    Utils.print(MODULE_NAME, "Failed to initialize remotes")
    return false
  end

  return true
end

-- Cleanup the remotes system
local function cleanup()
  Utils.print(MODULE_NAME, "Cleaning up remotes system")

  if remoteFolder then
    remoteFolder:Destroy()
    remoteFolder = nil
  end

  commandRemote = nil
  autocompleteRemote = nil
  checkConsolePermission = nil
  isInitialized = false

  Utils.print(MODULE_NAME, "Remotes system cleaned up")
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return {
  init = init,
  cleanup = cleanup,
}
