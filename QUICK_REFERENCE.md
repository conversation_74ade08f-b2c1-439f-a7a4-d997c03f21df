# 🚀 Site-112 Quick Reference

> **Fast access to essential commands, links, and information for Site-112 development**

---

## ⚡ Quick Commands

### Development Setup

```bash
# Initial setup
./scripts/setup-dev.sh

# Install tools only
./scripts/setup-dev.sh --tools

# Setup Git hooks only
./scripts/setup-dev.sh --hooks
```

### Code Quality

```bash
# Run linting
npm run lint
./scripts/lint.sh

# Auto-fix formatting
npm run lint:fix
./scripts/lint.sh --fix

# Format code
npm run format
stylua src/

# Run all checks
npm test
```

### Development Workflow

```bash
# Open workspace
code site-112-workspace.code-workspace

# Check tool status
selene --version
stylua --version
luau-lsp --version

# Generate lint report
npm run lint:report
./scripts/lint.sh --report
```

---

## 📁 Important File Locations

### Configuration Files

- **Luau Config**: `.luaurc`
- **Linting**: `selene.toml`
- **Formatting**: `stylua.toml`
- **Tools**: `aftman.toml`
- **Project**: `package.json`

### Documentation

- **Development**: `docs/development/`
- **Systems**: `docs/project/systems/`
- **Company**: `docs/company/`

### Scripts

- **Setup**: `scripts/setup-dev.sh`
- **Linting**: `scripts/lint.sh`
- **Pre-commit**: `scripts/pre-commit.sh`

---

## 🔗 Essential Links

### Development

- **[VSCode Setup Guide](docs/development/VSCODE_SETUP_GUIDE.md)**
- **[Linting & CI/CD Guide](docs/development/LINTING_AND_CICD_GUIDE.md)**
- **[MAFS Documentation](docs/project/systems/MAFS/MAFS-README.md)**

### External Resources

- **[Roblox Creator Docs](https://create.roblox.com/docs)**
- **[Studio Script Sync](https://create.roblox.com/docs/studio/script-sync)**
- **[Luau Language](https://luau-lang.org/)**
- **[SCP Foundation](https://scp-wiki.wikidot.com/)**

### Organization

- **[Website](https://dynamic-innovative-studio.web.app)**
- **[Discord](https://discord.gg/nGEnj6abUs)**
- **[GitHub](https://github.com/Dynamic-Innovative-Studio/Site-112)**

---

## 🐛 Troubleshooting

### Common Issues

**Selene errors about unknown fields**

```bash
# Update roblox.yml if needed
# Check selene.toml configuration
```

**StyLua formatting issues**

```bash
# Auto-fix formatting
stylua src/
npm run lint:fix
```

**Tools not found**

```bash
# Reinstall tools
aftman install
npm run tools:install
```

**Studio Script Sync not working**

1. Enable Script Sync in File → Beta Features
2. Open your place file
3. Check that src/ folder is syncing

### Getting Help

1. **Check documentation** in `docs/`
2. **Run diagnostics**: `npm test`
3. **Ask in Discord**: [Community Server](https://discord.gg/nGEnj6abUs)
4. **Contact team**: <<EMAIL>>

---

## 📊 Project Status

- **Version**: Alpha v3 Development
- **Platform**: Roblox
- **Language**: Luau (Advanced)
- **Sync Method with Roblox**: Studio Script Sync (Beta feature)
- **Code Quality**: Selene + StyLua + Luau LSP

**Need something added to this quick reference? Contact the co-founder or founder & ceo!**
