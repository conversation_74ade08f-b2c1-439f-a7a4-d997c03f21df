# Diurnal Cycle Processor System (DCPS)

The Diurnal Cycle Processor System (DCPS) orchestrates dynamic time-of-day transitions, lighting profiles, and remote event broadcasting for immersive in-game environments.

## Features

- **Time Segment Detection:** Automatically determines the current time segment (e.g., Sunrise, Morning, Afternoon, Sunset, Night, Midnight) based on in-game time.
- **Lighting Transitions:** Smoothly applies lighting profiles for each segment, including atmosphere, skybox, sun rays, brightness, and exposure compensation.
- **Day/Night Progression:** Advances in-game time using real-time delta, wrapping at 24 hours.
- **Remote Events:** Broadcasts segment changes via bindable events for other systems to react (e.g., turning on lights at sunset).
- **Asset Preloading:** Preloads skybox assets to ensure seamless visual transitions.
- **Modular Configuration:** Centralized configuration for all segments and lighting profiles.

## Key Modules

- `DCPS_Server.luau`: Main orchestrator, segment detection, and event firing.
- `DCPS_Configuration.luau`: Defines all time segments and their lighting profiles.
- `DCPS_Types.luau`: Type definitions for segments, profiles, and configuration.
- `DCPS_Utils.luau`: Utilities for logging, time conversion, lighting transitions, and asset preloading.
- `DCPS_Remotes.luau`: Centralized remote event system for segment changes.
- `DCPS_CycleDayNight.luau`: Manages in-game clock progression.

## Data Flow Overview

See `DCPS-data-flow.md` for detailed diagrams of:

- System architecture
- Time segment evaluation
- Lighting transitions
- Day/night progression
- Remote event broadcasting
- Asset preloading

## Usage Example

```lua
local DCPS_Remotes = require(ServerStorage.DCPS.DCPS_Remotes)
local Utils = require(ServerStorage.DCPS.Shared.DCPS_Utils)

local TAG: string = "UsageExample"

DCPS_Remotes.OnTimeSegment("Sunset", function()
    Utils.log(TAG, "It's sunset! Turning on outside lights...")
end)
```

Output:

```log
[UsageExample] It's sunset! Turning on outside lights...
```

## Extending DCPS

- Add new segments or adjust lighting profiles in `DCPS_Configuration.luau`.
- Listen for segment changes using `DCPS_Remotes.OnTimeSegment`.
- Integrate with other systems by responding to remote events.
- Customize lighting transitions in `DCPS_Utils.applyLightingTransition`.
- Adjust in-game clock progression in `DCPS_CycleDayNight.Start`.
