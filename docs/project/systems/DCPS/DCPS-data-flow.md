# DCPS Data Flow Diagrams

This document provides comprehensive data flow diagrams for the Diurnal Cycle Processor System (DCPS), illustrating how data flows through each subsystem and component.

## 1. Overall DCPS System Architecture

```mermaid
flowchart TB
    subgraph "Server Side (src/server/DCPS)"
        DS[DCPS_Server.luau\n(Main Orchestrator)]
    end

    subgraph "Shared (src/serverStorage/DCPS)"
        DC[DCPS_Configuration.luau]
        DT[DCPS_Types.luau]
        DU[DCPS_Utils.luau]
        DR[DCPS_Remotes.luau]
        CD[DCPS_CycleDayNight.luau]
    end

    subgraph "External"
        LG[Lighting Service]
        CP[ContentProvider]
    end

    DS -- Reads Config --> DC
    DS -- Uses Types --> DT
    DS -- Uses Utils --> DU
    DS -- Fires Events --> DR
    DS -- Starts Cycle --> CD
    DS -- Controls Lighting --> LG

    DU -- Preloads Assets --> CP
    DU -- Applies Lighting --> LG
    CD -- Updates ClockTime --> LG
    DR -- Broadcasts Segment Events --> DS
```

## 2. Time Segment Evaluation & Transition

```mermaid
flowchart TB
    DS[DCPS_Server Tick Loop] --> LT[Lighting.TimeOfDay]
    LT --> TN[Utils.timeStringToNumber]
    TN --> ES[evaluateTimeSegment]
    ES --> SC[Segment Changed?]
    SC -- Yes --> OC[onSegmentChanged]
    OC --> DR[Remotes.FireTimeSegment]
    OC --> AL[Utils.applyLightingTransition]
    AL --> LG[Lighting Service]
    SC -- No --> DS
```

## 3. Lighting Transition System

```mermaid
flowchart TB
    AL[applyLightingTransition] --> AT[Atmosphere Tween]
    AL --> SR[SunRays Tween]
    AL --> SB[Skybox Swap]
    AT --> LG[Lighting.Atmosphere]
    SR --> LG[Lighting.SunRaysEffect]
    SB --> LG[Lighting.Sky]
```

## 4. Day/Night Clock Progression

```mermaid
flowchart TB
    CD[DCPS_CycleDayNight.Start] --> RT[os.clock Delta]
    RT --> CT[Update currentClockTime]
    CT --> WC[Wrap at 24h]
    WC --> LC[Lighting.ClockTime]
    LC --> DS[DCPS_Server]
```

## 5. Remote Event System

```mermaid
flowchart TB
    DS[DCPS_Server] --> DR[Remotes.FireTimeSegment]
    DR --> BE[BindableEvents Table]
    BE --> EV[OnTimeSegment Listeners]
    EV --> CB[Callback Execution]
    CB --> EXT[External Systems]
```

## 6. Asset Preloading System

```mermaid
flowchart TB
    DU[DCPS_Utils.preloadSkyboxAssets] --> SB[Skybox Asset IDs]
    SB --> CP[ContentProvider.PreloadAsync]
    CP --> LG[Lighting.Sky]
```

## Summary

These data flow diagrams illustrate the complete architecture of the DCPS system:

1. **Overall Architecture** – Shows the high-level interaction between server, shared modules, and external services.
2. **Time Segment Evaluation** – Details how the server detects and transitions between time segments.
3. **Lighting Transition** – Illustrates how lighting profiles are smoothly applied.
4. **Day/Night Clock Progression** – Shows how in-game time advances and updates Lighting.
5. **Remote Event System** – Details how segment changes are broadcast and handled.
6. **Asset Preloading** – Shows how skybox assets are preloaded for seamless transitions.

Each system operates independently while maintaining clear interfaces, ensuring modularity and maintainability.

Use Markdown Enhanced Preview to view the diagrams.
