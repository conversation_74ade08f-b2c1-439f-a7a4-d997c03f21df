# MAFS Data Flow Diagrams

This document provides comprehensive data flow diagrams for the Modular Audio FootStep System (MAFS), illustrating how data flows through each subsystem and component.

## 1. Overall MAFS System Architecture

```mermaid
flowchart TB
    subgraph "Client Side (src/client/MAFS)"
        CM[MAFS_Client_Initilization.luau\n(Movement Detection)]
        CP[MAFS_Client_Module.luau\n(Sound Playback)]
        CC[Client Config]
        CV[Volume Control]
        CS[Sound Pool]
    end

    subgraph "Server Side (src/server/MAFS)"
        SV[MAFS_Server_Initilization.luau\n(Request Validation)]
        SM[MAFS_Server_Manager.luau\n(Material Detection & Broadcast)]
        SP[Player State Tracking]
    end

    subgraph "Shared (src/shared/MAFS)"
        SC[Configurations/Systems/MAFS.luau]
        SMC[MaterialConfig.luau]
        SU[Utils/]
        SR[Remotes.luau]
        SAPI[API.luau]
    end

    subgraph "External"
        WS[Workspace Parts]
        PL[Players]
        RS[ReplicatedStorage]
    end

    CM -- Position Data --> SR
    SR -- Position Data --> SV
    SV -- Validated Request --> SM
    SM -- Material Data --> SR
    SR -- Audio Data --> CP
    CP -- Sound Instance --> CS

    SC -.-> CM
    SC -.-> SV
    SMC -.-> SM
    SU -.-> SM

    WS -.-> SM
    PL -.-> SP
    RS -.-> SR
```

## 2. Client-Side Movement Detection System

```mermaid
flowchart TB
    RB[RenderStep Binding] --> MC[Movement Check]
    MC --> CD[Cooldown Check]
    CD -- Pass --> VT[Velocity Threshold]
    CD -- Fail --> RB
    VT -- Pass --> FM[Floor Material Check]
    VT -- Fail --> RB
    FM -- Pass --> PR[Position Request]
    FM -- Fail --> RB
    PR --> RE[RemoteEvent:FireServer]
    RE --> PD[Position Data]

    MC -.-> CH[Character]
    MC -.-> HU[Humanoid]
    MC -.-> RP[HumanoidRootPart]
    FM -.-> HF[Humanoid.FloorMaterial]
    CD -.-> CT[CLIENT_COOLDOWN]
    VT -.-> MT[MovementThreshold]
    MC -.-> EN[footstepEnabled]
```

## 3. Server-Side Validation & Broadcasting System

```mermaid
flowchart TB
    IR[Incoming Request] --> PV[Player Validation]
    PV -- Valid --> CV[Character Validation]
    PV -- Invalid --> PM[Performance Metrics]
    CV -- Valid --> CD[Cooldown Check]
    CV -- Invalid --> PM
    CD -- Valid --> POS[Position Validation]
    CD -- Invalid --> PM
    POS -- Valid --> DD[Distance Delta Check]
    POS -- Invalid --> PM
    DD -- Valid --> MR[Material Resolver]
    DD -- Invalid --> PM

    MR --> CA[Custom Attributes]
    CA -- Found --> MD[Material Data]
    CA -- Not Found --> FM[Floor Material]
    FM -- Found --> MD
    FM -- Not Found --> DM[Default Material]
    DM --> MD

    MD --> SI[Sound ID Selection]
    SI --> VS[Volume Setting]
    VS --> PS[Playback Speed]
    PS --> RO[RollOff Settings]
    RO --> AD[Audio Data Package]

    AD --> PF[Player Filtering]
    PF --> DR[Distance Radius Check]
    DR --> BC[Broadcast to Clients]
    BC --> PM
```

## 4. Material Resolution System

```mermaid
flowchart TB
    MR[Material Resolution Request] --> POS[Position Input]
    MR --> HUM[Humanoid Input]
    POS --> RC[Raycast from Position]
    RC --> HP[Hit Part Detection]
    HP --> CA[Check FootstepMaterial Attribute]
    CA -- Found --> CM[Custom Material Found]
    CA -- Not Found --> PM[Check Parent Model]
    PM --> PA[Parent Attribute Check]
    PA -- Found --> PMA[Parent Material Found]
    PA -- Not Found --> HF[Humanoid.FloorMaterial]
    HF --> RM[Roblox Material Mapping]
    RM -- Found --> RMF[Roblox Material Found]
    RM -- Not Found --> DF[Default Material]
    DF --> DM[Default Material Data]
    CM --> MD[Material Data Package]
    PMA --> MD
    RMF --> MD
    DM --> MD
    MD --> SI[Sound IDs]
    MD --> VOL[Volume Settings]
    MD --> PS[Playback Speed Range]
    MD --> RO[RollOff Settings]
```

## 5. Sound Pooling & Playback System

```mermaid
flowchart TB
    SR[Sound Request] --> SC[Sound Cache Check]
    SC -- Hit --> AU[Available Sound Check]
    SC -- Miss --> CS[Create New Sound]
    AU -- Available --> RS[Reuse Existing Sound]
    AU -- Busy --> CS
    CS --> AD[Audio Data Input]
    RS --> AD
    AD --> SID[Sound ID Setting]
    SID --> VOL[Volume Setting]
    VOL --> PBS[Playback Speed Setting]
    PBS --> POS[3D Position Setting]
    POS --> ROF[RollOff Configuration]
    ROF --> SG[Sound Group Assignment]
    SG --> PL[Play Sound]
    PL --> PM[Performance Monitoring]
    PL --> RT[Return to Pool]
    RT --> MC[Max Cache Check]
    MC -- Under Limit --> ST[Statistics Tracking]
    MC -- Over Limit --> CL[Cache Limit Enforcement]
    CL --> SD[Sound Destruction]
    SD --> ST
```

## 6. Performance Monitoring System

```mermaid
flowchart TB
    SC[Sound Creation Events] --> SM[Sound Metrics]
    SD[Sound Destruction Events] --> SM
    NR[Network Requests] --> NM[Network Metrics]
    MR[Material Resolutions] --> MM[Material Metrics]
    ER[Error Events] --> EM[Error Metrics]
    SM --> TH[Threshold Checking]
    NM --> TH
    MM --> TH
    PM[Performance Metrics] --> TH
    EM --> TH
    TH --> AL[Alert Generation]
    TH --> EF[Efficiency Calculation]
    EF --> RP[Report Generation]
    AL --> WA[Warnings]
    SM --> MT[Metrics Table]
    NM --> MT
    MM --> MT
    PM --> MT
    EM --> MT
    MT --> ST[Statistics]
    AL --> DB[Debug Output]
    RP --> DB
```

## 7. Remote Communication System

```mermaid
flowchart TB
    SF[Server Folder Creation] --> SE[Server Event Creation]
    SE --> SL[Server Event Listener]
    SL --> SH[Server Event Handler]
    CF[Client Folder Access] --> CE[Client Event Access]
    CE --> CR[Client Request Sender]
    CE --> CL[Client Event Listener]
    CR --> REQ[Footstep Request]
    REQ --> VAL[Server Validation]
    VAL --> RES[Audio Response]
    RES --> CL
    VAL --> BRD[Broadcast to Others]
    CE --> TO[Timeout Handling]
    TO --> RT[Retry Logic]
    RT --> ER[Error Reporting]
    ER --> FB[Fallback Mechanisms]
    SH --> VAL
    SH --> BRD
```

## Summary

These data flow diagrams illustrate the complete architecture of the MAFS system:

1. **Overall Architecture** - Shows the high-level interaction between client, server, and shared components
2. **Movement Detection** - Details how client-side movement is detected and validated
3. **Server Validation & Broadcasting** - Shows the server-side validation pipeline and broadcasting logic
4. **Material Resolution** - Illustrates the hierarchical material resolution system
5. **Sound Pooling & Playback** - Details the efficient sound management and playback system
6. **Performance Monitoring** - Shows how metrics are collected and analyzed
7. **Remote Communication** - Details the client-server communication layer

Each system operates independently while maintaining clear interfaces with other components, ensuring modularity and maintainability.

Use Markdown Enhanced Preview to view the diagrams.
