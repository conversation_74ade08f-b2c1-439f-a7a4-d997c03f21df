# CGS Proximity Proximity Prompt UX System

## Overview

The **CGS Proximity Proximity Prompt UX** is a specialized component of the **Core Game Systems (CGS) Interaction Sub-system** that provides enhanced user experience for Roblox Proximity Prompts. This system replaces the default proximity prompt UI with a custom, animated interface that offers better visual feedback and improved player interaction.

## System Architecture

### CGS Subsystem Classification

- **Primary System**: Core Game Systems (CGS)
- **Subsystem**: Interaction
- **Component**: Proximity Prompt UX Enhancement

### Key Features

- ✨ **Animated Progress Bars**: Smooth fill animations during hold interactions
- 🎯 **Dynamic Key Display**: Automatic key binding visualization
- 🔄 **State Management**: Intelligent prompt lifecycle handling
- 🧹 **Memory Efficient**: Automatic cleanup and resource management
- 🏷️ **Tag-Based Activation**: Collection Service integration for easy setup and performance

## Technical Requirements

### Roblox Studio Setup

1. **Proximity Prompt Configuration**:
   - Style: `Enum.ProximityPromptStyle.Custom`
   - Tag: `"CustomPrompt"`

2. **StarterGui Hierarchy Structure**:

   ```zsh
   ProximityPrompt (BillboardGui)
   ├── ActionText (TextLabel)
   ├── CGS_ProximityPrompt_UX.luau (Script)
   └── KeyText (TextLabel)
       └── Progress (Frame)
   ```

### Services

- `CollectionService` - For tag-based prompt management
- `TweenService` - For smooth animations

## Implementation Guide

### Step 1: Create UI Structure

1. **Insert BillboardGui** into `StarterGui`
2. **Add ActionText**: Create a `TextLabel` child named "ActionText" and parent it to BillboardGui
3. **Add KeyText**: Create a `TextLabel` child named "KeyText" and parent it to BillboardGui
4. **Add Progress**: Create a `Frame` child under KeyText named "Progress" and parent it to KeyText

### Step 2: Configure Proximity Prompt

For any Proximity Prompt you want to use the custom UI & UX:

1. **Set Style to Custom**:

   ```lua
   proximityPrompt.Style = Enum.ProximityPromptStyle.Custom
   ```

2. **Add Tag**:
   - In Roblox Studio Properties panel
   - Navigate to "Tags" section
   - Click "+" to add new tag
   - Enter: `CustomPrompt`

### Step 3: Deploy the Script

Place the `CGS_ProximityPrompt_UX.luau` script with the BillboardGui as its parent. The system will automatically:

- Detect existing tagged prompts
- Monitor for newly tagged prompts
- Handle all UI management and animations

## Usage Examples

### Basic Setup

```lua
-- Create a proximity prompt
local proximityPrompt = Instance.new("ProximityPrompt")
proximityPrompt.ActionText = "Open Door"
proximityPrompt.KeyboardKeyCode = Enum.KeyCode.E
proximityPrompt.HoldDuration = 2
proximityPrompt.Style = Enum.ProximityPromptStyle.Custom
proximityPrompt.Parent = workspace.Door

-- Add the tag (can be done in Studio or via script)
local CollectionService = game:GetService("CollectionService")
CollectionService:AddTag(proximityPrompt, "CustomPrompt")
```

### Advanced Configuration

```lua
-- Multi-step interaction example
local complexPrompt = Instance.new("ProximityPrompt")
complexPrompt.ActionText = "Hack Terminal"
complexPrompt.KeyboardKeyCode = Enum.KeyCode.F
complexPrompt.HoldDuration = 5  -- Longer duration for complex actions
complexPrompt.MaxActivationDistance = 8
complexPrompt.Style = Enum.ProximityPromptStyle.Custom
complexPrompt.Parent = workspace.Terminal

-- The system will automatically handle the 5-second progress animation
CollectionService:AddTag(complexPrompt, "CustomPrompt")
```

## System Behavior

### Automatic Prompt Detection

The system uses CollectionService to monitor for Proximity Prompts with the "CustomPrompt" tag:

- **Initialization**: Processes all existing tagged prompts
- **Runtime**: Automatically handles newly tagged prompts
- **Cleanup**: Removes tracking when prompts are deleted

### Animation States

#### Prompt Shown

- BillboardGui appears over the prompt's parent object
- ActionText displays the prompt's ActionText
- KeyText shows the appropriate key binding

#### Hold Interaction

- Progress bar appears with a smooth fill animation
- Animation duration matches `ProximityPrompt.HoldDuration`
- Progress resets if player releases key early

#### Prompt Hidden

- All UI elements are hidden
- Any active animations are cancelled
- Resources are properly cleaned up

### Memory Management

- **Connection Tracking**: Prevents duplicate event connections
- **Automatic Cleanup**: Removes references when prompts are destroyed
- **Tween Management**: Cancels animations to prevent memory leaks

## Customization Options

### UI Styling

Modify the BillboardGui hierarchy to customize appearance:

- **ActionText**: Font, size, color, positioning
- **KeyText**: Key display styling
- **Progress**: Progress bar appearance, colors, effects

### Animation Tweaking

Adjust constants in the script:

```lua
-- Modify reset animation speed
local TWEEN_INFO_RESET = TweenInfo.new(0.15, Enum.EasingStyle.Quad, Enum.EasingDirection.In)

-- Customize progress animation in AnimateProgressPrompt function
TweenInfo.new(duration or 1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
```

### File Structure

```zsh
StarterGui/
├── BillboardGui
│   ├── CGS_ProximityPrompt_UX.luau (Script)
│   ├── ActionText (TextLabel)
│   └── KeyText (TextLabel)
│       └── Progress (Frame)
```

All proximity prompts are placed in Workspace.
