# TBRDS API Reference

Complete API reference for the Tag-Based Role Display System (TBRDS) Service-Oriented Architecture.

## Table of Contents

1. [Public API](#public-api)
2. [Type Definitions](#type-definitions)
3. [Event System](#event-system)
4. [Administrative Interface](#administrative-interface)
5. [Integration Patterns](#integration-patterns)

## Public API

The main TBRDS API provides a type-safe interface for external systems to interact with the tag system. All types are defined in `TBRDS_Types.luau`.

### Import

```lua
local TBRDSAPI = require(ReplicatedStorage.TBRDS.Shared.TBRDS_API)
```

### Role Management

#### `GetPlayerRole(player: Player): RoleName`

Gets the current role for a player. Returns the default role if not found/invalid.

```lua
local role = TBRDSAPI.GetPlayerRole(player)
print("Player role:", role)
```

#### `SetPlayerRole(player: Player, roleName: RoleName): ValidationResult`

Sets a player's role (server-side only, validated).

```lua
local result = TBRDSAPI.SetPlayerRole(player, "Developer")
if result.Success then
    print("Role assigned successfully:", result.Role)
else
    warn("Failed to assign role:", result.ErrorMessage)
end
```

#### `RefreshPlayerTag(player: Player): ValidationResult`

Forces a refresh of a player's tag, re-validating their role.

```lua
local result = TBRDSAPI.RefreshPlayerTag(player)
if result.Success then
    print("Tag refreshed, new role:", result.Role)
end
```

#### `IsRoleValid(player: Player, roleName: RoleName): boolean`

Checks if a player qualifies for a specific role.

```lua
if TBRDSAPI.IsRoleValid(player, "Developer") then
    print("Player qualifies for Developer role")
end
```

### Tag Data Management

#### `GetPlayerTagData(player: Player): PlayerTagData?`

Gets tag data for a player, or nil if invalid.

```lua
local tagData = TBRDSAPI.GetPlayerTagData(player)
if tagData then
    print("Role:", tagData.Role)
end
```

#### `GetPlayersWithRole(roleName: RoleName): { Player }`

Gets all players currently assigned to a specific role.

```lua
local developers = TBRDSAPI.GetPlayersWithRole("Developer")
print("Current developers:", #developers)
```

#### `GetRoleStatistics(): { [string]: number }`

Gets statistics about role distribution.

```lua
local stats = TBRDSAPI.GetRoleStatistics()
for role, count in pairs(stats) do
    print(string.format("%s: %d players", role, count))
end
```

### Role Information

#### `GetRoleStyle(roleName: RoleName): RoleStyle?`

Gets the visual style configuration for a role.

```lua
local style = TBRDSAPI.GetRoleStyle("Developer")
if style then
    print("Color:", style.Color)
end
```

### Event System

#### `SubscribeToTagChanges(callback: EventCallback): string`

Subscribes to tag change events.

```lua
local subscriptionId = TBRDSAPI.SubscribeToTagChanges(function(eventData)
    print(string.format("%s role changed from %s to %s",
        eventData.Player.Name,
        eventData.OldRole or "None",
        eventData.NewRole))
end)
```

#### `UnsubscribeFromTagChanges(subscriptionId: string): boolean`

Unsubscribes from tag change events.

```lua
local success = TBRDSAPI.UnsubscribeFromTagChanges(subscriptionId)
```

### System Information

#### `GetPerformanceMetrics(): PerformanceMetrics`

Gets current system performance metrics.

```lua
local metrics = TBRDSAPI.GetPerformanceMetrics()
print("Tag assignments:", metrics.TagAssignments)
```

#### `GetSystemHealth(): { [string]: any }`

Gets comprehensive system health information.

```lua
local health = TBRDSAPI.GetSystemHealth()
print("Services healthy:", health.servicesHealthy)
```

#### `SetDebugMode(enabled: boolean): ()`

Enables or disables debug mode for detailed logging.

```lua
TBRDSAPI.SetDebugMode(true)
```

### Internal/Advanced Functions

These are intended for internal use or advanced integrations:

#### `_UpdatePlayerCache(player: Player, tagData: PlayerTagData): ()`

Directly updates the player cache with new tag data.

#### `_RemovePlayerFromCache(player: Player): ()`

Removes a player from the cache (e.g., when they leave).

#### `_GetPlayerCache(): PlayerCache`

Returns the current player cache (mapping Player to PlayerTagData).

---

## Type Definitions

All types are defined in `TBRDS_Types.luau`.

```lua
-- Player role and validation
type RoleName = string
type ValidationResult = {
    Success: boolean,
    Role: RoleName?,
    ErrorCode: string?,
    ErrorMessage: string?,
    SecurityFlags: {string}?,
}

type PlayerTagData = {
    Role: RoleName,
    BillboardGUI: BillboardGui?,
    LastUpdated: number,
    ValidationCount: number,
    SecurityFlags: {string}?,
}

type RoleStyle = {
    Color: Color3,
    Font: Enum.Font,
    Image: string?,
    GetText: ((Player) -> string)?,
}

type EventCallback = (TagEventData) -> ()

type PlayerCache = { [Player]: PlayerTagData }

type PerformanceMetrics = {
    TagAssignments: number,
    ValidationTime: number,
    SecurityEvents: number,
    CacheHits: number,
    CacheMisses: number,
    ErrorCount: number,
    LastReset: number,
}
```

## Event System

The TBRDS event system enables loose coupling between components and external system integration.

### Event Types

- **TagChanged**: Fired when a player's role changes
- **TagAssigned**: Fired when a tag is initially assigned
- **TagRemoved**: Fired when a tag is removed
- **RoleValidated**: Fired when role validation occurs
- **SecurityViolation**: Fired when security issues are detected

### Event Data Structure

```lua
type TagEventData = {
    Player: Player,
    OldRole: RoleName?,
    NewRole: RoleName,
    Timestamp: number,
    Source: string,
    Metadata: {[string]: any}?,
}
```

### Usage Example

```lua
TBRDSAPI.SubscribeToTagChanges(function(eventData)
    if eventData.NewRole == "Developer" then
        DeveloperSystem.GrantAccess(eventData.Player)
    end
end)
```

## Administrative Interface

### Chat Commands

Available to administrators (rank 252+):

```zsh
/tbrds status    -- System health report
/tbrds restart   -- Emergency system restart
/tbrds metrics   -- Performance metrics
/tbrds refresh   -- Refresh all player tags
```

### Programmatic Access

```lua
local TBRDSSystem = _G.TBRDSSystem
local healthReport = TBRDSSystem.GetSystemHealth()
print(healthReport)
local isHealthy = TBRDSystem.IsSystemHealthy()
local success = TBRDSSystem.RefreshPlayerTag(player)
```

## Integration Patterns

### Basic Integration

```lua
local role = TBRDSAPI.GetPlayerRole(player)
if role == "Developer" then
    -- Enable developer features
end
```

### Event-Driven Integration

```lua
TBRDSAPI.SubscribeToTagChanges(function(eventData)
    YourSystem.UpdatePlayerPermissions(eventData.Player, eventData.NewRole)
end)
```

### Error Handling

```lua
local result = TBRDSAPI.SetPlayerRole(player, "Developer")
if not result.Success then
    warn("Failed to set role:", result.ErrorMessage)
end
```
