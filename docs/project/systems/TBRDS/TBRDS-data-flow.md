# TBRDS Data Flow Diagrams

This document provides comprehensive data flow diagrams for the Tag-Based Role Display System (TBRDS), illustrating how data flows through each subsystem and component.

## 1. Overall TBRDS System Architecture

```mermaid
flowchart TB
    subgraph "Client Side (src/client/TBRDS)"
        CI[TBRDS_Client_Initialization.luau]
        CR[Tag Renderer]
        CE[Event Listener]
        CC[Validation Cache]
    end

    subgraph "Server Side (src/server/TBRDS)"
        SI[TBRDS_Server_Initialization.luau]
        RH[TBRDS_RoleHandler.luau]
        SM[TBRDS_ServiceManager.luau]
        TS[TBRDS_TagService.luau]
        RS[TBRDS_RoleService.luau]
        BS[TBRDS_BillboardService.luau]
        CS[TBRDS_ConfigurationService.luau]
    end

    subgraph "Shared (src/shared/TBRDS)"
        CONF[TBRDS_Configuration.luau]
        TYPES[TBRDS_Types.luau]
        UTILS[TBRDS_Utils.luau]
        PERF[TBRDS_PerformanceMonitor.luau]
        REM[TBRDS_Remotes.luau]
        EVENTS[TBRDS_EventSystem.luau]
        API[TBRDS_API.luau]
    end

    subgraph "External Systems"
        DS[DataStore Service]
        MS[MarketplaceService]
        GS[Group Service]
        PL[Players Service]
        RS[ReplicatedStorage]
    end

    subgraph "Integration Points"
        MAFS[MAFS System]
        MCS[MCS System]
        CGS[CGS System]
        OTHER[Other Systems]
    end

    CI --> REM
    CR --> CC
    CE --> EVENTS

    SI --> SM
    SM --> TS
    SM --> RS
    SM --> BS
    SM --> CS
    RH --> RS

    REM --> EVENTS
    EVENTS --> API
    API --> PERF

    TS --> DS
    RS --> MS
    RS --> GS
    SM --> PL

    API --> MAFS
    API --> MCS
    API --> CGS
    API --> OTHER
```

## 2. Role Validation and Assignment Flow

```mermaid
flowchart TB
    PJ[Player Joins] --> IV[Initial Validation]
    IV --> GP[Get Player Data]
    GP --> RH[Check Role Handlers]
    RH --> VP[Validate Permissions]
    VP --> SC[Security Check]
    SC --> RL[Rate Limit Check]
    RL --> PP[Priority Processing]
    PP --> GR[Group Rank Check]
    GR --> GP2[GamePass Check]
    GP2 --> UR[User Role Default]
    UR --> RF[Role Found]
    RF --> RA[Role Assignment]
    RA --> ST[Style Lookup]
    ST --> BB[Billboard Creation]
    BB --> REM[Remote Event Fire]
    REM --> CC[Client Cache Update]
    CC --> EVENTS[Event Fire]
    EVENTS --> TC[Tag Creation]
```

## 3. Security and Anti-Exploit System

```mermaid
flowchart TB
    IR[Incoming Request] --> PV[Player Validation]
    PV --> RV[Role Validation]
    RV --> SV[Security Validation]
    SV --> RL[Rate Limiting]
    PV --> UV[User ID Validation]
    UV --> CV[Character Validation]
    CV --> POS[Position Validation]
    POS --> RC[Role Change Tracking]
    RC --> SA[Suspicious Activity Detection]
    RL --> RT[Request Throttling]
    SV --> IP[Input Sanitization]
    IP --> AV[Authority Validation]
    AV --> DS[DataStore Verification]
    DS --> EM[Exploit Monitoring]
    EM --> AL[Allow Request]
    EM --> DR[Deny Request]
    DR --> LG[Log Security Event]
    LG --> BL[Blacklist User]
    BL --> AL2[Alert Administrators]
```

## 4. Event System and Integration Flow

```mermaid
flowchart TB
    TC[Tag Changed] --> EVENTS[Event System]
    TA[Tag Assigned] --> EVENTS
    TR[Tag Removed] --> EVENTS
    RV[Role Validated] --> EVENTS
    SV[Security Violation] --> EVENTS
    EVENTS --> VE[Validate Event]
    VE --> EH[Event History]
    EH --> SN[Subscriber Notification]
    SN --> EM[Error Management]
    SN --> MAFS[MAFS Integration]
    SN --> MCS[MCS Integration]
    SN --> CGS[CGS Integration]
    SN --> CUSTOM[Custom Systems]
    SN --> API[Public API]
    MAFS --> AUDIO[Audio System]
    MCS --> VISUAL[Visual Effects]
    CGS --> PERMS[Permission System]
    API --> LOGS[Logging System]
    API --> METRICS[Metrics Collection]
```

## 5. Performance Monitoring and Optimization

```mermaid
flowchart TB
    TA[Tag Assignments] --> MT[Metrics Tracking]
    VT[Validation Time] --> MT
    SE[Security Events] --> MT
    CH[Cache Hits/Misses] --> MT
    EC[Error Count] --> MT
    MT --> TH[Threshold Monitoring]
    TH --> PA[Performance Analysis]
    PA --> IH[Issue Detection]
    IH --> RP[Report Generation]
    IH --> CC[Cache Optimization]
    IH --> VE[Validation Efficiency]
    IH --> SC[Security Calibration]
    IH --> CF[Configuration Tuning]
    IH --> AL[Alert Generation]
    RP --> DB[Debug Logs]
    RP --> PR[Performance Reports]
    RP --> HM[Health Metrics]
    AL --> WA[Warning Alerts]
    AL --> ER[Error Reports]
```

## 6. Client-Server Communication Flow

```mermaid
flowchart TB
    CR[Client Request] --> TR[Tag Request Remote]
    TR --> REQ[Request Data]
    REQ --> SH[Server Handler]
    SH --> SV[Server Validation]
    SV --> VAL[Validation Data]
    VAL --> SM[Server Manager]
    SM --> TAG[Tag Data]
    TAG --> TU[Tag Update Remote]
    TU --> CL[Client Listener]
    CL --> CC[Client Cache]
    CC --> CR2[Client Renderer]
    CR2 --> CE[Client Events]
    SS[Server Security] --> SEC[Security Data]
    SEC --> SR[Security Report Remote]
    SR --> CE
    SV --> ERR[Error Data]
    ERR --> TU
```

## Summary

These data flow diagrams illustrate the complete architecture of the TBRDS system:

1. **Overall Architecture** - Shows the high-level interaction between client, server, and shared components
2. **Role Validation** - Details the role assignment and validation pipeline
3. **Security System** - Illustrates the comprehensive anti-exploit and security measures
4. **Event System** - Shows how events enable system integration and reactions
5. **Performance Monitoring** - Details the metrics collection and optimization system
6. **Client-Server Communication** - Shows the remote event communication layer

The TBRDS system is designed with:

- **Modular Architecture**: Clear separation of concerns across components
- **Security First**: Comprehensive validation and anti-exploit measures
- **Event-Driven Design**: Enables integration with other systems
- **Performance Monitoring**: Built-in metrics and optimization
- **Type Safety**: Strict typing throughout the system
- **Scalability**: Designed to handle large numbers of players efficiently

Use Markdown Enhanced Preview to view the diagrams.
