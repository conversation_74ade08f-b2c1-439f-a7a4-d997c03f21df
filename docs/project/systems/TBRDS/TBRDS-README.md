# TBRDS - Tag-Based Role Display System

A comprehensive, high-performance role management and display system for Site-112 built on Service-Oriented Architecture (SOA) with advanced security features, performance monitoring, and seamless integration capabilities.

## 🎯 Features

### Core Features

- **Service-Oriented Architecture**: Modular design with specialized services
- **Automatic Role Detection**: Determines player roles based on group rank and gamepass ownership
- **Visual Tag Display**: Shows role tags above players with custom styling and effects
- **Priority System**: Handles multiple role qualifications with clear priority hierarchy
- **Performance Optimized**: Intelligent caching, monitoring, and optimization systems
- **Security Focused**: Comprehensive rate limiting and anti-exploit measures
- **Event-Driven**: Real-time integration with other game systems

### Advanced Features

- **Service Coordination**: ServiceManager orchestrates all system components
- **Health Monitoring**: Automatic service health checks and recovery
- **Administrative Tools**: Built-in commands for system management
- **Integration APIs**: Clean interfaces for external system integration
- **Performance Analytics**: Comprehensive metrics and optimization
- **Configuration Management**: Hot-reload configuration with validation

## 🏗️ Architecture

TBRDS follows a Service-Oriented Architecture (SOA):

```mermaid
flowchart TB
    subgraph "Service Manager (src/server/TBRDS/Services)"
        SM[ServiceManager.luau]
        SM --> CS[ConfigurationService.luau]
        SM --> RS[RoleService.luau]
        SM --> BS[BillboardService.luau]
        SM --> TS[TagService.luau]
    end

    subgraph "External Integration"
        API[Public API (src/shared/TBRDS/Shared/API.luau)]
        MAFS[MAFS System]
        MCS[MCS System]
        CGS[CGS System]
    end

    subgraph "Shared Infrastructure"
        CONF[Configurations/Systems/TBRDS_Configuration.luau]
        EVENTS[EventSystem.luau]
        PERF[PerformanceMonitor.luau]
        REMOTES[Remotes.luau]
    end

    TS --> RS
    TS --> BS
    TS --> CS
    API --> SM
    MAFS --> API
    MCS --> API
    CGS --> API
```

## 🚀 Quick Start

### 1. System Initialization

The TBRDS system initializes automatically when the server starts using the ServiceManager:

```lua
-- The system starts automatically via TBRDS_ServerManager.init.server.luau
-- No manual setup required!
```

### 2. Basic API Usage

```lua
local TBRDSAPI = require(ReplicatedStorage.TBRDS.Shared.API)

-- Get a player's current role
local role = TBRDSAPI.GetPlayerRole(player)
print("Player role:", role)

-- Check if a player qualifies for a specific role
local isValid = TBRDSAPI.IsRoleValid(player, "Developer")

-- Get role style information
local style = TBRDSAPI.GetRoleStyle("Developer")
```

### 3. System Integration

```lua
-- Subscribe to tag change events
local subscriptionId = TBRDSAPI.SubscribeToTagChanges(function(eventData)
    print(string.format("%s role changed from %s to %s",
        eventData.Player.Name,
        eventData.OldRole or "None",
        eventData.NewRole))
end)

-- Get system health
local health = TBRDSAPI.GetSystemHealth()
print("System healthy:", health.servicesHealthy)
```

## 📚 Available Roles

### Administrative Roles

- **BleckWolf25**: Founder (highest priority)
- **Anonmancer**: Co-Founder
- **Senior Moderator**: High-level moderation privileges
- **Game Moderator**: Standard moderation privileges
- **Junior Moderator**: Basic moderation privileges

### Development & Support Roles

- **Developer**: Development team members
- **Investors**: Project investors
- **Trusted**: Trusted community members
- **Supporter**: GamePass supporters
- **User**: Default role for all players

## 🔧 API Reference

### Role Management

```lua
-- Get player's current role
TBRDSAPI.GetPlayerRole(player) -> string

-- Set player's role (server-side only)
TBRDSAPI.SetPlayerRole(player, roleName) -> ValidationResult

-- Refresh player's tag
TBRDSAPI.RefreshPlayerTag(player) -> ValidationResult

-- Check if role is valid for player
TBRDSAPI.IsRoleValid(player, roleName) -> boolean

-- Get role style information
TBRDSAPI.GetRoleStyle(roleName) -> RoleStyle?
```

### Tag Data Management

```lua
-- Get player tag data
TBRDSAPI.GetPlayerTagData(player) -> PlayerTagData?

-- Get players with specific role
TBRDSAPI.GetPlayersWithRole(roleName) -> {Player}

-- Get role statistics
TBRDSAPI.GetRoleStatistics() -> {[string]: number}
```

### Event System

```lua
-- Subscribe to tag changes
TBRDSAPI.SubscribeToTagChanges(callback) -> string

-- Unsubscribe from tag changes
TBRDSAPI.UnsubscribeFromTagChanges(subscriptionId) -> boolean
```

### System Information

```lua
-- Get performance metrics
TBRDSAPI.GetPerformanceMetrics() -> PerformanceMetrics

-- Get system health
TBRDSAPI.GetSystemHealth() -> {[string]: any}

-- Set debug mode
TBRDSAPI.SetDebugMode(enabled) -> ()
```

## 🎮 Integration Examples

### MAFS Integration

```lua
-- React to role changes for different footstep sounds
TBRDSAPI.SubscribeToTagChanges(function(eventData)
    local player = eventData.Player
    local role = eventData.NewRole

    if role == "Developer" then
        -- Use special developer footstep sounds
        MAFS.SetPlayerFootstepProfile(player, "developer")
    elseif role == "BleckWolf25" or role == "Anonmancer" then
        -- Use founder footstep sounds
        MAFS.SetPlayerFootstepProfile(player, "founder")
    else
        -- Use default footstep sounds
        MAFS.SetPlayerFootstepProfile(player, "default")
    end
end)
```

### Permission System Integration

```lua
-- Update player permissions based on role changes
TBRDSAPI.SubscribeToTagChanges(function(eventData)
    local player = eventData.Player
    local role = eventData.NewRole

    -- Update command permissions
    if string.find(role, "Moderator") then
        PermissionSystem.GrantModeratorCommands(player)
    elseif role == "Developer" then
        PermissionSystem.GrantDeveloperCommands(player)
    else
        PermissionSystem.GrantBasicCommands(player)
    end
end)
```

### Custom System Integration

```lua
-- Example: Special abilities based on role
local function setupRoleBasedAbilities()
    TBRDSAPI.SubscribeToTagChanges(function(eventData)
        local player = eventData.Player
        local role = eventData.NewRole

        if role == "Developer" then
            -- Grant developer tools
            DeveloperTools.GrantAccess(player)
        elseif role == "Supporter" then
            -- Grant supporter benefits
            SupporterBenefits.GrantBenefits(player)
        end
    end)
end
```

## ⚙️ Configuration

### System Settings

Located in `src/shared/Configurations/Systems/TBRDS_Configuration.luau`:

```lua
Settings = {
    DebugMode = false,
    EnablePerformanceMetrics = true,
    EnableEventSystem = true,

    RateLimit = {
        Window = 60, -- seconds
        MaxRequests = 5, -- requests per window
    },

    MaxTagLength = 50,
    MaxDisplayNameLength = 100,
    TagValidationInterval = 60,
    GroupRankCheckInterval = 30,
    MaxCachedPlayers = 200,

    BillboardSettings = {
        MaxDistance = 15,
        StudsOffset = Vector3.new(0, 3.5, 0),
        Size = UDim2.new(8, 0, 2, 0),
        AlwaysOnTop = true,
        LightInfluence = 1,
    },
}
```

### Role Priority Configuration

```lua
RolePriority = {
    "BleckWolf25", -- Founder (highest priority)
    "Anonmancer", -- Co-Founder
    "Senior Moderator",
    "Game Moderator",
    "Junior Moderator",
    "Developer",
    "Investors",
    "Trusted",
    "Supporter",
    "User", -- Default role (lowest priority)
}
```

### Adding Custom Role Handlers

```lua
-- In TBRDS_RoleHandlers.init.luau
["YourCustomRole"] = {
    Check = function(player)
        -- Your custom role validation logic
        return player:GetRankInGroup(groupId) >= 100
    end,
    Style = {
        Color = Color3.fromRGB(255, 0, 0),
        Font = Enum.Font.GothamBold,
        Image = "rbxassetid://your_icon_id",
        GetText = function(player)
            return "[Custom Role]"
        end,
    },
}
```

## 🔒 Security Features

- **Service Isolation**: Services operate independently with controlled communication
- **Rate Limiting**: Comprehensive rate limiting with security event tracking
- **Input Validation**: All player data and role assignments validated
- **Anti-Exploit Measures**: Protection against role spoofing and manipulation
- **Security Monitoring**: Real-time security event tracking and alerting
- **Access Control**: Administrative commands restricted to authorized users
- **Validation Pipeline**: Multi-layer validation for all role assignments

## 📊 Performance

- **Intelligent Caching**: Role cache with 5-minute TTL and smart invalidation
- **Service Optimization**: Each service optimized for its specific responsibilities
- **Performance Monitoring**: Comprehensive metrics collection and analysis
- **Health Monitoring**: Automatic service health checks and recovery
- **Memory Management**: Efficient billboard and cache management
- **Event System**: Optimized event processing and subscription management
- **Configuration Caching**: Hot-reload configuration with minimal performance impact

## 🛠️ Administrative Tools

Available to administrators (rank 252+):

### Chat Commands

```zsh
/tbrds status    -- System health report
/tbrds restart   -- Emergency system restart
/tbrds metrics   -- Performance metrics
/tbrds refresh   -- Refresh all player tags
```

### Programmatic Access

```lua
-- Access the system directly (server-side)
local TBRDSSystem = _G.TBRDSSystem

-- Get system health
local health = TBRDSSystem.GetSystemHealth()

-- Check if system is healthy
local isHealthy = TBRDSSystem.IsSystemHealthy()

-- Refresh a specific player's tag
local success = TBRDSSystem.RefreshPlayerTag(player)
```

## 🐛 Debugging

Enable debug mode for detailed logging:

```lua
TBRDSAPI.SetDebugMode(true)
```

Debug output includes:

- Service initialization and health
- Role assignment and validation
- Tag creation and updates
- Performance metrics and issues
- Security events and violations
- Configuration changes

## 📁 File Structure

```zsh
src/
├── client/TBRDS/
│   └── TBRDS_Client_Initialization.luau      # Client-side tag display
├── server/TBRDS/
│   ├── TBRDS_Server_Initialization.luau      # Main server initialization
│   └── TBRDS_RoleHandler.luau                # Role handler definitions
├── serverStorage/TBRDS/
│   └── TBRDSSystem.luau                      # Server storage system management
└── shared/
    ├── Configurations/Systems/
    │   └── TBRDS_Configuration.luau           # System Module-based configuration
    └── TBRDS/
        ├── TBRDS_Remotes.luau                # Remotes Management
        ├── TBRDS_Types.luau                  # Type definitions
        └── Shared/
            ├── TBRDS_API.luau                # Public TBRDS API
            ├── TBRDS_EventSystem.luau        # Event System Architecture
            ├── TBRDS_PerformanceMonitor.luau # Performance tracking
            └── TBRDS_Utils.luau              # Utility functions
        └── Services/
            ├── TBRDS_BillboardService.luau   # Billboard management
            ├── TBRDS_ConfigurationService.luau # Configuration management
            ├── TBRDS_RoleService.luau        # Role management
            ├── TBRDS_ServiceManager.luau     # Service management
            └── TBRDS_TagService.luau         # Tag management
```

## 🤝 Contributing

When extending TBRDS:

1. **Follow SOA Patterns**: Use the Service-Oriented Architecture principles
2. **Service Isolation**: Keep services focused on single responsibilities
3. **Type Safety**: Use strict typing throughout your code
4. **Event Integration**: Use the event system for loose coupling
5. **Performance Monitoring**: Include performance tracking in new features
6. **Security First**: Validate all inputs and implement proper security measures
7. **Documentation**: Add comprehensive documentation and examples
8. **Testing**: Test with multiple players and edge cases

### Adding New Services

1. Create service in `src/server/TBRDS/Services/`
2. Follow existing service patterns (Initialize, GetServiceStatus, Cleanup)
3. Add to ServiceManager initialization order if needed
4. Update API if external access is required
5. Add comprehensive documentation

### Adding New Roles

1. Add role to `RolePriority` in configuration
2. Implement role handler in `TBRDS_RoleHandlers.init.luau`
3. Define role style with colors, fonts, and effects
4. Test role validation logic thoroughly
5. Update documentation

---

## 📖 Additional Documentation

- **[SOA Architecture Guide](TBRDS-SOA-Architecture.md)**: Detailed service architecture
- **[Data Flow Diagrams](TBRDS-data-flow.md)**: System data flow visualization

*For technical support and questions, contact the development team or refer to the comprehensive documentation in the `docs/` directory.*
