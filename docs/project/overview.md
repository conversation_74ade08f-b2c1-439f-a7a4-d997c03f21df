# SCP: Site-112 Overview

This document provides an overview of the SCP: Site-112 project, including its purpose, key features, and technical details.

## Purpose

The purpose of the SCP: Site-112 project is to [briefly describe the project's goal and objectives].

## Key Features

- **Feature 1**: [Brief description of feature 1]
- **Feature 2**: [Brief description of feature 2]
- **Feature 3**: [Brief description of feature 3]

## Technical Details

### Programming Languages

- **Primary Language**: LuaU (Roblox's variant of Lua)
- **Secondary Languages**: Typescript (for type definitions, IDE support, documentation and type-safety)

### Frameworks and Libraries

- No frameworks or libraries are used in this project, as it is built directly on Roblox's platform.

### Database

- **Database**: Roblox DataStore
- **ORM**: Roblox's built-in DataStore API

### Deployment

- **Cloud Provider**: Roblox Cloud Infrastructure
- **Serverless**: N/A (Roblox does not use serverless architectures)
- **Containerization**: N/A (Roblox does not use containerization)
- **Version Control**: Git (GitHub)
- **CI/CD**: GitHub Actions

### Security

- **Authentication**: Roblox's built-in authentication
- **Authorization**: Role-based access control (RBAC)
- **Encryption**: None, as Roblox does not support custom encryption methods
- **Security Testing**: Manual code reviews and testing

### Monitoring and Logging

- **Monitoring**: Each system has its own monitoring setup using Roblox's built-in tools.
- **Logging**: Each system has its own logging setup using Roblox's built-in tools.
- **Error Tracking**: Each system in the future will send error reports to a centralized logging system (D.I.S Dashboard)

### Documentation

- **API Documentation**: TypeScript definitions and inline LuaU comments
- **Code Documentation**: Check [docs/project/systems](../systems/) for detailed system documentation

### Testing

- **Unit Testing**: None, as Roblox does not support unit testing frameworks directly.
- **Integration Testing**: None, as Roblox does not support integration testing frameworks directly.
- **End-to-End Testing**: None, as Roblox does not support end-to-end testing frameworks directly.

### Continuous Integration and Deployment

- **CI/CD Pipeline**: GitHub Actions
- **Automated Testing**: No automated testing is used in the CI/CD pipeline
- **Automated Deployment**: No automated deployment is used in the CI/CD pipeline
- **Rollback Mechanism**: No rollback mechanism is in place for failed deployments
- **Monitoring**: Monitoring is used to detect and respond to issues in production

## Conclusion

## Game Overview

**SCP: Site-112** immerses players in the aftermath of a containment breach at a secret SCP Foundation facility. The game features two primary modes, blending narrative depth with replayable tension:

### Campaign Mode

- Experience the fall of Site-112 from multiple perspectives: MTF operatives, Class-D personnel, and Foundation scientists.
- Player choices lead to branching storylines and varied outcomes (K.I.A., M.I.A., or survival).
- Non-linear narrative delivered through mission logs, corrupted audio, and environmental storytelling.

### Multiplayer Mode

- Asymmetric matches with teams pursuing conflicting objectives (e.g., MTF, Class-D, SCPs).
- Each session is shaped by unpredictable SCP anomalies and procedural hazards, ensuring unique rounds.
- Multiplayer matches contribute to a persistent world state, reflecting Campaign developments.

### Unique & Innovative Features

- **Adaptive SCP Anomalies**: SCPs react dynamically to player actions, creating emergent threats.
- **Team-Based Asymmetry**: Distinct roles with unique tools, abilities, and objectives.
- **Procedural Facility Layout**: Each match generates a new Site-112 layout, with shifting rooms, hazards, and SCP spawns. Environmental decay escalates tension during gameplay.
- **Reactive Sanity System**: Exposure to SCPs, darkness, or traumatic events affects player sanity, causing hallucinations, audio distortions, and misleading UI feedback.
- **Persistent Campaign Consequences**: Choices in Campaign Mode have lasting effects on Site-112, influencing future missions and unlocking unique content.

### Game Design Document Overview

The full [Game Design Document](https://docs.google.com/document/d/1ZH41a032wXsJhvArWzL_XmswiAc2uNRj7ayRnNQD7i8/edit?usp=sharing) covers:

- Core Gameplay Mechanics
- Environment Design
- Story & Narrative
- Integration of campaign and multiplayer progression

## Contact Information

For more information, please contact <<EMAIL>>(mailto:<EMAIL>).
