# Ragdoll Mechanic

## Overview

The Ragdoll mechanic provides a semi-realistic physics-based ragdoll effects for character models in Site-112. It is designed to be efficient, streaming-aware, and network-optimized, with both server and client logic for smooth gameplay and minimal server load.

## Features

- **Physics-based ragdolling** for R6 (with fallback safe-checking if R15 is detected)
- **Server-side detection** of character death and event broadcasting
- **Client-side simulation** for performance and smoothness
- **Distance culling** and streaming support
- **Configurable limits** for max ragdolls and lifetime
- **Debug logging** and utility functions

## File Structure

- `Ragdoll_Client.luau`: Client logic for enabling/disabling ragdolls and managing cleanup
- `Ragdoll_Server.luau`: Server logic for detecting deaths and firing ragdoll events
- `Ragdoll_Utils.luau`: Shared utility functions for ragdoll operations
- `Ragdoll_Types.luau`: Strict type definitions for ragdoll state/config
- `Ragdoll_Remotes.luau`: RemoteEvent management for client-server communication

## How It Works

1. **Server** detects when a player's character dies.
2. **Server** fires a `RagdollEvent` to nearby clients (distance-limited for performance).
3. **Client** receives the event, checks streaming/culling, and enables ragdoll physics on the character.
4. Ragdoll state is managed and cleaned up after a configurable time or when limits are exceeded.

## Configuration

Settings are defined in `Ragdoll_Configuration` and include:

- `Debug`: Enable/disable debug logs
- `MaxRagdolls`: Maximum number of active ragdolls per client
- `MaxRagdollLifetime`: Time before ragdolls are cleaned up
- `PhysicsForce`, `CollisionEnabled`, `MaxBroadcastDistance`: Physics and replication settings

## Usage

- **To enable ragdoll:** Triggered automatically on character death via server event
- **To disable/cleanup:** Managed automatically by client logic
- **Manual control:** Utility functions in `Ragdoll_Utils.luau` can be used for custom ragdolling

## Extending

- Add new ragdoll types in `Ragdoll_Types.luau`
- Add new constraints in `Ragdoll_Utils.luau`
- Adjust configuration for different gameplay needs
- Integrate with other mechanics by firing or listening to `RagdollEvent`

## Troubleshooting

- Ensure all required modules are in `ReplicatedStorage` and accessible
- Check debug logs by enabling `Debug` in the configuration
- For type or require errors, verify module paths and type definitions

## Credits

- **Author:** BleckWolf25
- **Contributors:** Silver
- **Copyright:** Dynamic Innovative Studio

---
For more details, see the code comments in each file and the main project documentation.
