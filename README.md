# 🏢 SCP: Site-112

[![License](https://img.shields.io/badge/License-Commercial-red.svg)](LICENSE)
[![Roblox](https://img.shields.io/badge/Platform-Roblox-00A2FF.svg)](https://www.roblox.com)
[![Luau](https://img.shields.io/badge/Language-Luau-00A2FF.svg)](https://luau-lang.org/)
[![Studio Script Sync](https://img.shields.io/badge/Sync-Studio%20Script%20Sync-green.svg)](https://create.roblox.com/docs/studio/script-sync)

> **A professional SCP Foundation multiplayer experience built with modern Roblox development practices**

Welcome to **SCP: Site-112**, an immersive multiplayer Roblox game set in the SCP Foundation universe. This repository contains the complete source code, built with a modular architecture and professional development workflows.

---

## 🎮 About the Game

**SCP: Site-112** is a multiplayer roleplay experience where players take on various roles within a secure SCP Foundation facility. The game features:

- **Immersive SCP Foundation Setting**: Authentic atmosphere and lore
- **Role-Based Gameplay**: Multiple clearance levels and specialized roles
- **Advanced Audio Systems**: Dynamic footstep sounds and environmental audio
- **Observation Mechanics**: Document reading, audio logs, and security systems
- **Command System**: Comprehensive admin and moderation tools
- **Tag-Based Role Display**: Visual role identification system

---

## 🏗️ Architecture & Systems

### Core Systems

| System | Description | Status |
|--------|-------------|--------|
| **MAFS** | Modular Audio FootStep System - Advanced material-based footstep audio | ✅ Active |
| **TBRDS** | Tag-Based Role Display System - Visual role identification | ✅ Active |
| **CGS** | Core Game System - Observation and interaction mechanics | ✅ Active |
| **MCS** | Modular Command System - Admin and moderation commands | ✅ Active |

### System Details

#### 🔊 MAFS (Modular Audio FootStep System)

- **Purpose**: High-performance footstep audio with material detection
- **Features**: Client-server architecture, sound pooling, security validation
- **Materials**: Grass, Sand, Concrete, Wood, Metal, Rock, Water + Custom materials
- **Documentation**: [`docs/project/systems/MAFS/`](docs/project/systems/MAFS/)

#### 🏷️ TBRDS (Tag-Based Role Display System)

- **Purpose**: Visual role identification and management
- **Features**: Server-side tag assignment, group integration, persistent storage
- **Roles**: Foundation personnel, researchers, security, D-Class, etc.

#### CGS (Core Game System)

- **Purpose**: Observation mechanics and document interaction
- **Features**: Document reading, audio logs, FOV manipulation, clearance levels
- **Security**: Clearance-based access control

#### ⚡ MCS (Modular Command System)

- **Purpose**: Comprehensive admin and moderation tools
- **Features**: Permission system, middleware, autocomplete, rate limiting
- **Commands**: Moderation, system management, debugging tools

---

## 🚀 Quick Start

### Prerequisites

- **Roblox Studio** with Script Sync enabled
- **Visual Studio Code** (recommended)
- **Git** for version control

### Setup Development Environment

1. **Clone the repository**

   ```bash
   git clone https://github.com/Dynamic-Innovative-Studio/Site-112.git
   cd Site-112
   ```

2. **Run the setup script**

   ```bash
   ./scripts/setup-dev.sh
   ```

3. **Enable Studio Script Sync**
   - Open Roblox Studio
   - Go to File → Beta Features
   - Enable "Script Sync"
   - Open your place file

4. **Open in VSCode**

   ```bash
   code site-112-workspace.code-workspace
   ```

### Development Commands

```bash
# Linting and formatting
npm run lint              # Run linting
npm run lint:fix          # Auto-fix formatting issues
npm run format            # Format code with StyLua

# Testing
npm test                  # Run all checks
npm run validate          # Validate project structure

# Development tools
npm run setup             # Run setup script
npm run tools:install     # Install development tools
```

---

## 📁 Project Structure

```zsh
site-112-centralized-repo/
├── 📁 .vscode/                    # VSCode configuration
├── 📁 docs/                       # Documentation
│   ├── 📁 company/               # Organization info
│   ├── 📁 development/           # Development guides
│   ├── 📁 policies/              # Project policies
│   └── 📁 project/               # Project documentation
├── 📁 scripts/                    # Development scripts
│   ├── 🔧 setup-dev.sh          # Environment setup
│   ├── 🔍 lint.sh               # Code quality checks
│   └── 📋 pre-commit.sh         # Git hooks
├── 📁 src/                        # Source code
│   ├── 📁 client/                # Client-side scripts
│   │   ├── 📁 CGS/              # Core Game System
│   │   ├── 📁 MAFS/             # Audio FootStep System
│   │   ├── 📁 MCS/              # Command System UI
│   │   └── 📁 TBRDS/            # Role Display System
│   ├── 📁 server/                # Server-side scripts
│   │   ├── 📁 CGS/              # Observation System
│   │   ├── 📁 MAFS/             # Audio Management
│   │   ├── 📁 MCS/              # Command Processing
│   │   └── 📁 TBRDS/            # Role Management
│   ├── 📁 shared/                # Shared modules
│   │   ├── 📁 Configurations/   # System configurations
│   │   ├── 📁 MAFS/             # Audio system shared
│   │   ├── 📁 MCS/              # Command system shared
│   │   └── 📁 TBRDS/            # Role system shared
│   └── 📁 gui/                   # UI components
├── 📁 tests/                       # Tests files
├── ⚙️ aftman.toml                # Tool management
├── ⚙️ stylua.toml               # Code formatting config
├── ⚙️ selene.toml               # Linting configuration
├── ⚙️ .luaurc                   # Luau language config
└── 📋 package.json              # Project metadata & scripts
```

---

## 🛠️ Development Workflow

### Code Quality

This project uses professional-grade code quality tools:

- **Selene**: Luau linting for code quality and best practices
- **StyLua**: Automatic code formatting for consistency
- **Luau LSP**: Type checking and IntelliSense
- **Git Hooks**: Pre-commit validation

### Commit Standards

We follow [Conventional Commits](https://www.conventionalcommits.org/):

```bash
feat(mafs): add new material support
fix(tbrds): resolve tag display issue
docs(readme): update setup instructions
```

**Scopes**: `mafs`, `tbrds`, `cgs`, `mcs`, `client`, `server`, `shared`, `config`, `docs`, `ci`

### Testing

```bash
# Run all quality checks
npm test

# Generate detailed lint report
npm run lint:report

# Validate project structure
npm run validate
```

---

## 📚 Documentation

### Quick Links

- **[Development Setup Guide](docs/development/VSCODE_SETUP_GUIDE.md)** - Complete VSCode setup
- **[MAFS System Documentation](docs/project/systems/MAFS/)** - Audio system details
- **[Linting & CI/CD Guide](docs/development/LINTING_AND_CICD_GUIDE.md)** - Code quality setup
- **[Organization Info](docs/company/org_README.md)** - About Dynamic Innovative Studio

### System Documentation

| System | Documentation | API Reference |
|--------|---------------|---------------|
| MAFS | [MAFS README](docs/project/systems/MAFS/MAFS-README.md) | `src/shared/MAFS/Shared/API.luau` |
| TBRDS | Coming Soon | `src/shared/TBRDS/` |
| CGS | Coming Soon | `src/client/CGS/` |
| MCS | Coming Soon | `src/shared/MCS/` |

---

## 🤝 Contributing

### For Team Members

1. **Create a feature branch**

   ```bash
   git checkout -b feat/your-feature-name
   ```

2. **Make your changes**
   - Follow the existing code style
   - Add tests for new features
   - Update documentation as needed

3. **Run quality checks**

   ```bash
   npm test
   npm run lint:fix
   ```

4. **Commit with conventional format**

   ```bash
   git commit -m "feat(system): add new feature"
   ```

5. **Push and create PR**

   ```bash
   git push origin feat/your-feature-name
   ```

### Code Style Guidelines

- **Indentation**: 2 spaces
- **Line Length**: 100 characters max
- **Naming**: PascalCase for modules, camelCase for variables
- **Comments**: Use `--[[]]` for multi-line, `--` for single-line
- **Types**: Use Luau type annotations where helpful

---

## 🔗 External Links

### Organization

- 🌐 **[Dynamic Innovative Studio Website](https://dynamic-innovative-studio.web.app)**
- 💬 **[Community Discord](https://discord.gg/nGEnj6abUs)**
- 📧 **Contact**: <<EMAIL>>

### Resources

- 📖 **[Roblox Creator Documentation](https://create.roblox.com/docs)**
- 🔧 **[Studio Script Sync Guide](https://create.roblox.com/docs/studio/script-sync)**
- 📝 **[Luau Language Guide](https://luau-lang.org/)**
- 🎯 **[SCP Foundation Wiki](https://scp-wiki.wikidot.com/)**

---

## 📄 License

This project is licensed under a **Commercial License**. See [LICENSE](LICENSE) for details.

**© 2025 Dynamic Innovative Studio. All rights reserved.**
